import Store from "electron-store";

interface VoiceConfig {
  voiceEnabled: boolean;
  voiceAppId: string;
  voiceAccessKeyId: string;
}

interface ConfigResponse {
  success: boolean;
  message: string;
  data?: {
    models: string[];
    codeLanguages: string[];
    defaultLanguage: string;
    voiceConfig: VoiceConfig;
  };
  error?: string;
}

interface StoreSchema {
  openaiApiKey: string | null,
  curModel: string | null,
  curCodeLanguage: string | null,
  config: ConfigResponse | null
}

// 创建一个类型安全的Store实例
const store = new Store<StoreSchema>({
  defaults: {
    openaiApiKey: null,
    curModel: "deepseek-v3", // Default model
    curCodeLanguage: "Java", // Default code language
    config: null
  },

  encryptionKey: "your-encryption-key"
}) as Store<StoreSchema> & {
  get<K extends keyof StoreSchema>(key: K): StoreSchema[K];
  set<K extends keyof StoreSchema>(key: K, value: StoreSchema[K]): void;
};

export { ConfigResponse, VoiceConfig, store };

// 默认模型列表（作为后备）
const defaultModels = [
  "doubao-pro",
  "doubao-thinking",
  "deepseek-v3",
  "deepseek-r1",
  "gemini",
  "claude",
];

// 默认编程语言列表（作为后备）
const defaultCodeLanguages = [
  "Java",
  "Python",
  "Python3",
  "JavaScript",
  "C++",
  "C",
  "Go",
  "SQL",
  "Ruby",
  "TypeScript",
  "Kotlin",
  "Swift",
  "Rust"
];

/**
 * 获取模型列表，优先从配置中读取，否则使用默认列表
 * @returns 模型列表
 */
export function getModels(): string[] {
  try {
    const config = store.get('config');
    if (config && config.success && config.data && config.data.models) {
      console.log('从配置中读取模型列表:', config.data.models);
      return config.data.models;
    }
  } catch (error) {
    console.warn('读取配置中的模型列表失败:', error);
  }

  console.log('使用默认模型列表:', defaultModels);
  return defaultModels;
}

/**
 * 获取编程语言列表，优先从配置中读取，否则使用默认列表
 * @returns 编程语言列表
 */
export function getCodeLanguages(): string[] {
  try {
    const config = store.get('config');
    if (config && config.success && config.data && config.data.codeLanguages) {
      console.log('从配置中读取编程语言列表:', config.data.codeLanguages);
      return config.data.codeLanguages;
    }
  } catch (error) {
    console.warn('读取配置中的编程语言列表失败:', error);
  }

  console.log('使用默认编程语言列表:', defaultCodeLanguages);
  return defaultCodeLanguages;
}

/**
 * 获取默认编程语言，优先从配置中读取，否则使用默认值
 * @returns 默认编程语言
 */
export function getDefaultCodeLanguage(): string {
  try {
    const config = store.get('config');
    if (config && config.success && config.data && config.data.defaultLanguage) {
      console.log('从配置中读取默认编程语言:', config.data.defaultLanguage);
      return config.data.defaultLanguage;
    }
  } catch (error) {
    console.warn('读取配置中的默认编程语言失败:', error);
  }

  console.log('使用默认编程语言: Java');
  return "Java";
}

/**
 * 获取语音配置
 * @returns 语音配置对象，如果未配置则返回默认值
 */
export function getVoiceConfig(): VoiceConfig {
  try {
    const config = store.get('config');
    if (config && config.success && config.data && config.data.voiceConfig) {
      console.log('从配置中读取语音配置:', config.data.voiceConfig);
      return config.data.voiceConfig;
    }
  } catch (error) {
    console.warn('读取配置中的语音配置失败:', error);
  }

  // 默认语音配置
  const defaultVoiceConfig: VoiceConfig = {
    voiceEnabled: false, // 默认禁用语音功能
    voiceAppId: '',
    voiceAccessKeyId: ''
  };

  console.log('使用默认语音配置:', defaultVoiceConfig);
  return defaultVoiceConfig;
}

/**
 * 检查语音功能是否启用
 * @returns 语音功能是否启用
 */
export function isVoiceEnabled(): boolean {
  const voiceConfig = getVoiceConfig();
  const enabled = voiceConfig.voiceEnabled;
  return enabled;
}

/**
 * 检查麦克风按钮是否应该显示
 * @returns 麦克风按钮是否显示
 */
export function shouldShowMicrophoneButton(): boolean {
  return isVoiceEnabled();
}

// 保持向后兼容性
export const models = getModels();
