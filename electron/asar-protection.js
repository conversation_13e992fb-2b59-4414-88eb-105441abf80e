
/**
 * 运行时ASAR保护脚本
 * 在应用启动时验证ASAR文件完整性
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

function verifyAsarIntegrity() {
  try {
    const asarPath = path.join(__dirname, 'app.asar');
    const integrityPath = asarPath + '.integrity';
    
    if (!fs.existsSync(integrityPath)) {
      console.error('ASAR完整性文件缺失');
      return false;
    }
    
    const integrity = JSON.parse(fs.readFileSync(integrityPath, 'utf8'));
    const data = fs.readFileSync(asarPath);
    const hash = crypto.createHash('sha256').update(data).digest('hex');
    
    if (hash !== integrity.hash || data.length !== integrity.size) {
      console.error('ASAR文件完整性验证失败');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('ASAR完整性验证出错:', error.message);
    return false;
  }
}

// 导出验证函数
module.exports = { verifyAsarIntegrity };
