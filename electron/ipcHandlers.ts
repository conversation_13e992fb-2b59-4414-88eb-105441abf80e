import { ipcMain, screen } from "electron";
import { AppState } from "./main";
import { models, store, getCodeLanguages, getDefaultCodeLanguage } from "./store";

// Track which handlers have been registered
const registeredHandlers = new Set<string>();

/**
 * Safely register an IPC handler, removing any existing one first
 */
function safeRegisterHandler(channel: string, handler: (...args: any[]) => Promise<any> | any): void {
  if (registeredHandlers.has(channel)) {
    try {
      ipcMain.removeHandler(channel);
    } catch (error) {
      console.log(`Unable to remove existing handler for ${channel}:`, error);
    }
  }
  
  ipcMain.handle(channel, handler);
  registeredHandlers.add(channel);
}

export function initializeIpcHandlers(appState: AppState): void {
  const mainWindow = appState.getMainWindow();

  if (!mainWindow) {
    console.error("Main window is not available for IPC handlers");
    return;
  }

  // Clear any existing handlers
  for (const channel of registeredHandlers) {
    try {
      ipcMain.removeHandler(channel);
    } catch (error) {
      console.log(`Failed to remove existing handler for ${channel}:`, error);
    }
  }
  registeredHandlers.clear();

  // Window dimension update
  safeRegisterHandler(
    "update-content-dimensions",
    async (event, { width, height }: { width: number; height: number }) => {
      if (width && height) {
        appState.setWindowDimensions(width, height)
      }
    }
  );

  // Screenshot management
  safeRegisterHandler("delete-screenshot", async (event, path: string) => {
    return appState.deleteScreenshot(path)
  });

  // API key management
  safeRegisterHandler("get-api-key", async () => {
    return store.get("openaiApiKey")
  });

  // Model management
  safeRegisterHandler("get-current-model", async (event) => {
    const curModel = store.get("curModel") || models[0];
    const index = models.findIndex(model => model === curModel) + 1;

    if (event.sender) {
      event.sender.send("model-changed", {
        model: curModel,
        index,
        total: models.length
      });
    }

    return {
      model: curModel,
      index,
      total: models.length
    };
  });

  // Code language management
  safeRegisterHandler("get-current-code-language", async (event) => {
    const codeLanguages = getCodeLanguages();
    const curCodeLanguage = store.get("curCodeLanguage") || getDefaultCodeLanguage();
    const index = codeLanguages.findIndex(lang => lang === curCodeLanguage) + 1;

    if (event.sender) {
      event.sender.send("code-language-changed", {
        codeLanguage: curCodeLanguage,
        index,
        total: codeLanguages.length
      });
    }

    return {
      codeLanguage: curCodeLanguage,
      index,
      total: codeLanguages.length
    };
  });

  // Screenshot capture
  safeRegisterHandler("take-screenshot", async () => {
    try {
      const screenshotPath = await appState.takeScreenshot()
      const preview = await appState.getImagePreview(screenshotPath)
      return { path: screenshotPath, preview }
    } catch (error) {
      console.error("Error taking screenshot:", error)
      throw error
    }
  });

  safeRegisterHandler("take-full-screenshot", async () => {
    try {
      // Get primary display dimensions for full screen capture
      const primaryDisplay = screen.getPrimaryDisplay();
      const { width, height } = primaryDisplay.bounds;
      
      // Use the region screenshot method with full screen dimensions
      const screenshotPath = await appState.takeRegionScreenshot(0, 0, width, height);
      const preview = await appState.getImagePreview(screenshotPath);
      
      // Return the same data format as take-screenshot
      return { path: screenshotPath, preview };
    } catch (error) {
      console.error("Error taking full screenshot:", error);
      throw error;
    }
  });

  // Solution generation
  safeRegisterHandler("generate-solution", async () => {
    try {
      // Check if processing is already active
      if (appState.processingHelper.isProcessingActive()) {
        console.log("Already processing a request, ignoring solution generation");
        return { success: false, error: "Already processing a request" };
      }

      // Check if there are screenshots to process
      if (appState.getScreenshotQueue().length === 0) {
        console.log("No screenshots to process");
        // Notify the renderer that there are no screenshots to process
        const mainWindow = appState.getMainWindow();
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send("processing-no-screenshots"); 
        }
        return { success: false, error: "No screenshots to process" };
      }

      // Generate and process solutions using the chat model
      appState.processingHelper.generateAndProcessScreenshotSolutionsNew();
      return { success: true };
    } catch (error) {
      console.error("Error generating solution:", error);
      throw error;
    }
  });

  // Screenshot queue management
  safeRegisterHandler("get-screenshots", async () => {
    try {
      let previews = await Promise.all(
        appState.getScreenshotQueue().map(async (path) => ({
          path,
          preview: await appState.getImagePreview(path)
        }))
      )

      return previews
    } catch (error) {
      console.error("Error getting screenshots:", error)
      throw error
    }
  });

  // Window management
  safeRegisterHandler("toggle-window", async () => {
    appState.toggleMainWindow()
  });

  // Queue reset
  safeRegisterHandler("reset-queues", async () => {
    try {
      appState.clearQueues()

      return { success: true }
    } catch (error: any) {
      console.error("Error resetting queues:", error)
      return { success: false, error: error.message }
    }
  });

  // API key setting
  safeRegisterHandler("set-api-key", (_event, apiKey: string) => {
    try {
      store.set("openaiApiKey", apiKey)
      appState.setIgnoreMouseEvents(true)
      return { success: true }
    } catch (error) {
      console.error("Error setting API key:", error)
      return { success: false, error: "Failed to set API key" }
    }
  });

  // Toolbar management
  safeRegisterHandler(
    "update-toolbar-bounds",
    (_event, bounds: { x: number; y: number; width: number; height: number }) => {
      try {
        appState.mouseTrackingHelper.updateToolbarBounds(bounds);
        return { success: true };
      } catch (error) {
        console.error("更新工具条位置出错:", error);
        return { success: false, error: "Failed to update toolbar bounds" };
      }
    }
  );

  safeRegisterHandler(
    "update-toolbar-regions",
    (_event, regions: Array<{ x: number; y: number; width: number; height: number; type: 'model' | 'code-language' | 'reset' | 'capture' | 'solution' | 'other' | 'voice' }>) => {
      try {
        appState.mouseTrackingHelper.updateToolbarRegions(regions);
        return { success: true };
      } catch (error) {
        console.error("更新工具条区域出错:", error);
        return { success: false, error: "Failed to update toolbar regions" };
      }
    }
  );

  // AI回复框位置上报处理器
  safeRegisterHandler(
    "voice-ai-response-positions",
    (_event, positions: {
      fastResponse?: { x: number; y: number; width: number; height: number; type: string };
      accurateResponse?: { x: number; y: number; width: number; height: number; type: string };
    }) => {
      try {
        appState.mouseTrackingHelper.updateAIResponsePositions(positions);
        return { success: true };
      } catch (error) {
        console.error("更新AI回复框位置出错:", error);
        return { success: false, error: "Failed to update AI response positions" };
      }
    }
  );

  // AI回复框箭头按钮位置上报处理器
  safeRegisterHandler(
    "voice-ai-arrow-positions",
    (_event, positions: {
      [key: string]: { x: number; y: number; width: number; height: number; type: string };
    }) => {
      try {
        appState.mouseTrackingHelper.updateAIArrowPositions(positions);
        return { success: true };
      } catch (error) {
        console.error("更新AI箭头按钮位置出错:", error);
        return { success: false, error: "Failed to update AI arrow positions" };
      }
    }
  );

  safeRegisterHandler('system-audio:process-audio', async (_event, audioData: any) => {
    try {
      // SystemAudioManager处理音频数据是通过文件监控自动完成的
      // 这里只需要返回成功状态
      return { success: true };
    } catch (error: any) {
      console.error('Error processing system audio data:', error);
      return { success: false, error: error.message };
    }
  });


}
