/**
 * Utility functions for the Electron app
 */

/**
 * Generate a UUID for connection ID
 * @returns A UUID string
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Format a timestamp to a readable string
 * @param date Date object or ISO string
 * @returns Formatted time string
 */
export function formatTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
}

/**
 * Throttle a function call
 * @param func Function to throttle
 * @param limit Throttle limit in milliseconds
 * @returns Throttled function
 */
export function throttle<T extends (...args: any[]) => any>(func: T, limit: number): (...args: Parameters<T>) => void {
  let lastCall = 0;
  return function(...args: Parameters<T>): void {
    const now = Date.now();
    if (now - lastCall >= limit) {
      lastCall = now;
      func(...args);
    }
  };
}

/**
 * Debounce a function call
 * @param func Function to debounce
 * @param delay Debounce delay in milliseconds
 * @returns Debounced function
 */
export function debounce<T extends (...args: any[]) => any>(func: T, delay: number): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return function(...args: Parameters<T>): void {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), delay);
  };
}

/**
 * Convert Float32Array to Int16Array (PCM)
 * @param float32Array Float32Array audio data
 * @returns Int16Array audio data
 */
export function convertFloat32ToPCM16(float32Array: Float32Array): Int16Array {
  const pcm16 = new Int16Array(float32Array.length);
  
  for (let i = 0; i < float32Array.length; i++) {
    const s = Math.max(-1, Math.min(1, float32Array[i]));
    pcm16[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
  }
  
  return pcm16;
}

/**
 * Resample audio data to a different sample rate
 * @param audioData Audio data to resample
 * @param inputSampleRate Input sample rate
 * @param outputSampleRate Output sample rate
 * @returns Resampled audio data
 */
export function resampleAudio(audioData: Int16Array, inputSampleRate: number, outputSampleRate: number): Int16Array {
  if (inputSampleRate === outputSampleRate) {
    return audioData;
  }
  
  const ratio = inputSampleRate / outputSampleRate;
  const outputLength = Math.ceil(audioData.length / ratio);
  const result = new Int16Array(outputLength);
  
  for (let i = 0; i < outputLength; i++) {
    const inputIndex = Math.floor(i * ratio);
    result[i] = audioData[inputIndex];
  }
  
  return result;
}

/**
 * Prepare audio data for ASR service
 * @param audioData Audio data to prepare
 * @param sampleRate Sample rate of the audio data
 * @returns Prepared audio data object
 */
export function prepareAudioData(audioData: Int16Array, sampleRate: number): { audio_data: number[]; sample_rate: number; audio_format: string } {
  // Resample to 16kHz if needed
  const targetSampleRate = 16000;
  const resampledData = resampleAudio(audioData, sampleRate, targetSampleRate);
  
  return {
    audio_data: Array.from(resampledData),
    sample_rate: targetSampleRate,
    audio_format: 'pcm'
  };
} 