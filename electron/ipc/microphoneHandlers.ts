/**
 * microphoneHandlers.ts
 * 麦克风相关的IPC处理器
 */

import { BrowserWindow, ipcMain } from 'electron';
import { microphoneManager } from '../handlers/MicrophoneManager';

/**
 * 注册麦克风相关的IPC处理器
 * @param mainWindow - 主窗口实例
 */
export function registerMicrophoneHandlers(mainWindow: BrowserWindow): void {
  // 设置最大监听器数量，避免内存泄漏警告
  mainWindow.webContents.setMaxListeners(20);
  
  // 设置主窗口引用
  microphoneManager.setMainWindow(mainWindow);
  
  // 在注册新处理器之前，尝试移除现有的处理器
  try {
    ipcMain.removeHandler('microphone:start-capturing');
    console.log('移除已存在的microphone:start-capturing处理器');
  } catch (e) {
    // 忽略错误，如果处理器不存在
  }
  
  try {
    ipcMain.removeHandler('microphone:stop-capturing');
    console.log('移除已存在的microphone:stop-capturing处理器');
  } catch (e) {
    // 忽略错误，如果处理器不存在
  }
  
  try {
    ipcMain.removeHandler('microphone:get-status');
    console.log('移除已存在的microphone:get-status处理器');
  } catch (e) {
    // 忽略错误，如果处理器不存在
  }
  
  try {
    ipcMain.removeHandler('microphone:process-audio');
    console.log('移除已存在的microphone:process-audio处理器');
  } catch (e) {
    // 忽略错误，如果处理器不存在
  }
  
  // 启动麦克风音频捕获
  ipcMain.handle('microphone:start-capturing', async () => {
    console.log('IPC: 收到启动麦克风音频捕获请求');
    try {
      const result = await microphoneManager.startCapturing();
      return result;
    } catch (error) {
      console.error('启动麦克风音频捕获失败:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '启动麦克风音频捕获失败' 
      };
    }
  });
  
  // 停止麦克风音频捕获
  ipcMain.handle('microphone:stop-capturing', async () => {
    console.log('IPC: 收到停止麦克风音频捕获请求');
    try {
      await microphoneManager.stopCapturing();
      return { success: true };
    } catch (error) {
      console.error('停止麦克风音频捕获失败:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '停止麦克风音频捕获失败' 
      };
    }
  });
  
  // 获取麦克风音频捕获状态
  ipcMain.handle('microphone:get-status', () => {
    return { 
      recording: microphoneManager.isCapturing() 
    };
  });
  
  // 处理麦克风音频数据
  ipcMain.handle('microphone:process-audio', async (_event, audioData) => {
    // 如果麦克风未在录制状态，直接返回
    if (!microphoneManager.isCapturing()) {
      console.log('麦克风未在录制状态，忽略音频数据');
      return { success: false, error: '麦克风未在录制状态' };
    }
    
    try {
      await microphoneManager.processAudioData(audioData);
      return { success: true };
    } catch (error) {
      console.error('处理麦克风音频数据失败:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '处理麦克风音频数据失败' 
      };
    }
  });
  
  console.log('麦克风相关的IPC处理器已注册');
} 