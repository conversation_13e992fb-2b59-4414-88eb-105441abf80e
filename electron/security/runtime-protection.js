/**
 * 运行时保护模块
 * 提供应用运行时的安全检测和保护机制
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { app, dialog } = require('electron');

// 运行时保护配置
const RUNTIME_CONFIG = {
  // 检测间隔（毫秒）
  CHECK_INTERVAL: 10000,
  // 完整性检查间隔（毫秒）
  INTEGRITY_CHECK_INTERVAL: 30000,
  // 最大异常次数
  MAX_ANOMALY_COUNT: 5,
  // 是否启用严格模式
  STRICT_MODE: process.env.NODE_ENV === 'production',
  // 保护功能开关
  FEATURES: {
    INTEGRITY_CHECK: true,
    MEMORY_PROTECTION: true,
    PROCESS_MONITORING: true,
    NETWORK_MONITORING: true,
    FILE_SYSTEM_MONITORING: true
  }
};

// 全局状态
let protectionActive = false;
let anomalyCount = 0;
let baselineMetrics = null;
let checkIntervals = [];

/**
 * 初始化运行时保护
 */
function initRuntimeProtection() {
  if (protectionActive) {
    console.warn('运行时保护已经激活');
    return;
  }

  console.log('🛡️ 初始化运行时保护...');

  try {
    // 1. 建立基线指标
    establishBaseline();

    // 2. 启动完整性检查
    if (RUNTIME_CONFIG.FEATURES.INTEGRITY_CHECK) {
      startIntegrityMonitoring();
    }

    // 3. 启动内存保护
    if (RUNTIME_CONFIG.FEATURES.MEMORY_PROTECTION) {
      startMemoryProtection();
    }

    // 4. 启动进程监控
    if (RUNTIME_CONFIG.FEATURES.PROCESS_MONITORING) {
      startProcessMonitoring();
    }

    // 5. 启动网络监控
    if (RUNTIME_CONFIG.FEATURES.NETWORK_MONITORING) {
      startNetworkMonitoring();
    }

    // 6. 启动文件系统监控
    if (RUNTIME_CONFIG.FEATURES.FILE_SYSTEM_MONITORING) {
      startFileSystemMonitoring();
    }

    protectionActive = true;
    console.log('✅ 运行时保护已启动');
  } catch (error) {
    console.error('❌ 运行时保护初始化失败:', error.message);
  }
}

/**
 * 建立基线指标
 */
function establishBaseline() {
  console.log('📊 建立基线指标...');

  baselineMetrics = {
    timestamp: Date.now(),
    memory: {
      heapUsed: process.memoryUsage().heapUsed,
      heapTotal: process.memoryUsage().heapTotal,
      external: process.memoryUsage().external,
      rss: process.memoryUsage().rss
    },
    cpu: {
      userTime: process.cpuUsage().user,
      systemTime: process.cpuUsage().system
    },
    process: {
      pid: process.pid,
      ppid: process.ppid,
      platform: process.platform,
      arch: process.arch,
      version: process.version
    },
    app: {
      path: app.getAppPath(),
      version: app.getVersion(),
      name: app.getName()
    }
  };

  console.log('✅ 基线指标已建立');
}

/**
 * 启动完整性监控
 */
function startIntegrityMonitoring() {
  console.log('🔐 启动完整性监控...');

  const interval = setInterval(() => {
    try {
      checkApplicationIntegrity();
    } catch (error) {
      console.warn('⚠️ 完整性检查出错:', error.message);
    }
  }, RUNTIME_CONFIG.INTEGRITY_CHECK_INTERVAL);

  checkIntervals.push(interval);
}

/**
 * 检查应用完整性
 */
function checkApplicationIntegrity() {
  // 检查关键文件
  const criticalFiles = [
    path.join(app.getAppPath(), 'package.json'),
    path.join(app.getAppPath(), 'main.js'),
    path.join(app.getAppPath(), 'preload.js')
  ];

  for (const file of criticalFiles) {
    if (fs.existsSync(file)) {
      const stats = fs.statSync(file);
      const currentTime = Date.now();
      const fileAge = currentTime - stats.mtime.getTime();

      // 如果文件在运行时被修改，可能存在篡改
      if (fileAge < 60000) { // 1分钟内修改
        handleAnomaly('文件完整性', `关键文件被修改: ${file}`);
      }
    }
  }

  // 检查进程完整性
  if (process.pid !== baselineMetrics.process.pid) {
    handleAnomaly('进程完整性', 'PID发生变化');
  }
}

/**
 * 启动内存保护
 */
function startMemoryProtection() {
  console.log('🧠 启动内存保护...');

  const interval = setInterval(() => {
    try {
      checkMemoryAnomalies();
    } catch (error) {
      console.warn('⚠️ 内存检查出错:', error.message);
    }
  }, RUNTIME_CONFIG.CHECK_INTERVAL);

  checkIntervals.push(interval);
}

/**
 * 检查内存异常
 */
function checkMemoryAnomalies() {
  const currentMemory = process.memoryUsage();
  const baseline = baselineMetrics.memory;

  // 检查内存使用异常增长
  const heapGrowth = (currentMemory.heapUsed - baseline.heapUsed) / baseline.heapUsed;
  const rssGrowth = (currentMemory.rss - baseline.rss) / baseline.rss;

  if (heapGrowth > 5.0) { // 堆内存增长超过500%
    handleAnomaly('内存异常', `堆内存异常增长: ${(heapGrowth * 100).toFixed(1)}%`);
  }

  if (rssGrowth > 3.0) { // RSS内存增长超过300%
    handleAnomaly('内存异常', `RSS内存异常增长: ${(rssGrowth * 100).toFixed(1)}%`);
  }

  // 检查内存泄漏
  if (global.gc) {
    const beforeGC = process.memoryUsage().heapUsed;
    global.gc();
    const afterGC = process.memoryUsage().heapUsed;
    const leaked = beforeGC - afterGC;

    if (leaked > 50 * 1024 * 1024) { // 50MB
      handleAnomaly('内存泄漏', `检测到内存泄漏: ${(leaked / 1024 / 1024).toFixed(1)}MB`);
    }
  }
}

/**
 * 启动进程监控
 */
function startProcessMonitoring() {
  console.log('⚙️ 启动进程监控...');

  const interval = setInterval(() => {
    try {
      checkProcessAnomalies();
    } catch (error) {
      console.warn('⚠️ 进程检查出错:', error.message);
    }
  }, RUNTIME_CONFIG.CHECK_INTERVAL);

  checkIntervals.push(interval);
}

/**
 * 检查进程异常
 */
function checkProcessAnomalies() {
  const currentCpu = process.cpuUsage();
  const baseline = baselineMetrics.cpu;

  // 检查CPU使用异常
  const userTimeGrowth = (currentCpu.user - baseline.userTime) / 1000000; // 转换为秒
  const systemTimeGrowth = (currentCpu.system - baseline.systemTime) / 1000000;

  if (userTimeGrowth > 300) { // 用户时间超过5分钟
    handleAnomaly('CPU异常', `用户CPU时间异常: ${userTimeGrowth.toFixed(1)}秒`);
  }

  if (systemTimeGrowth > 60) { // 系统时间超过1分钟
    handleAnomaly('CPU异常', `系统CPU时间异常: ${systemTimeGrowth.toFixed(1)}秒`);
  }
}

/**
 * 启动网络监控
 */
function startNetworkMonitoring() {
  console.log('🌐 启动网络监控...');

  // 监控可疑的网络连接
  const originalFetch = global.fetch;
  if (originalFetch) {
    global.fetch = function(...args) {
      const url = args[0];
      if (typeof url === 'string') {
        // 检查可疑域名
        const suspiciousDomains = [
          'localhost:9229', // Node.js调试端口
          'localhost:9222', // Chrome DevTools
          'debug',
          'inspector'
        ];

        for (const domain of suspiciousDomains) {
          if (url.includes(domain)) {
            handleAnomaly('网络异常', `检测到可疑网络请求: ${url}`);
          }
        }
      }
      return originalFetch.apply(this, args);
    };
  }
}

/**
 * 启动文件系统监控
 */
function startFileSystemMonitoring() {
  console.log('📁 启动文件系统监控...');

  // 监控关键目录的文件变化
  const criticalDirs = [
    app.getAppPath(),
    path.join(app.getPath('userData'))
  ];

  for (const dir of criticalDirs) {
    if (fs.existsSync(dir)) {
      try {
        fs.watch(dir, { recursive: false }, (eventType, filename) => {
          if (filename && eventType === 'change') {
            handleAnomaly('文件系统异常', `关键目录文件变化: ${filename}`);
          }
        });
      } catch (error) {
        console.warn(`⚠️ 无法监控目录 ${dir}:`, error.message);
      }
    }
  }
}

/**
 * 处理异常情况
 */
function handleAnomaly(type, message) {
  anomalyCount++;
  console.warn(`⚠️ 检测到异常 [${type}]: ${message} (次数: ${anomalyCount})`);

  if (RUNTIME_CONFIG.STRICT_MODE && anomalyCount >= RUNTIME_CONFIG.MAX_ANOMALY_COUNT) {
    console.error('🚨 异常次数超过阈值，应用将退出');

    // 显示警告对话框
    dialog.showErrorBox(
      '安全警告',
      `检测到多次安全异常，应用将退出以保护安全。\n\n异常类型: ${type}\n详情: ${message}`
    );

    // 清理并退出
    stopRuntimeProtection();
    app.quit();
    process.exit(1);
  }
}

/**
 * 停止运行时保护
 */
function stopRuntimeProtection() {
  if (!protectionActive) {
    return;
  }

  console.log('🛡️ 停止运行时保护...');

  // 清理所有定时器
  for (const interval of checkIntervals) {
    clearInterval(interval);
  }
  checkIntervals = [];

  protectionActive = false;
  console.log('✅ 运行时保护已停止');
}

/**
 * 获取保护状态
 */
function getProtectionStatus() {
  return {
    active: protectionActive,
    anomalyCount,
    baseline: baselineMetrics ? {
      timestamp: new Date(baselineMetrics.timestamp).toISOString(),
      memory: baselineMetrics.memory,
      process: baselineMetrics.process
    } : null,
    config: RUNTIME_CONFIG
  };
}

module.exports = {
  initRuntimeProtection,
  stopRuntimeProtection,
  getProtectionStatus,
  RUNTIME_CONFIG
};
