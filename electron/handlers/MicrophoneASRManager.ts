/**
 * MicrophoneASRManager.ts
 * Dedicated ASR Manager for Microphone Audio
 * Handles WebSocket connections specifically for microphone audio processing
 */

import { BrowserWindow } from 'electron';
import { getVoiceConfig, isVoiceEnabled } from '../store';
import { generateUUID } from '../utils';
import { gzip, gunzip } from 'zlib';
import { promisify } from 'util';

// Import WebSocket with error handling
let WebSocket: any;
try {
  WebSocket = require('ws');
  console.log('WebSocket module loaded successfully for MicrophoneASR');
} catch (error) {
  console.error('Failed to load ws module for MicrophoneASR:', error);
  WebSocket = class MockWebSocket {
    constructor() {
      throw new Error('WebSocket module is not available');
    }
  };
}

// 协议常量 - 基于doubao-client.ts
enum ProtocolVersion {
  V1 = 0x01,
}

enum MessageType {
  CLIENT_FULL_REQUEST = 0x01,
  CLIENT_AUDIO_ONLY = 0x02,
  SERVER_FULL_RESPONSE = 0x09,
  SERVER_ERROR = 0x0f,
}

enum MessageTypeSpecificFlags {
  NO_SEQUENCE = 0x00,
  POS_SEQUENCE = 0x01,
  NEG_SEQUENCE = 0x02,
  NEG_WITH_SEQUENCE = 0x03,
}

enum SerializationType {
  NO_SERIALIZATION = 0x00,
  JSON = 0x01,
}

enum CompressionType {
  NO_COMPRESSION = 0x00,
  GZIP = 0x01,
}

export class MicrophoneASRManager {
  private wsConnection: any = null;
  private isConnected: boolean = false;
  private isProcessing: boolean = false;
  private mainWindow: BrowserWindow | null = null;
  private static instance: MicrophoneASRManager | null = null;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private messageQueue: Array<any> = [];
  private sessionStarted: boolean = false;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private lastActivityTimestamp: number = 0;
  private sequence: number = 1;

  // 添加gzip异步方法
  private gzipAsync = promisify(gzip);
  private gunzipAsync = promisify(gunzip);
  private readyToSendAudio: boolean = false;
  private isConnecting: boolean = false;
  private config: any = null; // 存储语音配置信息
  private currentConnectionId: string = '';
  private isScheduledForClosure: boolean = false;
  private lastConnectionTime: number = 0;

  // 静音检测相关属性
  private silenceThreshold: number = 0.01; // 静音阈值，可调节
  private minSilenceDuration: number = 500; // 最小静音持续时间(ms)
  private lastSoundTime: number = 0; // 上次检测到声音的时间
  private consecutiveSilenceCount: number = 0; // 连续静音帧计数

  // 采样率管理
  private actualSampleRate: number = 44100; // 默认采样率，会被实际值覆盖
  private configSent: boolean = false; // 标记是否已发送配置
  private connectionAttemptBlocked: boolean = false;
  private connectionBlockTimeout: NodeJS.Timeout | null = null;
  private readonly MIN_CONNECTION_INTERVAL = 30000; // 30秒
  private autoCloseTimer: NodeJS.Timeout | null = null;
  private lastPongTime: number = 0;
  private pingInterval: NodeJS.Timeout | null = null;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private lastAudioDataTime: number = 0;
  private audioTimeoutCheckTimer: NodeJS.Timeout | null = null;
  private readonly AUDIO_TIMEOUT_MS = 10000; // 10秒无音频数据则自动关闭

  private static lastSendTime: number = 0;

  // 音频缓冲相关
  private audioBuffer: Buffer[] = [];
  private audioBufferSize = 0;
  private readonly MAX_BUFFER_SIZE = 8192; // 最大缓冲区大小（字节）
  private readonly MIN_BUFFER_SIZE = 2048; // 最小缓冲区大小（字节）

  private static lastTranscriptions: {
    finalTexts: Set<string>;
    lastInterimText: string;
    lastFinalText: string;
    lastTimestamp: number;
    lastHistoryUpdateTime: number;
    history: Array<{
      text: string;
      source: 'microphone';
      timestamp: string;
      isFinal: boolean;
      paragraphId?: string;
    }>;
  } = {
    finalTexts: new Set<string>(),
    lastInterimText: '',
    lastFinalText: '',
    lastTimestamp: 0,
    lastHistoryUpdateTime: 0,
    history: []
  };

  constructor() {
    console.log('MicrophoneASRManager instance created');
    // this.loadConfig();
    this.startAudioTimeoutCheck();

    // 初始化静音检测状态
    this.resetSilenceDetection();
    console.log(`MicrophoneASR: 静音检测已启用 - 阈值=${this.silenceThreshold}, 最小持续时间=${this.minSilenceDuration}ms`);
  }

  /**
   * 设置主窗口引用
   */
  public setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
  }

  /**
   * 从 store 中加载语音配置
   */
  private loadConfig(): void {
    try {
      this.config = getVoiceConfig();
      console.log('MicrophoneASRManager: 语音配置加载成功', this.config);
    } catch (error) {
      console.error('MicrophoneASRManager: 加载语音配置失败', error);
      this.config = null;
    }
  }

  /**
   * 检查麦克风音频识别功能是否启用
   */
  public isMicrophoneASREnabled(): boolean {
    const enabled = isVoiceEnabled();
    console.log('MicrophoneASRManager: 麦克风音频识别启用状态:', enabled);
    return enabled;
  }

  /**
   * 获取语音配置中的 AppId
   */
  public getVoiceAppId(): string {
    return this.config?.voiceAppId || '';
  }

  /**
   * 获取语音配置中的 AccessKeyId
   */
  public getVoiceAccessKeyId(): string {
    return this.config?.voiceAccessKeyId || '';
  }

  public static getInstance(): MicrophoneASRManager {
    if (!MicrophoneASRManager.instance) {
      MicrophoneASRManager.instance = new MicrophoneASRManager();
    }
    return MicrophoneASRManager.instance;
  }

  public async startASRSession(): Promise<{ success: boolean, error?: string }> {
    console.log('🎤 Starting MicrophoneASR session for microphone audio');

    if (!this.config) {
      this.loadConfig();
    }

    // 检查语音功能是否启用
    if (!this.isMicrophoneASREnabled()) {
      console.log('🚫 MicrophoneASR: 语音功能已禁用');
      return { success: false, error: '语音功能已禁用，请检查配置' };
    }

    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
      console.log('⏰ MicrophoneASR: 自动关闭计时器已取消');
    }

    if (this.isConnecting) {
      console.log('⏳ MicrophoneASR: Already attempting to connect');
      return { success: false, error: 'Connection already in progress' };
    }

    if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN && this.isConnected) {
      console.log('✅ MicrophoneASR: Active WebSocket connection already exists');
      return { success: true };
    }

    const now = Date.now();
    if (now - this.lastConnectionTime < this.MIN_CONNECTION_INTERVAL && this.connectionAttemptBlocked) {
      const remainingTime = Math.ceil((this.MIN_CONNECTION_INTERVAL - (now - this.lastConnectionTime)) / 1000);
      return { success: false, error: `Please wait ${remainingTime}s before reconnecting` };
    }

    if (this.wsConnection) {
      console.log('MicrophoneASR: Closing existing connection');
      try {
        this.isConnected = false;
        this.sessionStarted = false;
        
        if (this.wsConnection.readyState === WebSocket.OPEN) {
          this.wsConnection.close(1000, 'Closing before new connection');
        } else {
          this.wsConnection.terminate();
        }
      } catch (err) {
        console.error('MicrophoneASR: Error closing existing connection:', err);
      }
      
      this.wsConnection = null;
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    this.lastConnectionTime = now;
    this.messageQueue = [];
    this.reconnectAttempts = 0;
    this.sessionStarted = false;
    this.lastActivityTimestamp = Date.now();
    this.lastPongTime = Date.now();
    this.currentConnectionId = generateUUID();
    
    this.clearIntervals();
    
    try {
      await this.connectToASRService();
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown connection error' 
      };
    }
  }

  private clearIntervals(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }

    // 重置配置状态
    this.configSent = false;
  }

  private async connectToASRService(): Promise<void> {
    this.isConnecting = true;
    const connectionId = this.currentConnectionId;
    
    return new Promise((resolve, reject) => {
      try {
        console.log(`MicrophoneASR: Starting connection attempt (ID: ${connectionId})`);
        
        const requestId = generateUUID();
        // 使用bigmodel_async端点
        const targetUrl = 'wss://openspeech.bytedance.com/api/v3/sauc/bigmodel_async';

        const options = {
          headers: {
            'X-Api-App-Key': this.getVoiceAppId(),
            'X-Api-Access-Key': this.getVoiceAccessKeyId(),
            'X-Api-Resource-Id': 'volc.bigasr.sauc.duration',
            'X-Api-Request-Id': requestId
          },
          timeout: 10000
        };
        
        this.wsConnection = new WebSocket(targetUrl, options);
        this.wsConnection.binaryType = 'arraybuffer';
        
        const connectionTimeout = setTimeout(() => {
          if (connectionId !== this.currentConnectionId) return;
          
          if (!this.isConnected) {
            console.error('MicrophoneASR: Connection timeout');
            if (this.wsConnection) {
              this.wsConnection.terminate();
              this.wsConnection = null;
            }
            
            if (this.reconnectAttempts < this.maxReconnectAttempts) {
              this.reconnectAttempts++;
              console.log(`MicrophoneASR: Reconnection attempt ${this.reconnectAttempts} of ${this.maxReconnectAttempts}`);
              setTimeout(() => {
                if (connectionId === this.currentConnectionId) {
                  this.connectToASRService().then(resolve).catch(reject);
                }
              }, 3000);
            } else {
              this.isConnecting = false;
              reject(new Error('Connection timeout after multiple attempts'));
            }
          }
        }, 15000);
        
        this.wsConnection.on('open', () => {
          if (connectionId !== this.currentConnectionId) {
            clearTimeout(connectionTimeout);
            try {
              this.wsConnection.close(1000, "Superseded by newer connection");
            } catch (e) {
              console.error("MicrophoneASR: Error closing superseded connection:", e);
            }
            return;
          }
          
          clearTimeout(connectionTimeout);
          console.log('MicrophoneASR: WebSocket connection established');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.lastActivityTimestamp = Date.now();
          this.isConnecting = false;
          
          // 与SystemASRManager一致：连接建立后立即发送配置
          this.sendInitMessage();
          this.processQueuedMessages();
          this.startHeartbeat();
          
          if (this.mainWindow && !this.mainWindow.isDestroyed()) {
            this.mainWindow.webContents.send('asr:connection-status', { connected: true });
          }
          
          resolve();
        });
        
        this.wsConnection.on('message', (message: any) => {
          if (connectionId !== this.currentConnectionId) return;
          this.handleWebSocketMessage(message);
        });
        
        this.wsConnection.on('error', (error: any) => {
          if (connectionId !== this.currentConnectionId) {
            clearTimeout(connectionTimeout);
            return;
          }
          
          clearTimeout(connectionTimeout);
          console.error('MicrophoneASR: WebSocket error:', error);

          this.isConnected = false;
          this.readyToSendAudio = false;
          this.sessionStarted = false;
          this.clearIntervals();

          // 清空音频缓冲区避免数据积压
          this.audioBuffer = [];
          this.audioBufferSize = 0;

          if (this.mainWindow && !this.mainWindow.isDestroyed()) {
            this.mainWindow.webContents.send('asr:error', {
              error: true,
              message: `MicrophoneASR service error: ${error.message || 'Unknown error'}`,
              code: error.code || 1011,
              reconnectAttempts: this.reconnectAttempts,
              maxReconnectAttempts: this.maxReconnectAttempts
            });
          }

          // 智能重连策略：指数退避算法
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const backoffDelay = Math.min(1000 * Math.pow(2, this.reconnectAttempts - 1), 30000); // 最大30秒
            console.log(`MicrophoneASR: 将在${backoffDelay}ms后尝试第${this.reconnectAttempts}次重连`);

            setTimeout(() => {
              if (connectionId === this.currentConnectionId) {
                this.connectToASRService().then(resolve).catch(reject);
              }
            }, backoffDelay);
          } else {
            this.isConnecting = false;
            console.error('MicrophoneASR: 已达到最大重连次数，停止重连');
            reject(error);
          }
        });
        
        this.wsConnection.on('close', (code: number, reason: string) => {
          if (connectionId !== this.currentConnectionId) {
            clearTimeout(connectionTimeout);
            return;
          }
          
          clearTimeout(connectionTimeout);
          console.log(`MicrophoneASR: WebSocket closed with code ${code}: ${reason || 'No reason provided'}`);
          
          this.isConnected = false;
          this.sessionStarted = false;
          this.clearIntervals();
          
          if (this.mainWindow && !this.mainWindow.isDestroyed()) {
            this.mainWindow.webContents.send('asr:connection-status', { 
              connected: false,
              code,
              reason
            });
          }
          
          if (code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            setTimeout(() => {
              if (connectionId === this.currentConnectionId) {
                this.connectToASRService().then(resolve).catch(reject);
              }
            }, 5000);
          } else {
            this.isConnecting = false;
          }
        });
      } catch (error) {
        console.error('MicrophoneASR: Failed to create WebSocket connection:', error);
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  private async sendInitMessage(): Promise<void> {
    if (!this.wsConnection || this.wsConnection.readyState !== WebSocket.OPEN) {
      console.error('MicrophoneASR: Cannot send init message: WebSocket not open');
      return;
    }

    try {
      // 与SystemASRManager一致：固定使用16000Hz配置
      console.log(`MicrophoneASR: 使用固定采样率 16000Hz 进行配置`);

      // 使用与SystemASRManager完全一致的配置格式
      const requestConfig = {
        user: { uid: "microphone-audio" },
        audio: {
          format: "pcm",
          codec: "raw",
          rate: 16000, // 固定使用16000Hz，与SystemASRManager一致
          bits: 16,
          channel: 1,
        },
        request: {
          model_name: "bigmodel",
          enable_itn: true,
          enable_punc: true,
          enable_ddc: true,
          show_utterances: false, // 不需要 utterances，只使用 result.text
          enable_nonstream: false,
          result_type: 'single'
        },
      };
      
      console.log('MicrophoneASR: 发送初始化消息 (与websocket-record-rtc.js一致)');

      // 使用与websocket-record-rtc.js完全一致的消息格式
      const message = this.encodeFullClientRequest(requestConfig);

      console.log(`MicrophoneASR: 发送配置消息，消息大小=${message.size}字节`);

      this.wsConnection.send(message);
      this.readyToSendAudio = false;
      this.lastActivityTimestamp = Date.now();
    } catch (error) {
      console.error('MicrophoneASR: 发送初始化消息时出错:', error);
    }
  }

  private processQueuedMessages(): void {
    if (this.messageQueue.length > 0) {
      console.log(`MicrophoneASR: Sending ${this.messageQueue.length} queued messages`);
      this.messageQueue.forEach(msg => {
        if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
          this.wsConnection.send(msg);
        }
      });
      this.messageQueue = [];
    }
  }

  private startHeartbeat(): void {
    this.clearIntervals();
    
    this.pingInterval = setInterval(() => {
      if (this.isScheduledForClosure) {
        this.clearIntervals();
        return;
      }
      
      if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
        try {
          this.wsConnection.ping();
          
          const now = Date.now();
          if (now - this.lastPongTime > 30000) {
            console.warn('MicrophoneASR: No pong response for 30 seconds');
            this.handleConnectionFailure();
          }
        } catch (error) {
          console.error('MicrophoneASR: Error sending ping:', error);
          this.handleConnectionFailure();
        }
      }
    }, 15000);
    
    if (this.wsConnection) {
      this.wsConnection.on('pong', () => {
        this.lastPongTime = Date.now();
      });
    }
    
    this.heartbeatInterval = setInterval(() => {
      if (this.isScheduledForClosure) {
        this.clearIntervals();
        return;
      }
      
      if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
        const currentTime = Date.now();
        if (currentTime - this.lastActivityTimestamp > 15000) {
          console.log('MicrophoneASR: Sending heartbeat');
          
          try {
            this.wsConnection.send(JSON.stringify({ type: 'heartbeat' }));
            this.lastActivityTimestamp = currentTime;
          } catch (error) {
            console.error('MicrophoneASR: Error sending heartbeat:', error);
            this.handleConnectionFailure();
          }
        }
      } else {
        this.clearIntervals();
      }
    }, 10000);
  }

  public async processAudioData(audioData: any): Promise<void> {
    this.lastAudioDataTime = Date.now();

    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
    }

    this.lastActivityTimestamp = Date.now();

    const audioDataLength = audioData.audio_data ? audioData.audio_data.byteLength : 0;
    const sampleRate = audioData.sample_rate || 44100;

    // 静音检测 - 在处理音频数据前检查是否为静音
    if (audioData.audio_data && audioData.audio_data.byteLength > 0) {
      const audioBytes = new Uint8Array(audioData.audio_data);
      const isSilent = this.detectSilence(audioBytes);

      if (isSilent) {
        this.consecutiveSilenceCount++;
        console.log(`🔇 MicrophoneASR: 检测到静音 (连续${this.consecutiveSilenceCount}帧)，跳过发送`);
        return; // 跳过静音数据的发送
      } else {
        // 检测到声音，重置静音计数
        if (this.consecutiveSilenceCount > 0) {
          console.log(`🔊 MicrophoneASR: 检测到声音，结束静音状态 (之前连续${this.consecutiveSilenceCount}帧静音)`);
        }
        this.consecutiveSilenceCount = 0;
        this.lastSoundTime = Date.now();
      }
    }

    console.log(`🎵 MicrophoneASR: ${Date.now()} 处理麦克风音频数据，长度=${audioDataLength}字节, 采样率=${sampleRate}Hz`);

    if (!this.wsConnection) {
      console.error('❌ MicrophoneASR: WebSocket连接未初始化');
      throw new Error('MicrophoneASR WebSocket connection not initialized');
    }

    console.log('🔗 MicrophoneASR: WebSocket状态:', {
      readyState: this.wsConnection.readyState,
      isConnected: this.isConnected,
      sessionStarted: this.sessionStarted,
      readyToSendAudio: this.readyToSendAudio
    });

    if (!this.sessionStarted && this.isConnected) {
      console.log(`⏳ MicrophoneASR: 会话尚未开始，将音频数据加入队列`);
      this.messageQueue.push(audioData);
      return;
    }
    
    const now = Date.now();
    const MIN_SEND_INTERVAL = 100; // 优化：减少发送间隔提高响应速度

    if (!MicrophoneASRManager.lastSendTime) {
      MicrophoneASRManager.lastSendTime = 0;
    }

    // 使用缓冲策略而不是简单跳过
    if (now - MicrophoneASRManager.lastSendTime < MIN_SEND_INTERVAL) {
      this.bufferAudioData(audioData);
      return;
    }

    // 检查是否有缓冲的数据需要先发送
    if (this.audioBufferSize > 0) {
      this.flushAudioBuffer(sampleRate);
    }
    
    if (this.wsConnection.readyState === WebSocket.OPEN && this.readyToSendAudio) {
      try {
        this.sequence++;
        MicrophoneASRManager.lastSendTime = now;
        
        let audioDataBytes = new Uint8Array(audioData.audio_data);

        if (!audioDataBytes || audioDataBytes.length === 0) {
          console.warn(`MicrophoneASR: 收到空的音频数据，跳过处理`);
          return;
        }

        console.log(`MicrophoneASR: 原始音频数据 - 长度=${audioDataBytes.length}字节, 采样率=${sampleRate}Hz, 前8字节=${Array.from(audioDataBytes.slice(0, 8)).map(b => b.toString(16).padStart(2, '0')).join(' ')}`);

        // 验证音频数据格式
        if (audioDataBytes.length % 2 !== 0) {
          console.warn(`MicrophoneASR: 音频数据长度不是偶数，调整长度`);
          audioDataBytes = audioDataBytes.slice(0, audioDataBytes.length - 1);
        }

        // 使用与SystemASRManager一致的简单静音检查
        const nonZeroSamples = Array.from(audioDataBytes).filter(value => value !== 0).length;
        const percentNonZero = (nonZeroSamples / audioDataBytes.length) * 100;

        console.log(`MicrophoneASR: 音频数据分析 - 非零字节: ${nonZeroSamples}/${audioDataBytes.length} (${percentNonZero.toFixed(2)}%)`);

        // 调整静音阈值，允许更多低音量数据通过
        if (percentNonZero < 0.05) {
          console.log('MicrophoneASR: 检测到几乎完全静音数据，跳过发送');
          return;
        }

        let processedAudioData = audioDataBytes;

        // 与SystemASRManager一致：如果采样率不是16000Hz，进行转换
        if (sampleRate !== 16000) {
          console.log(`MicrophoneASR: 转换采样率 ${sampleRate}Hz -> 16000Hz`);
          processedAudioData = this.convertSampleRate(audioDataBytes, sampleRate, 16000);
          console.log(`MicrophoneASR: 采样率转换完成，新长度=${processedAudioData.length}字节`);
        } else {
          console.log(`MicrophoneASR: 采样率已是16000Hz，无需转换`);
        }

        // 确保数据长度为偶数（16位PCM要求）
        if (processedAudioData.length % 2 !== 0) {
          processedAudioData = processedAudioData.slice(0, processedAudioData.length - 1);
          console.log(`MicrophoneASR: 调整音频数据长度为偶数: ${processedAudioData.length}字节`);
        }
        
        // websocket-record-rtc.js不使用压缩，直接发送原始PCM数据
        
        // 使用与websocket-record-rtc.js一致的音频消息格式
        const message = this.encodeAudioOnlyRequest(processedAudioData.buffer as ArrayBuffer);

        // 验证最终音频数据
        this.validateAudioData(processedAudioData);

        console.log(`MicrophoneASR: 发送音频数据，总大小=${message.size}字节, 原始长度=${audioDataLength}字节, 处理后长度=${processedAudioData.length}字节`);
        this.wsConnection.send(message);
        
        this.lastActivityTimestamp = Date.now();
        this.isProcessing = true;
        
      } catch (error) {
        console.error(`MicrophoneASR: 发送音频数据出错:`, error);
        throw error;
      }
    } else if (this.wsConnection.readyState === WebSocket.OPEN && !this.readyToSendAudio) {
      console.warn('MicrophoneASR: WebSocket已连接但尚未准备好发送音频，加入队列');
      this.messageQueue.push(audioData);
    } else {
      console.warn(`MicrophoneASR: WebSocket未连接，音频数据加入队列`);
      this.messageQueue.push(audioData);
    }
  }

  // Helper methods - 使用与SystemASRManager完全一致的采样率转换
  private convertSampleRate(audioData: Uint8Array, sourceSampleRate: number, targetSampleRate: number): Uint8Array {
    if (sourceSampleRate === targetSampleRate) {
      return audioData;
    }

    const dataLength = audioData.length % 2 === 0 ? audioData.length : audioData.length - 1;
    const samples = new Int16Array(dataLength / 2);

    for (let i = 0; i < samples.length; i++) {
      samples[i] = (audioData[i * 2] & 0xff) | ((audioData[i * 2 + 1] & 0xff) << 8);
    }

    let newSampleCount: number;

    if (sourceSampleRate > targetSampleRate) {
      const ratio = sourceSampleRate / targetSampleRate;
      newSampleCount = Math.floor(samples.length / ratio);
    } else {
      const ratio = targetSampleRate / sourceSampleRate;
      newSampleCount = Math.floor(samples.length * ratio);
    }

    const newSamples = new Int16Array(newSampleCount);

    if (sourceSampleRate > targetSampleRate) {
      const ratio = sourceSampleRate / targetSampleRate;
      for (let i = 0; i < newSampleCount; i++) {
        const srcIndex = i * ratio;
        const srcIndexInt = Math.floor(srcIndex);
        const srcIndexFrac = srcIndex - srcIndexInt;

        if (srcIndexInt + 1 < samples.length) {
          newSamples[i] = Math.round(samples[srcIndexInt] * (1 - srcIndexFrac) + samples[srcIndexInt + 1] * srcIndexFrac);
        } else {
          newSamples[i] = samples[srcIndexInt];
        }
      }
    } else {
      const ratio = targetSampleRate / sourceSampleRate;
      for (let i = 0; i < newSampleCount; i++) {
        const srcIndex = i / ratio;
        const srcIndexInt = Math.floor(srcIndex);
        const srcIndexFrac = srcIndex - srcIndexInt;

        if (srcIndexInt + 1 < samples.length) {
          newSamples[i] = Math.round(samples[srcIndexInt] * (1 - srcIndexFrac) + samples[srcIndexInt + 1] * srcIndexFrac);
        } else {
          newSamples[i] = samples[srcIndexInt < samples.length ? srcIndexInt : samples.length - 1];
        }
      }
    }

    const newAudioData = new Uint8Array(newSamples.length * 2);
    for (let i = 0; i < newSamples.length; i++) {
      newAudioData[i * 2] = newSamples[i] & 0xff;
      newAudioData[i * 2 + 1] = (newSamples[i] >> 8) & 0xff;
    }

    console.log(`MicrophoneASR: 采样率转换 ${sourceSampleRate}Hz -> ${targetSampleRate}Hz, 样本数 ${samples.length} -> ${newSamples.length}`);
    return newAudioData;
  }

  /**
   * 改进的音频质量分析方法
   * 综合考虑RMS、动态范围、信噪比等多个指标
   */
  private analyzeAudioQuality(audioData: Uint8Array): {
    rms: number;
    dynamicRange: number;
    snrEstimate: number;
    hasValidSignal: boolean;
    signalStrength: 'weak' | 'normal' | 'strong';
  } {
    // 转换为16位有符号样本进行分析，使用小端序
    const samples = new Int16Array(audioData.length / 2);
    for (let i = 0; i < samples.length; i++) {
      // 小端序：低字节在前，高字节在后
      const lowByte = audioData[i * 2] & 0xff;
      const highByte = audioData[i * 2 + 1] & 0xff;
      let sample = lowByte | (highByte << 8);

      // 转换为有符号16位整数
      if (sample > 32767) {
        sample -= 65536;
      }
      samples[i] = sample;
    }

    // 计算RMS值
    let sumSquares = 0;
    let max = -32768;
    let min = 32767;
    let nonZeroCount = 0;

    for (let i = 0; i < samples.length; i++) {
      const sample = samples[i];
      sumSquares += sample * sample;
      max = Math.max(max, Math.abs(sample));
      min = Math.min(min, Math.abs(sample));
      if (sample !== 0) nonZeroCount++;
    }

    const rms = Math.sqrt(sumSquares / samples.length) / 32768; // 归一化到0-1
    const dynamicRange = max > 0 ? 20 * Math.log10(max / Math.max(min, 1)) : 0;

    // 估算信噪比：使用最小值作为噪声基线
    const noiseFloor = min;
    const signalPeak = max;
    const snrEstimate = signalPeak > noiseFloor ? 20 * Math.log10(signalPeak / Math.max(noiseFloor, 1)) : 0;

    // 非零样本比例
    const nonZeroRatio = nonZeroCount / samples.length;

    // 综合判断是否为有效信号
    const hasValidSignal = (
      rms > 0.001 &&                    // RMS阈值降低，更敏感
      nonZeroRatio > 0.01 &&            // 至少1%的非零样本
      dynamicRange > 6 &&               // 至少6dB的动态范围
      snrEstimate > 3                   // 至少3dB的信噪比
    );

    // 信号强度分级
    let signalStrength: 'weak' | 'normal' | 'strong';
    if (rms < 0.01) {
      signalStrength = 'weak';
    } else if (rms < 0.1) {
      signalStrength = 'normal';
    } else {
      signalStrength = 'strong';
    }

    return {
      rms,
      dynamicRange,
      snrEstimate,
      hasValidSignal,
      signalStrength
    };
  }

  /**
   * 缓存音频数据进行批量处理
   */
  private bufferAudioData(audioData: any): void {
    if (!audioData.audio_data || audioData.audio_data.length === 0) {
      return;
    }

    const audioBuffer = Buffer.from(audioData.audio_data);
    this.audioBuffer.push(audioBuffer);
    this.audioBufferSize += audioBuffer.length;

    // 当缓冲区达到最小大小或超过最大大小时，立即处理
    if (this.audioBufferSize >= this.MIN_BUFFER_SIZE || this.audioBufferSize >= this.MAX_BUFFER_SIZE) {
      this.flushAudioBuffer(audioData.sample_rate || 16000);
    }
  }

  /**
   * 清空音频缓冲区并发送数据
   */
  private flushAudioBuffer(sampleRate: number = 16000): void {
    if (this.audioBuffer.length === 0 || this.audioBufferSize === 0) {
      return;
    }

    // 合并所有缓冲的音频数据
    const combinedBuffer = Buffer.concat(this.audioBuffer, this.audioBufferSize);

    // 创建合并后的音频数据对象
    const combinedAudioData = {
      audio_data: new Uint8Array(combinedBuffer),
      sample_rate: sampleRate,
      audio_format: 'pcm'
    };

    // 重置缓冲区
    this.audioBuffer = [];
    this.audioBufferSize = 0;

    // 发送合并后的数据
    console.log(`MicrophoneASR: 发送缓冲的音频数据，大小=${combinedBuffer.length}字节`);
    this.processCombinedAudioData(combinedAudioData).catch(error => {
      console.error('MicrophoneASR: 发送缓冲音频数据失败:', error);
    });
  }

  /**
   * 处理合并后的音频数据
   */
  private async processCombinedAudioData(audioData: any): Promise<void> {
    const sampleRate = audioData.sample_rate || 16000;
    const audioDataBytes = new Uint8Array(audioData.audio_data);

    // 进行音频质量分析
    const audioQuality = this.analyzeAudioQuality(audioDataBytes);

    if (!audioQuality.hasValidSignal) {
      console.log('MicrophoneASR: 合并音频质量不足，跳过发送');
      return;
    }

    // 直接发送到ASR服务
    await this.sendAudioDataToASR(audioDataBytes, sampleRate);
  }

  /**
   * 发送音频数据到ASR服务的核心方法
   */
  private async sendAudioDataToASR(audioDataBytes: Uint8Array, sampleRate: number): Promise<void> {
    // 🔍 数据验证：确保音频数据有效
    if (!audioDataBytes || audioDataBytes.length === 0) {
      console.warn('MicrophoneASR: 音频数据为空，跳过发送');
      return;
    }

    if (audioDataBytes.length < 320) { // 至少10ms的16kHz音频数据
      console.warn(`MicrophoneASR: 音频数据过短(${audioDataBytes.length}字节)，跳过发送`);
      return;
    }

    let processedAudioData = audioDataBytes;

    if (sampleRate !== 16000) {
      console.log(`MicrophoneASR: 转换采样率 ${sampleRate}Hz -> 16000Hz`);
      processedAudioData = this.convertSampleRate(audioDataBytes, sampleRate, 16000);
    }

    if (processedAudioData.length % 2 !== 0) {
      processedAudioData = processedAudioData.slice(0, processedAudioData.length - 1);
      console.log('MicrophoneASR: 调整音频数据长度为偶数');
    }

    // 🔍 验证音频数据格式
    console.log(`MicrophoneASR: 准备发送音频数据 - 长度: ${processedAudioData.length}字节, 采样率: 16000Hz, 格式: PCM16`);

    // 检查音频数据的前几个字节，确保不是JSON或其他文本格式
    const firstBytes = Array.from(processedAudioData.slice(0, Math.min(8, processedAudioData.length)));
    const isTextData = firstBytes.every(byte => byte >= 32 && byte <= 126); // ASCII可打印字符范围

    if (isTextData && processedAudioData.length > 8) {
      console.error('MicrophoneASR: 检测到文本数据而非音频数据，停止发送');
      console.error('MicrophoneASR: 数据前8字节:', firstBytes.map(b => String.fromCharCode(b)).join(''));
      return;
    }

    // websocket-record-rtc.js不使用压缩，直接发送原始PCM数据
    const audioDataLength = processedAudioData.length;

    MicrophoneASRManager.lastSendTime = Date.now();

    // 使用与websocket-record-rtc.js一致的音频消息格式
    const message = this.encodeAudioOnlyRequest(processedAudioData.buffer as ArrayBuffer);

    console.log(`MicrophoneASR: 发送音频数据，总大小=${message.size}字节, 原始长度=${audioDataLength}字节`);

    this.wsConnection.send(message);

    this.lastActivityTimestamp = Date.now();
    this.isProcessing = true;
  }

  // 与websocket-record-rtc.js完全一致的消息编码方法
  private encodeFullClientRequest(requestData: any): Blob {
    const fullClientRequestHeader = this.generateHeader();
    const json = JSON.stringify(requestData);
    fullClientRequestHeader.setUint32(4, json.length, false);
    return new Blob([fullClientRequestHeader, json]);
  }

  private encodeAudioOnlyRequest(requestData: ArrayBuffer): Blob {
    const audioOnlyRequestHeader = this.generateHeader(0b0010); // CLIENT_AUDIO_ONLY_REQUEST
    audioOnlyRequestHeader.setUint32(4, requestData.byteLength, false);
    return new Blob([audioOnlyRequestHeader, requestData]);
  }

  private generateHeader(
    messageType: number = 0b0001, // CLIENT_FULL_REQUEST
    version: number = 0b0001, // PROTOCOL_VERSION
    messageTypeSpecificFlags: number = 0b0000, // NO_SEQUENCE
    serialMethod: number = 0b0001, // JSON
    compressionType: number = 0b0000, // NO_COMPRESSION
    reservedData: number = 0x00,
    extensionHeader: ArrayBuffer = new ArrayBuffer(0)
  ): DataView {
    const buffer = new ArrayBuffer(8);
    const dataView = new DataView(buffer);
    const headerSize = Math.trunc(extensionHeader.byteLength / 4) + 1;

    dataView.setUint8(0, (version << 4) | headerSize);
    dataView.setUint8(1, (messageType << 4) | messageTypeSpecificFlags);
    dataView.setUint8(2, (serialMethod << 4) | compressionType);
    dataView.setUint8(3, reservedData);

    return dataView;
  }

  /**
   * 检测音频数据是否为静音
   * @param audioBytes PCM16格式的音频数据
   * @returns true表示静音，false表示有声音
   */
  private detectSilence(audioBytes: Uint8Array): boolean {
    if (!audioBytes || audioBytes.length === 0) {
      return true; // 空数据视为静音
    }

    // 确保数据长度为偶数（PCM16每个样本2字节）
    const sampleCount = Math.floor(audioBytes.length / 2);
    if (sampleCount === 0) {
      return true;
    }

    let sumSquares = 0;
    let maxAmplitude = 0;

    // 计算RMS（均方根）和最大振幅
    for (let i = 0; i < sampleCount; i++) {
      // 读取16位PCM样本（小端序）
      const sample = (audioBytes[i * 2 + 1] << 8) | audioBytes[i * 2];
      // 转换为有符号16位整数
      const signedSample = sample > 32767 ? sample - 65536 : sample;

      const amplitude = Math.abs(signedSample) / 32768.0; // 归一化到0-1
      sumSquares += amplitude * amplitude;
      maxAmplitude = Math.max(maxAmplitude, amplitude);
    }

    const rms = Math.sqrt(sumSquares / sampleCount);

    // 使用RMS和最大振幅的组合判断
    const isRmsSilent = rms < this.silenceThreshold;
    const isMaxSilent = maxAmplitude < this.silenceThreshold * 2; // 最大振幅阈值稍高

    const isSilent = isRmsSilent && isMaxSilent;

    // 详细日志（可选，用于调试）
    if (this.consecutiveSilenceCount % 10 === 0 || !isSilent) { // 每10帧或检测到声音时输出
      console.log(`🔊 MicrophoneASR: 音频分析 - RMS=${rms.toFixed(4)}, Max=${maxAmplitude.toFixed(4)}, 阈值=${this.silenceThreshold}, 静音=${isSilent}`);
    }

    return isSilent;
  }

  /**
   * 验证音频数据格式
   */
  private validateAudioData(audioData: Uint8Array): void {
    console.log(`MicrophoneASR: 音频数据验证 - 长度=${audioData.length}字节`);

    if (audioData.length === 0) {
      console.error('MicrophoneASR: 音频数据为空');
      return;
    }

    if (audioData.length % 2 !== 0) {
      console.error('MicrophoneASR: 音频数据长度不是偶数，不符合16位PCM格式');
      return;
    }

    // 检查前几个样本的值
    const sampleCount = Math.min(4, audioData.length / 2);
    const samples = [];

    for (let i = 0; i < sampleCount; i++) {
      const lowByte = audioData[i * 2];
      const highByte = audioData[i * 2 + 1];
      let sample = lowByte | (highByte << 8);

      if (sample > 32767) {
        sample -= 65536;
      }

      samples.push(sample);
    }

    console.log(`MicrophoneASR: 前${sampleCount}个样本值: [${samples.join(', ')}]`);
    console.log(`MicrophoneASR: 前8字节原始数据: ${Array.from(audioData.slice(0, 8)).map(b => b.toString(16).padStart(2, '0')).join(' ')}`);

    // 检查是否全为零
    const isAllZero = audioData.every(byte => byte === 0);
    if (isAllZero) {
      console.warn('MicrophoneASR: 音频数据全为零，可能是静音');
    }
  }

  /**
   * 配置静音检测参数
   * @param threshold 静音阈值 (0-1，默认0.01)
   * @param minDuration 最小静音持续时间(ms，默认500)
   */
  public configureSilenceDetection(threshold: number = 0.01, minDuration: number = 500): void {
    this.silenceThreshold = Math.max(0, Math.min(1, threshold)); // 限制在0-1范围
    this.minSilenceDuration = Math.max(100, minDuration); // 最小100ms
    console.log(`MicrophoneASR: 静音检测配置更新 - 阈值=${this.silenceThreshold}, 最小持续时间=${this.minSilenceDuration}ms`);
  }

  /**
   * 重置静音检测状态
   */
  public resetSilenceDetection(): void {
    this.consecutiveSilenceCount = 0;
    this.lastSoundTime = Date.now();
    console.log('MicrophoneASR: 静音检测状态已重置');
  }


  private handleWebSocketMessage(data: any): void {
    if (data instanceof Blob) {
      const reader = new FileReader();
      reader.onload = () => {
        this.parseResponse(new Uint8Array(reader.result as ArrayBuffer));
      };
      reader.readAsArrayBuffer(data);
    } else if (data instanceof ArrayBuffer) {
      this.parseResponse(new Uint8Array(data));
    } else if (typeof data === 'string') {
      console.error('MicrophoneASR: Error from server:', data);
      this.notifyError(data);
    }
  }

  // 基于SystemASRManager的parseResponse方法
  private async parseResponse(data: Uint8Array): Promise<number> {
    if (!data || data.length === 0) {
      console.warn('MicrophoneASR: Empty response data');
      return -1;
    }

    // 转换为Buffer以便与doubao-client.ts保持一致
    const msg = Buffer.from(data);

    // 解析响应结构 - 与doubao-client.ts完全一致
    const response: any = {
      code: 0,
      event: 0,
      is_last_package: false,
      payload_sequence: 0,
      payload_size: 0,
      payload_msg: null,
    };

    const headerSize = msg[0] & 0x0f;
    const messageType = msg[1] >> 4;
    const messageTypeSpecificFlags = msg[1] & 0x0f;
    const serializationMethod = msg[2] >> 4;
    const messageCompression = msg[2] & 0x0f;

    console.log('MicrophoneASR: 解析消息头', {
      headerSize,
      messageType: `0x${messageType.toString(16)}`,
      flags: `0x${messageTypeSpecificFlags.toString(16)}`,
      serialization: serializationMethod,
      compression: messageCompression,
      rawBytes: msg.slice(0, Math.min(16, msg.length)).toString('hex'),
    });

    let payload = msg.slice(headerSize * 4);
    console.log('MicrophoneASR: 初始载荷长度:', payload.length);

    // 解析flags - 关键步骤！
    if (messageTypeSpecificFlags & 0x01) {
      console.log('MicrophoneASR: 解析序列号');
      response.payload_sequence = payload.readInt32BE(0);
      payload = payload.slice(4);
      console.log('MicrophoneASR: 序列号:', response.payload_sequence, '剩余载荷长度:', payload.length);
    }
    if (messageTypeSpecificFlags & 0x02) {
      console.log('MicrophoneASR: 标记为最后一个包');
      response.is_last_package = true;
    }
    if (messageTypeSpecificFlags & 0x04) {
      console.log('MicrophoneASR: 解析事件类型');
      if (payload.length >= 4) {
        response.event = payload.readInt32BE(0);
        payload = payload.slice(4);
        console.log('MicrophoneASR: 事件类型:', response.event, '剩余载荷长度:', payload.length);
      } else {
        console.error('MicrophoneASR: 载荷长度不足，无法解析事件类型');
      }
    }

    // 解析message_type
    if (messageType === MessageType.SERVER_FULL_RESPONSE) {
      response.payload_size = payload.readUInt32BE(0);
      payload = payload.slice(4);
    } else if (messageType === MessageType.SERVER_ERROR) {
      response.code = payload.readInt32BE(0);
      response.payload_size = payload.readUInt32BE(4);
      payload = payload.slice(8);
    }

    if (!payload || payload.length === 0) {
      console.log('MicrophoneASR: 空载荷，直接处理响应');
      await this.handleParsedResponse(response);
      return response.payload_sequence || 0;
    }

    // 解压缩
    if (messageCompression === CompressionType.GZIP) {
      try {
        payload = Buffer.from(await this.gunzipAsync(payload));
      } catch (e) {
        // 对于event=150的初始响应，payload解析失败是正常的
        if (response.event !== 150) {
          console.error('MicrophoneASR: 解压缩失败:', e);
          throw e;
        }
        console.log('MicrophoneASR: event=150 解压缩失败是正常的');
        await this.handleParsedResponse(response);
        return response.payload_sequence || 0;
      }
    }

    // 解析payload
    if (serializationMethod === SerializationType.JSON && payload.length > 0) {
      try {
        response.payload_msg = JSON.parse(payload.toString("utf-8"));
      } catch (e) {
        if (response.event !== 150) {
          console.error('MicrophoneASR: JSON解析失败:', e);
          console.error('MicrophoneASR: 原始载荷:', payload.toString("utf-8"));
          throw e;
        }
        console.log('MicrophoneASR: event=150 JSON解析失败是正常的');
      }
    }

    await this.handleParsedResponse(response);
    return response.payload_sequence || 0;
  }

  // 存储当前转录状态
  private currentTranscription: string = '';
  private lastHistoryText: string = '';

  // 处理解析后的响应 - 基于SystemASRManager的逻辑
  private async handleParsedResponse(response: any): Promise<void> {
    console.log('MicrophoneASR: 处理解析后的响应', {
      code: response.code,
      event: response.event,
      isLastPackage: response.is_last_package,
      hasPayload: !!response.payload_msg,
    });

    this.sessionStarted = true;
    this.readyToSendAudio = true;

    // 处理不同的事件类型
    if (response.event === 150) {
      console.log('MicrophoneASR: 服务器握手成功 (event 150)');
      // 关键：在收到 event 150 后，标记会话已开始并准备发送音频
      this.sessionStarted = true;
      this.readyToSendAudio = true;
      console.log('MicrophoneASR: 会话已开始，准备发送音频数据');
      return;
    }

    if (response.event === 153) {
      this.sessionStarted = false;
      this.readyToSendAudio = false;
      console.error('MicrophoneASR: 服务器连接失败');
      this.notifyError('Server connection failed');
      return;
    }

    if (response.code !== 0) {
      this.sessionStarted = false;
      this.readyToSendAudio = false;
      console.error('MicrophoneASR: 服务器错误响应', { code: response.code });
      this.notifyError(`Server error: ${response.code}`);
      return;
    }

    if (response.payload_msg?.result) {
      const result = response.payload_msg.result;

      console.log('MicrophoneASR: 处理转录结果', {
        result,
        isLastPackage: response.is_last_package,
      });

      console.log('MicrophoneASR: 转录文本', JSON.stringify(result));

      // 处理转录结果 - 只使用 result.text
      if (typeof result === 'object' && !Array.isArray(result)) {
        const paragraphId = `microphone-${Date.now()}`;

        // 只处理 result.text，忽略 utterances
        if (result.text) {
          this.processTranscriptionText(result.text, paragraphId);
        }
      }
      // 处理列表格式的 result（保持兼容性）
      else if (Array.isArray(result)) {
        for (const item of result) {
          if (item.text) {
            console.log('MicrophoneASR: 转录更新 (列表格式)', {
              text: item.text,
            });

            // 使用相同的处理逻辑
            this.processTranscriptionText(item.text, `microphone-${Date.now()}`);
          }
        }
      }
    }
  }

  /**
   * 处理转录文本 - 通过比较前20个字符判断是否为正在转录
   */
  private processTranscriptionText(text: string, paragraphId: string): void {
    if (!text || !text.trim()) {
      return;
    }

    const trimmedText = text.trim();
    const currentPrefix = this.currentTranscription.substring(0, 20);
    const newPrefix = trimmedText.substring(0, 20);

    const isContinuousTranscription = currentPrefix.length > 0 && (newPrefix.startsWith(currentPrefix) || this.isSimilarSimple(currentPrefix, newPrefix));


    console.log('MicrophoneASR: 处理转录文本', {
      text: trimmedText.substring(0, 50) + (trimmedText.length > 50 ? '...' : ''),
      currentPrefix,
      newPrefix,
      isMatching: isContinuousTranscription
    });

    if (isContinuousTranscription) {
      // 正在转录 - 更新当前转录内容
      this.currentTranscription = trimmedText;

      // 发送实时转录结果
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.webContents.send('asr:transcription', {
          text: trimmedText,
          isFinal: false, // 标记为正在转录
          source: 'microphone',
          timestamp: new Date().toISOString(),
          paragraphId
        });
      }
    } else {
      // 新的转录内容 - 将之前的内容加入历史记录
      if (this.currentTranscription && this.currentTranscription !== this.lastHistoryText) {
        // 发送历史记录
        if (this.mainWindow && !this.mainWindow.isDestroyed()) {
          this.mainWindow.webContents.send('asr:transcription', {
            text: this.currentTranscription,
            isFinal: true, // 标记为历史记录
            source: 'microphone',
            timestamp: new Date().toISOString(),
            paragraphId: `microphone-history-${Date.now()}`
          });
        }
        this.lastHistoryText = this.currentTranscription;
      }

      // 开始新的转录
      this.currentTranscription = trimmedText;

      // 发送当前转录结果
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.webContents.send('asr:transcription', {
          text: trimmedText,
          isFinal: false, // 标记为正在转录
          source: 'microphone',
          timestamp: new Date().toISOString(),
          paragraphId
        });
      }
    }
  }

  private isSimilarSimple(str1: string, str2: string): boolean {
    if (!str1 || !str2) return false;
    if (str1 === str2) return true;
    
    const set1 = new Set(str1);
    const set2 = new Set(str2);
    const intersection = new Set([...set1].filter(char => set2.has(char)));
    const union = new Set([...set1, ...set2]);
    
    const similarity = intersection.size / union.size;
    return similarity >= 0.8;
}

  private notifyError(message: string): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('asr:connection-status', { 
        connected: false,
        error: message
      });
      
      this.mainWindow.webContents.send('asr:error', { 
        message
      });
    }
  }

  public isASRConnected(): boolean {
    return this.isConnected && this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN;
  }

  public isASRProcessing(): boolean {
    return this.isProcessing;
  }

  public isConnectionBlocked(): boolean {
    return this.connectionAttemptBlocked;
  }

  public getCooldownTimeRemaining(): number {
    if (!this.connectionAttemptBlocked) return 0;
    
    const now = Date.now();
    const timeSinceLastConnection = now - this.lastConnectionTime;
    const remainingTime = Math.max(0, this.MIN_CONNECTION_INTERVAL - timeSinceLastConnection);
    
    return remainingTime;
  }

  private handleConnectionFailure(): void {
    console.log('MicrophoneASR: Handling connection failure...');
    
    if (this.wsConnection) {
      try {
        this.wsConnection.terminate();
      } catch (e) {
        console.error('MicrophoneASR: Error terminating broken connection:', e);
      }
      this.wsConnection = null;
    }
    
    this.isConnected = false;
    this.clearIntervals();
    
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('asr:connection-status', { connected: false });
    }
    
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
      }
      
      this.reconnectAttempts++;
      const delay = Math.min(2000 * Math.pow(1.5, this.reconnectAttempts - 1), 30000);
      
      console.log(`MicrophoneASR: Scheduling reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
      
      this.reconnectTimer = setTimeout(async () => {
        console.log('MicrophoneASR: Attempting to reconnect...');
        try {
          await this.connectToASRService();
        } catch (error) {
          console.error('MicrophoneASR: Reconnection failed:', error);
        }
      }, delay);
    }
  }

  public stopASRSession(): void {
    console.log('MicrophoneASR: Stopping ASR session');
    
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
    }
    
    this.isScheduledForClosure = true;
    this.currentConnectionId = generateUUID();
    
    this.clearIntervals();
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.wsConnection) {
      console.log('MicrophoneASR: Stopping session');
      
      try {
        if (this.wsConnection.readyState === WebSocket.OPEN) {
          const endMessage = { type: 'end' };
          this.wsConnection.send(JSON.stringify(endMessage));
          
          setTimeout(() => {
            if (this.wsConnection) {
              this.wsConnection.close(1000, 'Session ended by client');
              this.wsConnection = null;
              this.isScheduledForClosure = false;
            }
          }, 500);
        } else {
          this.wsConnection.close(1000, 'Session ended by client');
          this.wsConnection = null;
          this.isScheduledForClosure = false;
        }
      } catch (error) {
        console.error('MicrophoneASR: Error stopping session:', error);
        
        if (this.wsConnection) {
          try {
            this.wsConnection.close();
          } catch (e) {
            console.error('MicrophoneASR: Error closing WebSocket:', e);
          }
          this.wsConnection = null;
        }
        this.isScheduledForClosure = false;
      }
    } else {
      this.isScheduledForClosure = false;
    }
    
    this.isConnected = false;
    this.isProcessing = false;
    this.sessionStarted = false;
    this.messageQueue = [];
    this.isConnecting = false;
  }

  public dispose(): void {
    console.log('MicrophoneASR: Disposing manager');
    
    this.lastAudioDataTime = 0;
    
    if (this.audioTimeoutCheckTimer) {
      clearInterval(this.audioTimeoutCheckTimer);
      this.audioTimeoutCheckTimer = null;
    }
    
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
    }
    
    this.clearIntervals();
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.connectionBlockTimeout) {
      clearTimeout(this.connectionBlockTimeout);
      this.connectionBlockTimeout = null;
    }
    
    if (this.wsConnection) {
      try {
        this.wsConnection.terminate();
      } catch (e) {
        console.error('MicrophoneASR: Error terminating WebSocket during disposal:', e);
      }
      this.wsConnection = null;
    }
    
    this.isConnected = false;
    this.isProcessing = false;
    this.sessionStarted = false;
    this.isConnecting = false;
    this.isScheduledForClosure = false;
  }

  private startAudioTimeoutCheck(): void {
    if (this.audioTimeoutCheckTimer) {
      clearInterval(this.audioTimeoutCheckTimer);
    }

    this.audioTimeoutCheckTimer = setInterval(() => {
      if (this.isConnected && this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
        const now = Date.now();
        if (now - this.lastAudioDataTime > this.AUDIO_TIMEOUT_MS && this.lastAudioDataTime > 0) {
          console.log(`MicrophoneASR: 超过${this.AUDIO_TIMEOUT_MS/1000}秒未接收到音频数据，准备关闭服务`);
          
          if (this.autoCloseTimer) {
            clearTimeout(this.autoCloseTimer);
          }
          
          this.autoCloseTimer = setTimeout(() => {
            console.log('MicrophoneASR: 由于音频数据超时，自动关闭会话');
            this.stopASRSession();
          }, 2000);
        }
      }
    }, 2000);
  }


}

export const microphoneASRManager = MicrophoneASRManager.getInstance();
export default microphoneASRManager;