/**
 * SystemASRManager.ts
 * Dedicated ASR Manager for System Audio
 * Handles WebSocket connections specifically for system audio processing
 */

import { <PERSON><PERSON>erWindow } from 'electron';
import { getVoiceConfig, isVoiceEnabled } from '../store';
import { generateUUID } from '../utils';
import { gzip, gunzip } from 'zlib';
import { promisify } from 'util';

// Import WebSocket with error handling
let WebSocket: any;
try {
  WebSocket = require('ws');
  console.log('WebSocket module loaded successfully for SystemASR');
} catch (error) {
  console.error('Failed to load ws module for SystemASR:', error);
  WebSocket = class MockWebSocket {
    constructor() {
      throw new Error('WebSocket module is not available');
    }
  };
}

// 协议常量 - 基于doubao-client.ts
enum ProtocolVersion {
  V1 = 0x01,
}

enum MessageType {
  CLIENT_FULL_REQUEST = 0x01,
  CLIENT_AUDIO_ONLY = 0x02,
  SERVER_FULL_RESPONSE = 0x09,
  SERVER_ERROR = 0x0f,
}

enum MessageTypeSpecificFlags {
  NO_SEQUENCE = 0x00,
  POS_SEQUENCE = 0x01,
  NEG_SEQUENCE = 0x02,
  NEG_WITH_SEQUENCE = 0x03,
}

enum SerializationType {
  NO_SERIALIZATION = 0x00,
  JSON = 0x01,
}

enum CompressionType {
  NO_COMPRESSION = 0x00,
  GZIP = 0x01,
}

export class SystemASRManager {
  private wsConnection: any = null;
  private isConnected: boolean = false;
  private isProcessing: boolean = false;
  private mainWindow: BrowserWindow | null = null;
  private static instance: SystemASRManager | null = null;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private messageQueue: Array<any> = [];
  private sessionStarted: boolean = false;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private lastActivityTimestamp: number = 0;
  private sequence: number = 1;

  // 添加gzip异步方法
  private gzipAsync = promisify(gzip);
  private gunzipAsync = promisify(gunzip);
  private readyToSendAudio: boolean = false;
  private isConnecting: boolean = false;
  private currentConnectionId: string = '';
  private isScheduledForClosure: boolean = false;
  private lastConnectionTime: number = 0;
  private connectionAttemptBlocked: boolean = false;
  private connectionBlockTimeout: NodeJS.Timeout | null = null;
  private readonly MIN_CONNECTION_INTERVAL = 30000; // 30秒
  private autoCloseTimer: NodeJS.Timeout | null = null;
  private lastPongTime: number = 0;
  private pingInterval: NodeJS.Timeout | null = null;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private lastAudioDataTime: number = 0;
  private audioTimeoutCheckTimer: NodeJS.Timeout | null = null;
  private readonly AUDIO_TIMEOUT_MS = 8000; // 8秒无音频数据则自动关闭
  private sessionCheckTimer: NodeJS.Timeout | null = null;
  private readonly SESSION_CHECK_INTERVAL = 5000; // 5秒检查一次会话状态

  // 新增优化相关属性
  private lastLogTime: number = 0;
  private audioBuffer: Buffer[] = [];
  private audioBufferSize: number = 0;
  private readonly MAX_BUFFER_SIZE = 32000; // 最大缓冲区大小 (约1秒的16kHz 16位音频)
  private readonly MIN_BUFFER_SIZE = 6400;  // 最小缓冲区大小 (约0.2秒的16kHz 16位音频)

  private static lastSendTime: number = 0;

  private config: any = null;

  private static lastTranscriptions: {
    finalTexts: Set<string>;
    lastInterimText: string;
    lastFinalText: string;
    lastTimestamp: number;
    lastHistoryUpdateTime: number;
    history: Array<{
      text: string;
      source: 'system';
      timestamp: string;
      isFinal: boolean;
      paragraphId?: string;
    }>;
  } | null = null;

  constructor() {
    console.log('SystemASRManager instance created');
    // this.startAudioTimeoutCheck();
    // this.startPerformanceMonitoring();

    if (!SystemASRManager.lastTranscriptions) {
      SystemASRManager.lastTranscriptions = {
        finalTexts: new Set<string>(),
        lastInterimText: '',
        lastFinalText: '',
        lastTimestamp: 0,
        lastHistoryUpdateTime: 0,
        history: []
      };
    }
  }

  /**
   * 设置主窗口引用
   */
  public setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
  }

  /**
   * 从 store 中加载语音配置
   */
  private loadConfig(): void {
    try {
      this.config = getVoiceConfig();
      console.log('SystemASRManager: 语音配置加载成功', this.config);
    } catch (error) {
      console.error('SystemASRManager: 加载语音配置失败', error);
      this.config = null;
    }
  }

  /**
   * 检查系统音频识别功能是否启用
   */
  public isSystemASREnabled(): boolean {
    const enabled = isVoiceEnabled();
    console.log('SystemASRManager: 系统音频识别启用状态:', enabled);
    return enabled;
  }

  /**
   * 获取语音配置中的 AppId
   */
  public getVoiceAppId(): string {
    return this.config?.voiceAppId || '';
  }

  /**
   * 获取语音配置中的 AccessKeyId
   */
  public getVoiceAccessKeyId(): string {
    return this.config?.voiceAccessKeyId || '';
  }

  public static getInstance(): SystemASRManager {
    if (!SystemASRManager.instance) {
      SystemASRManager.instance = new SystemASRManager();
    }
    return SystemASRManager.instance;
  }



  public async startASRSession(): Promise<{ success: boolean, error?: string }> {
    console.log('Starting SystemASR session for system audio');

    if (!this.config) {
      this.loadConfig();
    }

    // 检查语音功能是否启用
    if (!this.isSystemASREnabled()) {
      console.log('SystemASR: 语音功能已禁用');
      return { success: false, error: '语音功能已禁用，请检查配置' };
    }

    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
      console.log('SystemASR: 自动关闭计时器已取消');
    }

    if (this.isConnecting) {
      console.log('SystemASR: Already attempting to connect');
      return { success: false, error: 'Connection already in progress' };
    }

    if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN && this.isConnected) {
      console.log('SystemASR: Active WebSocket connection already exists');
      return { success: true };
    }

    const now = Date.now();
    if (now - this.lastConnectionTime < this.MIN_CONNECTION_INTERVAL && this.connectionAttemptBlocked) {
      const remainingTime = Math.ceil((this.MIN_CONNECTION_INTERVAL - (now - this.lastConnectionTime)) / 1000);
      return { success: false, error: `Please wait ${remainingTime}s before reconnecting` };
    }

    if (this.wsConnection) {
      console.log('SystemASR: Closing existing connection');
      try {
        this.isConnected = false;
        this.sessionStarted = false;
        
        if (this.wsConnection.readyState === WebSocket.OPEN) {
          this.wsConnection.close(1000, 'Closing before new connection');
        } else {
          this.wsConnection.terminate();
        }
      } catch (err) {
        console.error('SystemASR: Error closing existing connection:', err);
      }
      
      this.wsConnection = null;
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    this.lastConnectionTime = now;
    this.messageQueue = [];
    this.reconnectAttempts = 0;
    this.sessionStarted = false;
    this.lastActivityTimestamp = Date.now();
    this.lastPongTime = Date.now();
    this.currentConnectionId = generateUUID();
    
    this.clearIntervals();
    
    try {
      await this.connectToASRService();
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown connection error' 
      };
    }
  }

  private clearIntervals(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }

    if (this.sessionCheckTimer) {
      clearInterval(this.sessionCheckTimer);
      this.sessionCheckTimer = null;
    }
  }

  private async connectToASRService(): Promise<void> {
    // 检查熔断器状态
    if (!this.checkCircuitBreaker()) {
      throw new Error('熔断器开启，拒绝连接请求');
    }

    this.isConnecting = true;
    const connectionId = this.currentConnectionId;

    return new Promise((resolve, reject) => {
      try {
        console.log(`SystemASR: Starting connection attempt (ID: ${connectionId})`);

        // 记录连接尝试
        this.performanceMetrics.connectionAttempts++;
        const connectionStartTime = Date.now();

        const requestId = generateUUID();
        // 使用bigmodel_async端点
        const targetUrl = 'wss://openspeech.bytedance.com/api/v3/sauc/bigmodel_async';

        const options = {
          headers: {
            'X-Api-App-Key': this.getVoiceAppId(),
            'X-Api-Access-Key': this.getVoiceAccessKeyId(),
            'X-Api-Resource-Id': 'volc.bigasr.sauc.duration',
            'X-Api-Request-Id': requestId
          },
          timeout: 10000
        };
        
        this.wsConnection = new WebSocket(targetUrl, options);
        this.wsConnection.binaryType = 'arraybuffer';
        
        const connectionTimeout = setTimeout(() => {
          if (connectionId !== this.currentConnectionId) return;
          
          if (!this.isConnected) {
            console.error('SystemASR: Connection timeout');
            if (this.wsConnection) {
              this.wsConnection.terminate();
              this.wsConnection = null;
            }
            
            if (this.reconnectAttempts < this.maxReconnectAttempts) {
              this.reconnectAttempts++;
              console.log(`SystemASR: Reconnection attempt ${this.reconnectAttempts} of ${this.maxReconnectAttempts}`);
              setTimeout(() => {
                if (connectionId === this.currentConnectionId) {
                  this.connectToASRService().then(resolve).catch(reject);
                }
              }, 3000);
            } else {
              this.isConnecting = false;
              this.recordConnectionFailure(); // 记录连接失败
              this.performanceMetrics.failedConnections++; // 记录失败连接
              reject(new Error('Connection timeout after multiple attempts'));
            }
          }
        }, 15000);
        
        this.wsConnection.on('open', () => {
          if (connectionId !== this.currentConnectionId) {
            clearTimeout(connectionTimeout);
            try {
              this.wsConnection.close(1000, "Superseded by newer connection");
            } catch (e) {
              console.error("SystemASR: Error closing superseded connection:", e);
            }
            return;
          }
          
          clearTimeout(connectionTimeout);
          console.log('SystemASR: WebSocket connection established');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.lastActivityTimestamp = Date.now();
          this.isConnecting = false;

          // 记录连接成功，重置熔断器
          this.recordConnectionSuccess();

          // 记录性能指标
          this.performanceMetrics.successfulConnections++;
          this.performanceMetrics.lastConnectionTime = Date.now();
          const connectionTime = Date.now() - connectionStartTime;
          this.performanceMetrics.totalReconnectionTime += connectionTime;
          
          this.sendInitMessage();
          this.processQueuedMessages();
          this.startHeartbeat();
          this.startSessionCheck(); // 启动会话状态检查

          if (this.mainWindow && !this.mainWindow.isDestroyed()) {
            this.mainWindow.webContents.send('system-asr:connection-status', { connected: true });
          }
          
          resolve();
        });
        
        this.wsConnection.on('message', (message: any) => {
          if (connectionId !== this.currentConnectionId) return;
          this.handleWebSocketMessage(message);
        });
        
        this.wsConnection.on('error', (error: any) => {
          if (connectionId !== this.currentConnectionId) {
            clearTimeout(connectionTimeout);
            return;
          }
          
          clearTimeout(connectionTimeout);
          console.error('SystemASR: WebSocket error:', error);
          
          this.isConnected = false;
          this.clearIntervals();
          
          if (this.mainWindow && !this.mainWindow.isDestroyed()) {
            this.mainWindow.webContents.send('system-asr:error', {
              error: true,
              message: `SystemASR service error: ${error.message || 'Unknown error'}`,
              code: error.code || 1011
            });
          }
          
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            setTimeout(() => {
              if (connectionId === this.currentConnectionId) {
                this.connectToASRService().then(resolve).catch(reject);
              }
            }, 5000);
          } else {
            this.isConnecting = false;
            this.recordConnectionFailure(); // 记录连接失败
            reject(error);
          }
        });
        
        this.wsConnection.on('close', (code: number, reason: string) => {
          if (connectionId !== this.currentConnectionId) {
            clearTimeout(connectionTimeout);
            return;
          }
          
          clearTimeout(connectionTimeout);
          console.log(`SystemASR: WebSocket closed with code ${code}: ${reason || 'No reason provided'}`);
          
          this.isConnected = false;
          this.sessionStarted = false;
          this.clearIntervals();
          
          if (this.mainWindow && !this.mainWindow.isDestroyed()) {
            this.mainWindow.webContents.send('system-asr:connection-status', { 
              connected: false,
              code,
              reason
            });
          }
          
          if (code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            setTimeout(() => {
              if (connectionId === this.currentConnectionId) {
                this.connectToASRService().then(resolve).catch(reject);
              }
            }, 5000);
          } else {
            this.isConnecting = false;
          }
        });
      } catch (error) {
        console.error('SystemASR: Failed to create WebSocket connection:', error);
        this.isConnecting = false;
        this.recordConnectionFailure(); // 记录连接失败
        reject(error);
      }
    });
  }

  private async sendInitMessage(): Promise<void> {
    if (!this.wsConnection || this.wsConnection.readyState !== WebSocket.OPEN) {
      console.error('SystemASR: Cannot send init message: WebSocket not open');
      return;
    }

    try {
      // 使用doubao-client.ts的配置格式
      const requestConfig = {
        user: { uid: "system-audio" },
        audio: {
          format: "pcm",
          codec: "raw",
          rate: 16000,
          bits: 16,
          channel: 1,
        },
        request: {
          model_name: "bigmodel",
          enable_itn: true,
          enable_punc: true,
          enable_ddc: true,
          show_utterances: false, // 不需要 utterances，只使用 result.text
          enable_nonstream: false,
          result_type: 'single'
        },
      };

      console.log('SystemASR: 发送初始化消息 (bigmodel_async)');

      // 使用doubao-client.ts的消息构建方式
      const header = this.createHeader(MessageType.CLIENT_FULL_REQUEST);
      const payload = await this.gzipAsync(Buffer.from(JSON.stringify(requestConfig)));
      const message = this.buildMessage(header, this.sequence++, payload);

      console.log(`SystemASR: 发送配置消息，序列号=${this.sequence - 1}, 消息大小=${message.length}`);

      this.wsConnection.send(message);
      this.readyToSendAudio = false;
      this.lastActivityTimestamp = Date.now();
    } catch (error) {
      console.error('SystemASR: 发送初始化消息时出错:', error);
    }
  }

  private processQueuedMessages(): void {
    if (this.messageQueue.length > 0) {
      console.log(`SystemASR: Processing ${this.messageQueue.length} queued audio messages`);
      const queuedMessages = [...this.messageQueue]; // 创建副本
      this.messageQueue = []; // 清空队列

      // 逐个处理队列中的音频数据
      queuedMessages.forEach(audioData => {
        if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN && this.readyToSendAudio) {
          this.processAudioData(audioData);
        }
      });
    }
  }

  private startHeartbeat(): void {
    this.clearIntervals();
    
    this.pingInterval = setInterval(() => {
      if (this.isScheduledForClosure) {
        this.clearIntervals();
        return;
      }
      
      if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
        try {
          this.wsConnection.ping();
          
          const now = Date.now();
          if (now - this.lastPongTime > 30000) {
            console.warn('SystemASR: No pong response for 30 seconds');
            this.handleConnectionFailure();
          }
        } catch (error) {
          console.error('SystemASR: Error sending ping:', error);
          this.handleConnectionFailure();
        }
      }
    }, 15000);
    
    if (this.wsConnection) {
      this.wsConnection.on('pong', () => {
        this.lastPongTime = Date.now();
      });
    }
    
    this.heartbeatInterval = setInterval(() => {
      if (this.isScheduledForClosure) {
        this.clearIntervals();
        return;
      }
      
      if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
        const currentTime = Date.now();
        if (currentTime - this.lastActivityTimestamp > 15000) {
          console.log('SystemASR: Sending heartbeat');
          
          try {
            this.wsConnection.send(JSON.stringify({ type: 'heartbeat' }));
            this.lastActivityTimestamp = currentTime;
          } catch (error) {
            console.error('SystemASR: Error sending heartbeat:', error);
            this.handleConnectionFailure();
          }
        }
      } else {
        this.clearIntervals();
      }
    }, 10000);
  }

  /**
   * 启动会话状态检查
   * 定期检查会话状态，如果连接正常但会话未开始，尝试重新发送初始化消息
   */
  private startSessionCheck(): void {
    if (this.sessionCheckTimer) {
      clearInterval(this.sessionCheckTimer);
    }

    this.sessionCheckTimer = setInterval(() => {
      // 检查连接状态和会话状态
      if (this.isConnected &&
          this.wsConnection &&
          this.wsConnection.readyState === WebSocket.OPEN &&
          !this.sessionStarted) {

        const now = Date.now();
        const timeSinceConnection = now - this.lastActivityTimestamp;

        // 如果连接建立超过10秒但会话仍未开始，且队列中有数据
        if (timeSinceConnection > 10000 && this.messageQueue.length > 0) {
          console.warn(`SystemASR: 连接正常但会话未开始 ${timeSinceConnection}ms，队列长度: ${this.messageQueue.length}`);
          console.log('SystemASR: 尝试重新发送初始化消息');

          // 重新发送初始化消息
          this.sendInitMessage().catch(error => {
            console.error('SystemASR: 重新发送初始化消息失败:', error);
          });
        }

        // 如果队列积累过多，清理旧数据
        if (this.messageQueue.length > 30) {
          console.warn('SystemASR: 队列积累过多，清理旧数据');
          this.messageQueue = this.messageQueue.slice(-10); // 只保留最新的10个
        }
      }

      // 如果会话已开始，处理队列中的消息
      if (this.sessionStarted && this.readyToSendAudio && this.messageQueue.length > 0) {
        console.log(`SystemASR: 会话已开始，处理队列中的 ${this.messageQueue.length} 条消息`);
        this.processQueuedMessages();
      }
    }, this.SESSION_CHECK_INTERVAL);
  }

  public async processAudioData(audioData: any): Promise<void> {
    this.lastAudioDataTime = Date.now();

    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
    }

    this.lastActivityTimestamp = Date.now();

    const audioDataLength = audioData.audio_data ? audioData.audio_data.length : 0;
    const sampleRate = audioData.sample_rate || 16000;

    // 记录音频数据处理性能指标
    this.performanceMetrics.totalAudioPacketsSent++;
    this.performanceMetrics.totalAudioDataSent += audioDataLength;

    // 优化日志输出 - 减少频繁日志，提高性能
    const now = Date.now();
    if (!this.lastLogTime || now - this.lastLogTime > 5000) { // 每5秒输出一次详细日志
      console.log(`SystemASR: 处理音频数据，长度=${audioDataLength}字节, 采样率=${sampleRate}Hz`);
      this.lastLogTime = now;
    }

    if (!this.wsConnection) {
      console.error('SystemASR: WebSocket连接未初始化');
      throw new Error('SystemASR WebSocket connection not initialized');
    }

    if (!this.sessionStarted && this.isConnected) {
      console.log(`SystemASR: 会话尚未开始，将音频数据加入队列 (队列长度: ${this.messageQueue.length})`);
      // 限制队列大小，避免内存溢出
      if (this.messageQueue.length < 50) {
        this.messageQueue.push(audioData);
      } else {
        console.warn('SystemASR: 音频数据队列已满，丢弃旧数据');
        this.messageQueue.shift(); // 移除最旧的数据
        this.messageQueue.push(audioData);
      }
      return;
    }

    // 优化发送间隔 - 根据数据大小动态调整
    const MIN_SEND_INTERVAL = this.calculateOptimalSendInterval(audioDataLength);

    if (!SystemASRManager.lastSendTime) {
      SystemASRManager.lastSendTime = 0;
    }

    if (now - SystemASRManager.lastSendTime < MIN_SEND_INTERVAL) {
      // 不再跳过，而是缓存数据进行批量处理
      this.bufferAudioData(audioData);
      return;
    }

    // 检查是否有缓冲的数据需要先发送
    if (this.audioBufferSize > 0) {
      this.flushAudioBuffer(sampleRate);
    }

    if (this.wsConnection.readyState === WebSocket.OPEN && this.readyToSendAudio) {
      try {
        SystemASRManager.lastSendTime = now;

        const audioDataBytes = new Uint8Array(audioData.audio_data);

        if (!audioDataBytes || audioDataBytes.length === 0) {
          console.warn(`SystemASR: 收到空的音频数据，跳过处理`);
          return;
        }

        // 使用新的核心发送方法
        await this.sendAudioDataToASR(audioDataBytes, sampleRate);

      } catch (error) {
        console.error(`SystemASR: 发送音频数据出错:`, error);
        throw error;
      }
    } else if (this.wsConnection.readyState === WebSocket.OPEN && !this.readyToSendAudio) {
      console.warn('SystemASR: WebSocket已连接但尚未准备好发送音频，加入队列');
      // 限制队列大小
      if (this.messageQueue.length < 50) {
        this.messageQueue.push(audioData);
      } else {
        console.warn('SystemASR: 音频数据队列已满，丢弃旧数据');
        this.messageQueue.shift();
        this.messageQueue.push(audioData);
      }
    } else {
      console.warn(`SystemASR: WebSocket未连接，音频数据加入队列`);
      // 限制队列大小
      if (this.messageQueue.length < 50) {
        this.messageQueue.push(audioData);
      } else {
        console.warn('SystemASR: 音频数据队列已满，丢弃旧数据');
        this.messageQueue.shift();
        this.messageQueue.push(audioData);
      }
    }
  }

  // Helper methods
  private convertSampleRate(audioData: Uint8Array, sourceSampleRate: number, targetSampleRate: number): Uint8Array {
    if (sourceSampleRate === targetSampleRate) {
      return audioData;
    }
    
    const dataLength = audioData.length % 2 === 0 ? audioData.length : audioData.length - 1;
    const samples = new Int16Array(dataLength / 2);
    
    for (let i = 0; i < samples.length; i++) {
      samples[i] = (audioData[i * 2] & 0xff) | ((audioData[i * 2 + 1] & 0xff) << 8);
    }
    
    let newSampleCount: number;
    
    if (sourceSampleRate > targetSampleRate) {
      const ratio = sourceSampleRate / targetSampleRate;
      newSampleCount = Math.floor(samples.length / ratio);
    } else {
      const ratio = targetSampleRate / sourceSampleRate;
      newSampleCount = Math.floor(samples.length * ratio);
    }
    
    const newSamples = new Int16Array(newSampleCount);
    
    if (sourceSampleRate > targetSampleRate) {
      const ratio = sourceSampleRate / targetSampleRate;
      for (let i = 0; i < newSampleCount; i++) {
        const srcIndex = i * ratio;
        const srcIndexInt = Math.floor(srcIndex);
        const srcIndexFrac = srcIndex - srcIndexInt;
        
        if (srcIndexInt + 1 < samples.length) {
          newSamples[i] = Math.round(samples[srcIndexInt] * (1 - srcIndexFrac) + samples[srcIndexInt + 1] * srcIndexFrac);
        } else {
          newSamples[i] = samples[srcIndexInt];
        }
      }
    } else {
      const ratio = targetSampleRate / sourceSampleRate;
      for (let i = 0; i < newSampleCount; i++) {
        const srcIndex = i / ratio;
        const srcIndexInt = Math.floor(srcIndex);
        const srcIndexFrac = srcIndex - srcIndexInt;
        
        if (srcIndexInt + 1 < samples.length) {
          newSamples[i] = Math.round(samples[srcIndexInt] * (1 - srcIndexFrac) + samples[srcIndexInt + 1] * srcIndexFrac);
        } else {
          newSamples[i] = samples[srcIndexInt < samples.length ? srcIndexInt : samples.length - 1];
        }
      }
    }
    
    const newAudioData = new Uint8Array(newSamples.length * 2);
    for (let i = 0; i < newSamples.length; i++) {
      newAudioData[i * 2] = newSamples[i] & 0xff;
      newAudioData[i * 2 + 1] = (newSamples[i] >> 8) & 0xff;
    }
    
    return newAudioData;
  }

  private async compressData(data: string | Uint8Array | ArrayBuffer): Promise<ArrayBufferLike> {
    try {
      let inputData: Buffer;

      if (typeof data === 'string') {
        inputData = Buffer.from(data);
      } else if (data instanceof Uint8Array) {
        inputData = Buffer.from(data);
      } else if (data instanceof ArrayBuffer) {
        inputData = Buffer.from(data);
      } else {
        throw new Error('Unsupported data type for compression');
      }

      try {
        const compressed = await this.gzipAsync(inputData);
        return compressed.buffer.slice(compressed.byteOffset, compressed.byteOffset + compressed.byteLength);
      } catch (gzipError) {
        console.error('SystemASR: Error compressing data:', gzipError);
        return inputData.buffer.slice(inputData.byteOffset, inputData.byteOffset + inputData.byteLength);
      }
    } catch (error) {
      console.error('SystemASR: Error importing zlib:', error);
      
      if (typeof data === 'string') {
        return new TextEncoder().encode(data).buffer;
      } else if (data instanceof ArrayBuffer) {
        return data;
      } else if (data instanceof Uint8Array) {
        return data.buffer;
      } else {
        throw new Error('Unsupported data type for compression');
      }
    }
  }



  // 基于doubao-client.ts的createHeader方法
  private createHeader(
    messageType: number,
    flags: number = MessageTypeSpecificFlags.POS_SEQUENCE,
    serialization: number = SerializationType.JSON,
    compression: number = CompressionType.GZIP
  ): Buffer {
    const header = Buffer.alloc(4);
    header[0] = (ProtocolVersion.V1 << 4) | 0x01; // version + header size
    header[1] = (messageType << 4) | flags; // type + flags
    header[2] = (serialization << 4) | compression; // JSON + GZIP
    header[3] = 0x00; // reserved
    return header;
  }

  // 基于doubao-client.ts的buildMessage方法
  private buildMessage(header: Buffer, seq: number, payload: Buffer): Buffer {
    const message = Buffer.allocUnsafe(header.length + 4 + 4 + payload.length);
    header.copy(message, 0);
    message.writeInt32BE(seq, 4);
    message.writeUInt32BE(payload.length, 8);
    payload.copy(message, 12);

    console.log(`SystemASR: 构建消息 - 序列号=${seq}, 载荷大小=${payload.length}, 总大小=${message.length}`);
    return message;
  }





  private handleWebSocketMessage(data: any): void {
    // 记录消息接收性能指标
    this.performanceMetrics.messagesReceived++;

    if (data instanceof Blob) {
      const reader = new FileReader();
      reader.onload = () => {
        this.parseResponse(new Uint8Array(reader.result as ArrayBuffer));
      };
      reader.readAsArrayBuffer(data);
    } else if (data instanceof ArrayBuffer) {
      this.parseResponse(new Uint8Array(data));
    } else if (typeof data === 'string') {
      console.error('SystemASR: Error from server:', data);
      this.notifyError(data);
    }
  }

  // 基于doubao-client.ts的parseResponse方法
  private async parseResponse(data: Uint8Array): Promise<number> {
    if (!data || data.length === 0) {
      console.warn('SystemASR: Empty response data');
      return -1;
    }

    // 转换为Buffer以便与doubao-client.ts保持一致
    const msg = Buffer.from(data);

    // 解析响应结构 - 与doubao-client.ts完全一致
    const response: any = {
      code: 0,
      event: 0,
      is_last_package: false,
      payload_sequence: 0,
      payload_size: 0,
      payload_msg: null,
    };

    const headerSize = msg[0] & 0x0f;
    const messageType = msg[1] >> 4;
    const messageTypeSpecificFlags = msg[1] & 0x0f;
    const serializationMethod = msg[2] >> 4;
    const messageCompression = msg[2] & 0x0f;

    console.log('SystemASR: 解析消息头', {
      headerSize,
      messageType: `0x${messageType.toString(16)}`,
      flags: `0x${messageTypeSpecificFlags.toString(16)}`,
      serialization: serializationMethod,
      compression: messageCompression,
      rawBytes: msg.slice(0, Math.min(16, msg.length)).toString('hex'),
    });

    let payload = msg.slice(headerSize * 4);
    console.log('SystemASR: 初始载荷长度:', payload.length);

    // 解析flags - 关键步骤！
    if (messageTypeSpecificFlags & 0x01) {
      console.log('SystemASR: 解析序列号');
      response.payload_sequence = payload.readInt32BE(0);
      payload = payload.slice(4);
      console.log('SystemASR: 序列号:', response.payload_sequence, '剩余载荷长度:', payload.length);
    }
    if (messageTypeSpecificFlags & 0x02) {
      console.log('SystemASR: 标记为最后一个包');
      response.is_last_package = true;
    }
    if (messageTypeSpecificFlags & 0x04) {
      console.log('SystemASR: 解析事件类型');
      if (payload.length >= 4) {
        response.event = payload.readInt32BE(0);
        payload = payload.slice(4);
        console.log('SystemASR: 事件类型:', response.event, '剩余载荷长度:', payload.length);
      } else {
        console.error('SystemASR: 载荷长度不足，无法解析事件类型');
      }
    }

    // 解析message_type
    if (messageType === MessageType.SERVER_FULL_RESPONSE) {
      response.payload_size = payload.readUInt32BE(0);
      payload = payload.slice(4);
    } else if (messageType === MessageType.SERVER_ERROR) {
      response.code = payload.readInt32BE(0);
      response.payload_size = payload.readUInt32BE(4);
      payload = payload.slice(8);
    }

    if (!payload || payload.length === 0) {
      console.log('SystemASR: 空载荷，直接处理响应');
      await this.handleParsedResponse(response);
      return response.payload_sequence || 0;
    }

    // 解压缩
    if (messageCompression === CompressionType.GZIP) {
      try {
        payload = Buffer.from(await this.gunzipAsync(payload));
      } catch (e) {
        // 对于event=150的初始响应，payload解析失败是正常的
        if (response.event !== 150) {
          console.error('SystemASR: 解压缩失败:', e);
          throw e;
        }
        console.log('SystemASR: event=150 解压缩失败是正常的');
        await this.handleParsedResponse(response);
        return response.payload_sequence || 0;
      }
    }

    // 解析payload
    if (serializationMethod === SerializationType.JSON && payload.length > 0) {
      try {
        response.payload_msg = JSON.parse(payload.toString("utf-8"));
      } catch (e) {
        if (response.event !== 150) {
          console.error('SystemASR: JSON解析失败:', e);
          console.error('SystemASR: 原始载荷:', payload.toString("utf-8"));
          throw e;
        }
        console.log('SystemASR: event=150 JSON解析失败是正常的');
      }
    }

    await this.handleParsedResponse(response);
    return response.payload_sequence || 0;
  }

  // 存储当前转录状态
  private currentTranscription: string = '';
  private lastHistoryText: string = '';

  // 处理解析后的响应 - 基于doubao-client.ts的逻辑
  private async handleParsedResponse(response: any): Promise<void> {
    console.log('SystemASR: 处理解析后的响应', {
      code: response.code,
      event: response.event,
      isLastPackage: response.is_last_package,
      hasPayload: !!response.payload_msg,
    });

    this.sessionStarted = true;
    this.readyToSendAudio = true;
    // 处理不同的事件类型
    if (response.event === 150) {
      console.log('SystemASR: 服务器握手成功 (event 150)');
      // 关键：在收到 event 150 后，标记会话已开始并准备发送音频
      this.sessionStarted = true;
      this.readyToSendAudio = true;
      console.log('SystemASR: 会话已开始，准备发送音频数据');
      return;
    }

    if (response.event === 153) {
      this.sessionStarted = false;
      this.readyToSendAudio = false;
      console.error('SystemASR: 服务器连接失败');
      this.notifyError('Server connection failed');
      return;
    }

    if (response.code !== 0) {
      this.sessionStarted = false;
      this.readyToSendAudio = false;
      console.error('SystemASR: 服务器错误响应', { code: response.code });
      this.notifyError(`Server error: ${response.code}`);
      return;
    }

    if (response.payload_msg?.result) {
      const result = response.payload_msg.result;

      console.log('SystemASR: 处理转录结果', {
        result,
        isLastPackage: response.is_last_package,
      });

      // 处理转录结果 - 只使用 result.text
      if (typeof result === 'object' && !Array.isArray(result)) {
        const paragraphId = `system-${Date.now()}`;

        console.log('SystemASR: 转录文本', JSON.stringify(result));

        // 只处理 result.text，忽略 utterances
        if (result.text) {
          this.processTranscriptionText(result.text, paragraphId);
        }
      }
      // 处理列表格式的 result（保持兼容性）
      else if (Array.isArray(result)) {
        for (const item of result) {
          if (item.text) {
            console.log('SystemASR: 转录更新 (列表格式)', {
              text: item.text,
            });

            // 使用相同的处理逻辑
            this.processTranscriptionText(item.text, `system-${Date.now()}`);
          }
        }
      }
    }
  }

  /**
   * 处理转录文本 - 通过比较前20个字符判断是否为正在转录
   */
  private processTranscriptionText(text: string, paragraphId: string): void {
    if (!text || !text.trim()) {
      return;
    }

    const trimmedText = text.trim();

    const currentPrefix = this.currentTranscription.substring(0, 20);
    const newPrefix = trimmedText.substring(0, 20);

    const isContinuousTranscription = currentPrefix.length > 0 && (newPrefix.startsWith(currentPrefix) || this.isSimilarSimple(currentPrefix, newPrefix));

    console.log('SystemASR: 处理转录文本', {
      text: trimmedText.substring(0, 50) + (trimmedText.length > 50 ? '...' : ''),
      currentPrefix,
      newPrefix,
      isMatching: isContinuousTranscription
    });

    
    if (isContinuousTranscription) {
      // 正在转录 - 更新当前转录内容
      this.currentTranscription = trimmedText;

      // 发送实时转录结果
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.webContents.send('asr:transcription', {
          text: trimmedText,
          isFinal: false, // 标记为正在转录
          source: 'system',
          timestamp: new Date().toISOString(),
          paragraphId
        });
      }
    } else {
      // 新的转录内容 - 将之前的内容加入历史记录
      if (this.currentTranscription && this.currentTranscription !== this.lastHistoryText) {
        // 发送历史记录
        if (this.mainWindow && !this.mainWindow.isDestroyed()) {
          this.mainWindow.webContents.send('asr:transcription', {
            text: this.currentTranscription,
            isFinal: true, // 标记为历史记录
            source: 'system',
            timestamp: new Date().toISOString(),
            paragraphId: `system-history-${Date.now()}`
          });
        }
        this.lastHistoryText = this.currentTranscription;
      }

      // 开始新的转录
      this.currentTranscription = trimmedText;

      // 发送当前转录结果
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.webContents.send('asr:transcription', {
          text: trimmedText,
          isFinal: false, // 标记为正在转录
          source: 'system',
          timestamp: new Date().toISOString(),
          paragraphId
        });
      }
    }
  }



  private isSimilarSimple(str1: string, str2: string): boolean {
    if (!str1 || !str2) return false;

    // 标准化字符串：去除空格，转换为小写
    const normalize = (str: string) => str.replace(/\s+/g, '').toLowerCase();
    const normalized1 = normalize(str1);
    const normalized2 = normalize(str2);

    // 如果标准化后完全相同，返回 true
    if (normalized1 === normalized2) return true;

    // 检查是否一个是另一个的前缀（标准化后）
    if (normalized1.length > 0 && normalized2.startsWith(normalized1)) return true;
    if (normalized2.length > 0 && normalized1.startsWith(normalized2)) return true;

    // 使用字符集相似度计算（基于标准化后的字符串）
    const set1 = new Set(normalized1);
    const set2 = new Set(normalized2);
    const intersection = new Set([...set1].filter(char => set2.has(char)));
    const union = new Set([...set1, ...set2]);

    const similarity = intersection.size / union.size;
    return similarity >= 0.8;
  }


  private notifyError(message: string): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('system-asr:connection-status', { 
        connected: false,
        error: message
      });
      
      this.mainWindow.webContents.send('system-asr:error', { 
        message
      });
    }
  }

  public isASRConnected(): boolean {
    return this.isConnected && this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN;
  }

  public isASRProcessing(): boolean {
    return this.isProcessing;
  }

  public isConnectionBlocked(): boolean {
    return this.connectionAttemptBlocked;
  }

  public getCooldownTimeRemaining(): number {
    if (!this.connectionAttemptBlocked) return 0;
    
    const now = Date.now();
    const timeSinceLastConnection = now - this.lastConnectionTime;
    const remainingTime = Math.max(0, this.MIN_CONNECTION_INTERVAL - timeSinceLastConnection);
    
    return remainingTime;
  }

  private handleConnectionFailure(): void {
    console.log('SystemASR: Handling connection failure...');

    // 清理当前连接
    this.cleanupConnection();

    // 重置状态
    this.isConnected = false;
    this.sessionStarted = false;
    this.readyToSendAudio = false;
    this.clearIntervals();

    // 通知UI连接状态
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('system-asr:connection-status', {
        connected: false,
        reconnecting: this.reconnectAttempts < this.maxReconnectAttempts
      });
    }

    // 智能重连策略
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.scheduleReconnection();
    } else {
      console.error('SystemASR: 达到最大重连次数，停止重连尝试');
      this.notifyError('ASR服务连接失败，已达到最大重试次数');

      // 重置重连计数，允许用户手动重新启动
      setTimeout(() => {
        this.reconnectAttempts = 0;
        console.log('SystemASR: 重连计数已重置，可以重新尝试连接');
      }, 60000); // 1分钟后重置
    }
  }

  /**
   * 清理WebSocket连接和相关资源
   */
  private cleanupConnection(): void {
    if (this.wsConnection) {
      try {
        // 移除所有事件监听器，避免重复触发
        this.wsConnection.removeAllListeners();

        // 尝试优雅关闭
        if (this.wsConnection.readyState === WebSocket.OPEN) {
          this.wsConnection.close(1000, 'Client initiated cleanup');
        } else {
          this.wsConnection.terminate();
        }
      } catch (e) {
        console.error('SystemASR: Error during connection cleanup:', e);
        try {
          this.wsConnection.terminate();
        } catch (terminateError) {
          console.error('SystemASR: Error terminating connection:', terminateError);
        }
      } finally {
        this.wsConnection = null;
      }
    }
  }

  /**
   * 智能重连调度
   */
  private scheduleReconnection(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    this.reconnectAttempts++;

    // 指数退避算法，但有最大延迟限制
    const baseDelay = 2000;
    const maxDelay = 30000;
    const jitter = Math.random() * 1000; // 添加随机抖动，避免雷群效应
    const delay = Math.min(baseDelay * Math.pow(1.5, this.reconnectAttempts - 1) + jitter, maxDelay);

    console.log(`SystemASR: 调度重连尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts}，延迟 ${Math.round(delay)}ms`);

    this.reconnectTimer = setTimeout(async () => {
      console.log(`SystemASR: 执行重连尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);

      try {
        // 检查网络连接状态
        if (!await this.checkNetworkConnectivity()) {
          console.warn('SystemASR: 网络连接不可用，延迟重连');
          this.scheduleReconnection(); // 递归调度下一次重连
          return;
        }

        await this.connectToASRService();
        console.log('SystemASR: 重连成功');

        // 重连成功后处理队列中的消息
        this.processQueuedMessages();

      } catch (error) {
        console.error(`SystemASR: 重连尝试 ${this.reconnectAttempts} 失败:`, error);

        // 如果还有重连机会，继续尝试
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnection();
        } else {
          this.handleConnectionFailure(); // 达到最大重连次数
        }
      }
    }, delay);
  }

  /**
   * 检查网络连接状态
   */
  private async checkNetworkConnectivity(): Promise<boolean> {
    try {
      // 简单的网络连接检查
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch('https://www.baidu.com', {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache'
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      console.warn('SystemASR: 网络连接检查失败:', error);
      return false;
    }
  }



  /**
   * 熔断器模式实现
   */
  private circuitBreakerState: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  private failureCount: number = 0;
  private lastFailureTime: number = 0;
  private readonly FAILURE_THRESHOLD = 5;
  private readonly RECOVERY_TIMEOUT = 60000; // 1分钟

  /**
   * 检查熔断器状态
   */
  private checkCircuitBreaker(): boolean {
    const now = Date.now();

    switch (this.circuitBreakerState) {
      case 'CLOSED':
        return true; // 正常状态，允许请求

      case 'OPEN':
        if (now - this.lastFailureTime > this.RECOVERY_TIMEOUT) {
          console.log('SystemASR: 熔断器进入半开状态');
          this.circuitBreakerState = 'HALF_OPEN';
          return true;
        }
        console.log('SystemASR: 熔断器开启，拒绝连接请求');
        return false;

      case 'HALF_OPEN':
        return true; // 半开状态，允许一个请求测试

      default:
        return true;
    }
  }

  /**
   * 记录连接成功
   */
  private recordConnectionSuccess(): void {
    this.failureCount = 0;
    if (this.circuitBreakerState !== 'CLOSED') {
      console.log('SystemASR: 熔断器恢复到关闭状态');
      this.circuitBreakerState = 'CLOSED';
    }
  }

  /**
   * 记录连接失败
   */
  private recordConnectionFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.failureCount >= this.FAILURE_THRESHOLD) {
      console.log(`SystemASR: 连续失败${this.failureCount}次，熔断器开启`);
      this.circuitBreakerState = 'OPEN';
    }
  }

  /**
   * 性能监控系统
   */
  private performanceMetrics = {
    connectionAttempts: 0,
    successfulConnections: 0,
    failedConnections: 0,
    totalAudioDataSent: 0,
    totalAudioPacketsSent: 0,
    averagePacketSize: 0,
    lastResetTime: Date.now(),
    connectionUptime: 0,
    lastConnectionTime: 0,
    reconnectionCount: 0,
    averageReconnectionTime: 0,
    totalReconnectionTime: 0,
    messagesSent: 0,
    messagesReceived: 0,
    lastActivityTime: Date.now(),
    memoryUsage: {
      heapUsed: 0,
      heapTotal: 0,
      external: 0,
      rss: 0
    }
  };

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    // 每30秒记录一次性能指标
    setInterval(() => {
      this.recordPerformanceMetrics();
    }, 30000);

    // 每5分钟输出详细的性能报告
    setInterval(() => {
      this.generatePerformanceReport();
    }, 300000);
  }

  /**
   * 记录性能指标
   */
  private recordPerformanceMetrics(): void {
    const now = Date.now();

    // 更新连接正常运行时间
    if (this.isConnected && this.performanceMetrics.lastConnectionTime > 0) {
      this.performanceMetrics.connectionUptime = now - this.performanceMetrics.lastConnectionTime;
    }

    // 记录内存使用情况
    const memUsage = process.memoryUsage();
    this.performanceMetrics.memoryUsage = {
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
      external: Math.round(memUsage.external / 1024 / 1024), // MB
      rss: Math.round(memUsage.rss / 1024 / 1024) // MB
    };

    // 计算平均包大小
    if (this.performanceMetrics.totalAudioPacketsSent > 0) {
      this.performanceMetrics.averagePacketSize = Math.round(
        this.performanceMetrics.totalAudioDataSent / this.performanceMetrics.totalAudioPacketsSent
      );
    }

    // 计算平均重连时间
    if (this.performanceMetrics.reconnectionCount > 0) {
      this.performanceMetrics.averageReconnectionTime = Math.round(
        this.performanceMetrics.totalReconnectionTime / this.performanceMetrics.reconnectionCount
      );
    }

    this.performanceMetrics.lastActivityTime = now;
  }

  /**
   * 生成性能报告
   */
  private generatePerformanceReport(): void {
    const now = Date.now();
    const runningTime = (now - this.performanceMetrics.lastResetTime) / 1000 / 60; // 分钟

    console.log('=== SystemASR 性能报告 ===');
    console.log(`运行时间: ${runningTime.toFixed(1)} 分钟`);
    console.log(`连接尝试: ${this.performanceMetrics.connectionAttempts}`);
    console.log(`成功连接: ${this.performanceMetrics.successfulConnections}`);
    console.log(`失败连接: ${this.performanceMetrics.failedConnections}`);

    if (this.performanceMetrics.connectionAttempts > 0) {
      const successRate = (this.performanceMetrics.successfulConnections / this.performanceMetrics.connectionAttempts * 100).toFixed(1);
      console.log(`连接成功率: ${successRate}%`);
    }

    console.log(`当前连接状态: ${this.isConnected ? '已连接' : '未连接'}`);

    if (this.performanceMetrics.connectionUptime > 0) {
      console.log(`连接正常运行时间: ${(this.performanceMetrics.connectionUptime / 1000 / 60).toFixed(1)} 分钟`);
    }

    console.log(`重连次数: ${this.performanceMetrics.reconnectionCount}`);

    if (this.performanceMetrics.averageReconnectionTime > 0) {
      console.log(`平均重连时间: ${this.performanceMetrics.averageReconnectionTime} 毫秒`);
    }

    console.log(`音频数据发送: ${this.performanceMetrics.totalAudioPacketsSent} 包`);
    console.log(`音频数据总量: ${(this.performanceMetrics.totalAudioDataSent / 1024 / 1024).toFixed(2)} MB`);

    if (this.performanceMetrics.averagePacketSize > 0) {
      console.log(`平均包大小: ${this.performanceMetrics.averagePacketSize} 字节`);
    }

    console.log(`消息发送: ${this.performanceMetrics.messagesSent}`);
    console.log(`消息接收: ${this.performanceMetrics.messagesReceived}`);

    console.log(`内存使用 - 堆已用: ${this.performanceMetrics.memoryUsage.heapUsed}MB, 堆总计: ${this.performanceMetrics.memoryUsage.heapTotal}MB`);
    console.log(`内存使用 - 外部: ${this.performanceMetrics.memoryUsage.external}MB, RSS: ${this.performanceMetrics.memoryUsage.rss}MB`);

    console.log(`熔断器状态: ${this.circuitBreakerState}`);
    console.log(`失败计数: ${this.failureCount}`);

    console.log('========================');

    // 发送性能报告到UI
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('system-asr:performance-report', {
        runningTime: runningTime.toFixed(1),
        connectionAttempts: this.performanceMetrics.connectionAttempts,
        successfulConnections: this.performanceMetrics.successfulConnections,
        failedConnections: this.performanceMetrics.failedConnections,
        successRate: this.performanceMetrics.connectionAttempts > 0 ?
          (this.performanceMetrics.successfulConnections / this.performanceMetrics.connectionAttempts * 100).toFixed(1) : '0',
        isConnected: this.isConnected,
        connectionUptime: this.performanceMetrics.connectionUptime > 0 ?
          (this.performanceMetrics.connectionUptime / 1000 / 60).toFixed(1) : '0',
        reconnectionCount: this.performanceMetrics.reconnectionCount,
        averageReconnectionTime: this.performanceMetrics.averageReconnectionTime,
        audioPacketsSent: this.performanceMetrics.totalAudioPacketsSent,
        audioDataSent: (this.performanceMetrics.totalAudioDataSent / 1024 / 1024).toFixed(2),
        averagePacketSize: this.performanceMetrics.averagePacketSize,
        messagesSent: this.performanceMetrics.messagesSent,
        messagesReceived: this.performanceMetrics.messagesReceived,
        memoryUsage: this.performanceMetrics.memoryUsage,
        circuitBreakerState: this.circuitBreakerState,
        failureCount: this.failureCount,
        timestamp: new Date().toISOString()
      });
    }
  }

  public stopASRSession(): void {
    console.log('SystemASR: Stopping ASR session');
    
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
    }
    
    this.isScheduledForClosure = true;
    this.currentConnectionId = generateUUID();
    
    this.clearIntervals();
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.wsConnection) {
      console.log('SystemASR: Stopping session');
      
      try {
        if (this.wsConnection.readyState === WebSocket.OPEN) {
          const endMessage = { type: 'end' };
          this.wsConnection.send(JSON.stringify(endMessage));
          
          setTimeout(() => {
            if (this.wsConnection) {
              this.wsConnection.close(1000, 'Session ended by client');
              this.wsConnection = null;
              this.isScheduledForClosure = false;
            }
          }, 500);
        } else {
          this.wsConnection.close(1000, 'Session ended by client');
          this.wsConnection = null;
          this.isScheduledForClosure = false;
        }
      } catch (error) {
        console.error('SystemASR: Error stopping session:', error);
        
        if (this.wsConnection) {
          try {
            this.wsConnection.close();
          } catch (e) {
            console.error('SystemASR: Error closing WebSocket:', e);
          }
          this.wsConnection = null;
        }
        this.isScheduledForClosure = false;
      }
    } else {
      this.isScheduledForClosure = false;
    }
    
    this.isConnected = false;
    this.isProcessing = false;
    this.sessionStarted = false;
    this.messageQueue = [];
    this.isConnecting = false;
    this.sequence = 1; // 重置序列号
  }

  public dispose(): void {
    console.log('SystemASR: Disposing manager');
    
    this.lastAudioDataTime = 0;
    
    if (this.audioTimeoutCheckTimer) {
      clearInterval(this.audioTimeoutCheckTimer);
      this.audioTimeoutCheckTimer = null;
    }
    
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
    }
    
    this.clearIntervals();
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.connectionBlockTimeout) {
      clearTimeout(this.connectionBlockTimeout);
      this.connectionBlockTimeout = null;
    }
    
    if (this.wsConnection) {
      try {
        this.wsConnection.terminate();
      } catch (e) {
        console.error('SystemASR: Error terminating WebSocket during disposal:', e);
      }
      this.wsConnection = null;
    }
    
    this.isConnected = false;
    this.isProcessing = false;
    this.sessionStarted = false;
    this.isConnecting = false;
    this.isScheduledForClosure = false;
    this.sequence = 1; // 重置序列号
  }

  private startAudioTimeoutCheck(): void {
    if (this.audioTimeoutCheckTimer) {
      clearInterval(this.audioTimeoutCheckTimer);
    }

    this.audioTimeoutCheckTimer = setInterval(() => {
      if (this.isConnected && this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
        const now = Date.now();
        if (now - this.lastAudioDataTime > this.AUDIO_TIMEOUT_MS && this.lastAudioDataTime > 0) {
          console.log(`SystemASR: 超过${this.AUDIO_TIMEOUT_MS/1000}秒未接收到音频数据，准备关闭服务`);
          
          if (this.autoCloseTimer) {
            clearTimeout(this.autoCloseTimer);
          }
          
          this.autoCloseTimer = setTimeout(() => {
            console.log('SystemASR: 由于音频数据超时，自动关闭会话');
            this.stopASRSession();
          }, 2000);
        }
      }
    }, 2000);
  }



  /**
   * 计算最优发送间隔
   * 根据音频数据大小动态调整发送间隔，提高识别准确度
   */
  private calculateOptimalSendInterval(audioDataLength: number): number {
    // 基础间隔：200ms（与音频文件读取间隔匹配）
    const BASE_INTERVAL = 200;

    // 根据数据大小调整间隔
    if (audioDataLength > 64000) {
      // 大数据块：减少间隔，快速处理
      return Math.max(100, BASE_INTERVAL * 0.5);
    } else if (audioDataLength > 32000) {
      // 中等数据块：标准间隔
      return BASE_INTERVAL * 0.75;
    } else if (audioDataLength > 16000) {
      // 小数据块：标准间隔
      return BASE_INTERVAL;
    } else {
      // 很小的数据块：增加间隔，避免频繁发送
      return BASE_INTERVAL * 1.5;
    }
  }

  /**
   * 缓存音频数据进行批量处理
   * 当发送间隔未到时，将数据缓存起来，达到一定大小后批量发送
   */
  private bufferAudioData(audioData: any): void {
    if (!audioData.audio_data || audioData.audio_data.length === 0) {
      return;
    }

    const audioBuffer = Buffer.from(audioData.audio_data);
    this.audioBuffer.push(audioBuffer);
    this.audioBufferSize += audioBuffer.length;

    // 当缓冲区达到最小大小或超过最大大小时，立即处理
    if (this.audioBufferSize >= this.MIN_BUFFER_SIZE || this.audioBufferSize >= this.MAX_BUFFER_SIZE) {
      this.flushAudioBuffer(audioData.sample_rate || 16000);
    }
  }

  /**
   * 清空音频缓冲区并发送数据
   */
  private flushAudioBuffer(sampleRate: number = 16000): void {
    if (this.audioBuffer.length === 0 || this.audioBufferSize === 0) {
      return;
    }

    // 合并所有缓冲的音频数据
    const combinedBuffer = Buffer.concat(this.audioBuffer, this.audioBufferSize);

    // 创建合并后的音频数据对象
    const combinedAudioData = {
      audio_data: new Uint8Array(combinedBuffer),
      sample_rate: sampleRate,
      audio_format: 'pcm'
    };

    // 重置缓冲区
    this.audioBuffer = [];
    this.audioBufferSize = 0;

    // 发送合并后的数据
    console.log(`SystemASR: 发送缓冲的音频数据，大小=${combinedBuffer.length}字节`);
    this.processCombinedAudioData(combinedAudioData).catch(error => {
      console.error('SystemASR: 发送缓冲音频数据失败:', error);
    });
  }

  /**
   * 处理合并后的音频数据（绕过间隔检查）
   */
  private async processCombinedAudioData(audioData: any): Promise<void> {
    const now = Date.now();
    SystemASRManager.lastSendTime = now;

    if (this.wsConnection.readyState === WebSocket.OPEN && this.readyToSendAudio) {
      try {
        const audioDataBytes = new Uint8Array(audioData.audio_data);

        if (!audioDataBytes || audioDataBytes.length === 0) {
          console.warn(`SystemASR: 收到空的合并音频数据，跳过处理`);
          return;
        }

        // 继续使用原有的处理逻辑...
        await this.sendAudioDataToASR(audioDataBytes, audioData.sample_rate || 16000);

      } catch (error) {
        console.error(`SystemASR: 发送合并音频数据出错:`, error);
        throw error;
      }
    } else {
      console.warn(`SystemASR: WebSocket未准备好，合并音频数据加入队列`);
      this.messageQueue.push(audioData);
    }
  }

  /**
   * 发送音频数据到ASR服务的核心方法
   */
  private async sendAudioDataToASR(audioDataBytes: Uint8Array, sampleRate: number): Promise<void> {
    const nonZeroSamples = Array.from(audioDataBytes).filter(value => value !== 0).length;
    const percentNonZero = (nonZeroSamples / audioDataBytes.length) * 100;

    // 调整静音阈值，允许更多低音量数据通过
    if (percentNonZero < 0.05) {
      console.log('SystemASR: 检测到几乎完全静音数据，跳过发送');
      return;
    }

    let processedAudioData = audioDataBytes;

    if (sampleRate !== 16000) {
      processedAudioData = this.convertSampleRate(audioDataBytes, sampleRate, 16000);
    }

    if (processedAudioData.length % 2 !== 0) {
      processedAudioData = processedAudioData.slice(0, processedAudioData.length - 1);
    }

    const compressedAudio = await this.compressData(processedAudioData);
    const compressedData = new Uint8Array(compressedAudio);

    if (!compressedData || compressedData.length === 0) {
      console.error(`SystemASR: 音频数据压缩失败，跳过发送`);
      return;
    }

    // 使用doubao-client.ts的方式构建音频消息
    // 音频数据使用 POS_SEQUENCE 标志和 NO_SERIALIZATION
    const header = this.createHeader(
      MessageType.CLIENT_AUDIO_ONLY,
      MessageTypeSpecificFlags.POS_SEQUENCE,
      SerializationType.NO_SERIALIZATION,
      CompressionType.GZIP
    );
    const message = this.buildMessage(header, this.sequence++, Buffer.from(compressedData));

    console.log(`SystemASR: 发送音频数据，序列号=${this.sequence - 1}, 总大小=${message.length}字节`);
    this.wsConnection.send(message);

    // 记录消息发送性能指标
    this.performanceMetrics.messagesSent++;

    this.lastActivityTimestamp = Date.now();
    this.isProcessing = true;
  }
}

export const systemASRManager = SystemASRManager.getInstance();
export default systemASRManager;