/**
 * MicrophoneManager.ts
 * 麦克风音频管理器 - 负责捕获麦克风音频并发送到专用的MicrophoneASR服务
 */

import { BrowserWindow } from 'electron';
import { microphoneASRManager } from './MicrophoneASRManager';

export class MicrophoneManager {
  private static instance: MicrophoneManager | null = null;
  private isRecording: boolean = false;
  private mainWindow: BrowserWindow | null = null;

  constructor() {
    console.log('MicrophoneManager: 初始化麦克风管理器');
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): MicrophoneManager {
    if (!MicrophoneManager.instance) {
      MicrophoneManager.instance = new MicrophoneManager();
    }
    return MicrophoneManager.instance;
  }

  /**
   * 设置主窗口引用
   */
  public setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
    microphoneASRManager.setMainWindow(window);
    console.log('MicrophoneManager: 主窗口已设置');
  }

  /**
   * 启动麦克风音频捕获
   */
  public async startCapturing(): Promise<{ success: boolean, error?: string }> {
    console.log('🎤 MicrophoneManager: 开始启动麦克风音频捕获');

    if (this.isRecording) {
      console.log('🎤 MicrophoneManager: 麦克风已在录制中');
      return { success: true };
    }

    try {
      // 第一步：启动专用的MicrophoneASRManager服务
      console.log('🎤 MicrophoneManager: 第一步 - 启动MicrophoneASRManager服务');
      const asrResult = await microphoneASRManager.startASRSession();
      console.log('🎤 MicrophoneManager: MicrophoneASRManager启动结果:', asrResult);

      if (!asrResult.success) {
        console.error('❌ MicrophoneManager: MicrophoneASRManager服务启动失败:', asrResult.error);
        return { success: false, error: `MicrophoneASRManager服务启动失败: ${asrResult.error}` };
      }

      console.log('✅ MicrophoneManager: MicrophoneASRManager服务启动成功');

      // 第二步：启动麦克风音频捕获
      console.log('🎤 MicrophoneManager: 第二步 - 启动麦克风音频捕获');

      this.isRecording = true;

      // 通知渲染进程开始录制
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        console.log('📡 MicrophoneManager: 发送麦克风状态到渲染进程');
        this.mainWindow.webContents.send('microphone:status', { recording: true });
        console.log('✅ MicrophoneManager: 已通知渲染进程录制开始');
      } else {
        console.warn('⚠️ MicrophoneManager: 主窗口不可用，无法发送状态');
      }

      console.log('✅ MicrophoneManager: 麦克风音频捕获启动成功');
      return { success: true };

    } catch (error) {
      console.error('💥 MicrophoneManager: 启动麦克风音频捕获时出错:', error);
      this.isRecording = false;
      return {
        success: false,
        error: error instanceof Error ? error.message : '启动麦克风音频捕获失败'
      };
    }
  }

  /**
   * 处理麦克风音频数据
   * 这个方法接收来自渲染进程的音频数据并发送到专用的MicrophoneASRManager服务
   */
  public async processAudioData(audioData: any): Promise<void> {
    console.log('🎵 MicrophoneManager: 处理麦克风音频数据，长度:', audioData?.audio_data?.byteLength || 0);

    if (!this.isRecording) {
      console.warn('⚠️ MicrophoneManager: 麦克风未在录制状态，忽略音频数据');
      return;
    }

    // 验证音频数据格式
    if (!audioData || !audioData.audio_data) {
      console.warn('⚠️ MicrophoneManager: 无效的音频数据格式');
      return;
    }

    try {
      // 确保MicrophoneASRManager服务已连接
      if (!microphoneASRManager.isASRConnected()) {
        console.warn('⚠️ MicrophoneManager: MicrophoneASRManager未连接，尝试重新连接');
        const reconnectResult = await microphoneASRManager.startASRSession();
        if (!reconnectResult.success) {
          console.error('❌ MicrophoneManager: MicrophoneASRManager重连失败:', reconnectResult.error);
          return;
        }
        console.log('✅ MicrophoneManager: MicrophoneASRManager重连成功');
      }

      console.log('📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager');
      await microphoneASRManager.processAudioData(audioData);
      console.log('✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager');

    } catch (error) {
      console.error('💥 MicrophoneManager: 处理麦克风音频数据时出错:', error);
    }
  }

  /**
   * 停止麦克风音频捕获
   */
  public async stopCapturing(): Promise<void> {
    console.log('🛑 MicrophoneManager: 停止麦克风音频捕获');

    if (!this.isRecording) {
      console.log('⚠️ MicrophoneManager: 麦克风未在录制中');
      return;
    }

    this.isRecording = false;

    try {
      console.log('🔇 MicrophoneManager: 通知MicrophoneASRManager停止处理音频');
      microphoneASRManager.stopASRSession();

      // 通知渲染进程停止录制
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        console.log('📡 MicrophoneManager: 发送停止录制状态到渲染进程');
        this.mainWindow.webContents.send('microphone:status', { recording: false });
        console.log('✅ MicrophoneManager: 已通知渲染进程录制停止');
      } else {
        console.warn('⚠️ MicrophoneManager: 主窗口不可用，无法发送停止状态');
      }
    } catch (error) {
      console.error('💥 MicrophoneManager: 停止麦克风音频捕获时出错:', error);
    }
  }
  
  /**
   * 获取当前录音状态
   */
  public isCapturing(): boolean {
    return this.isRecording;
  }
  
  /**
   * 清理资源
   */
  public dispose(): void {
    this.stopCapturing();
    this.mainWindow = null;
  }
}

export const microphoneManager = MicrophoneManager.getInstance();
export default microphoneManager;