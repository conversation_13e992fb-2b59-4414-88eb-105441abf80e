import { contextBridge, ipc<PERSON><PERSON><PERSON> } from "electron"
const { shell } = require("electron")

// Types for the exposed Electron API
interface ElectronAPI {
  updateContentDimensions: (dimensions: {
    width: number
    height: number
  }) => Promise<void>
  getApiKey: () => Promise<string | null>
  getCurrentModel: () => Promise<{
    model: string
    index: number
    total: number
  }>
  getCurrentCodeLanguage: () => Promise<{
    codeLanguage: string
    index: number
    total: number
  }>
  getScreenshots: () => Promise<{
    success: boolean
    previews?: Array<{ path: string; preview: string }> | null
    error?: string
  }>
  deleteScreenshot: (
    path: string
  ) => Promise<{ success: boolean; error?: string }>
  onScreenshotTaken: (
    callback: (data: { path: string; preview: string }) => void
  ) => () => void
  onSolutionsReady: (callback: (solutions: string) => void) => () => void
  onResetView: (callback: () => void) => () => void
  onSolutionStart: (callback: () => void) => () => void
  onDebugStart: (callback: () => void) => () => void
  onDebugSuccess: (callback: (data: any) => void) => () => void
  onSolutionError: (callback: (error: string) => void) => () => void
  onProcessingNoScreenshots: (callback: () => void) => () => void
  onConfigInitFailed: (callback: (data: { message: string, detail: string }) => void) => () => void
  onConfigUpdated: (callback: (data: { success: boolean, message: string }) => void) => () => void
  onProblemExtracted: (callback: (data: any) => void) => () => void
  onSolutionSuccess: (callback: (data: any) => void) => () => void
  onFirstPointRecorded: (callback: () => void) => () => void // Add this new event listener
  onUnauthorized: (callback: () => void) => () => void
  onApiKeyOutOfCredits: (callback: () => void) => () => void
  onDebugError: (callback: (error: string) => void) => () => void
  onCopySolutionContent: (callback: () => void) => () => void
  onModelChanged: (callback: (event: any, data: any) => void) => () => void // Add this new event listener
  onCodeLanguageChanged: (callback: (event: any, data: any) => void) => () => void // Add this new event listener for code language changes
  onWindowMoved: (callback: () => void) => () => void // New event listener for window movement
  onToggleVoiceAssistant: (callback: (event: any, data: any) => void) => () => void
  onNavigateToVoiceView: (callback: () => void) => () => void // New event listener for navigating to voice view
  onStartVoiceRecognition: (callback: () => void) => () => void // New event listener for voice recognition
  onAsrTranscription: (callback: (event: any, data: any) => void) => () => void // New event listener for ASR transcription
  onAsrConnectionStatus: (callback: (event: any, data: any) => void) => () => void // New event listener for ASR connection status
  onAsrError: (callback: (event: any, data: any) => void) => () => void // New event listener for ASR error
  onMicrophoneStatus: (callback: (event: any, data: any) => void) => () => void // New event listener for microphone status
  takeScreenshot: () => Promise<void>
  takeFullScreenshot: () => Promise<void>
  generateSolution: () => Promise<void>
  moveWindowLeft: () => Promise<void>
  moveWindowRight: () => Promise<void>
  updateApiKey: (apiKey: string) => Promise<void>
  setApiKey: (apiKey: string) => Promise<{ success: boolean }>
  openExternal: (url: string) => void
  updateToolbarBounds: (bounds: { x: number; y: number; width: number; height: number }) => Promise<{ success: boolean }>
  updateToolbarRegions: (regions: Array<{ x: number; y: number; width: number; height: number; type: 'model' | 'code-language' | 'reset' | 'capture' | 'solution' | 'other' }>) => Promise<{ success: boolean }>
  processVoiceToText: (text: string) => Promise<{ success: boolean; response: string; error?: string }>
  processFastVoiceText: (text: string) => Promise<{ success: boolean; response: string; error?: string }>
  processAccurateVoiceText: (text: string) => Promise<{ success: boolean; response: string; error?: string }>
  invoke: (channel: string, ...args: any[]) => Promise<any> // Generic invoke method for IPC
  on: (channel: string, callback: (...args: any[]) => void) => void // Generic on method for IPC
  off: (channel: string, callback: (...args: any[]) => void) => void // Generic off method for IPC
  
  // 系统音频捕获相关API
  startSystemAudioCapturing: () => Promise<{ success: boolean, error?: string }>
  stopSystemAudioCapturing: () => Promise<{ success: boolean, error?: string }>
  getSystemAudioStatus: () => Promise<{ capturing: boolean }>
  onSystemAudioStatus: (callback: (event: any, data: { recording: boolean }) => void) => () => void
  
  // 麦克风音频捕获相关API
  startMicrophoneCapturing: () => Promise<{ success: boolean, error?: string }>
  stopMicrophoneCapturing: () => Promise<{ success: boolean, error?: string }>
  processMicrophoneAudio: (audioData: any) => Promise<{ success: boolean, error?: string }>
  getMicrophoneStatus: () => Promise<{ capturing: boolean }>
  
  // 语音识别流程统一控制API
  startVoiceRecognition: (enableMicrophone?: boolean) => Promise<{ success: boolean, error?: string }>
  stopVoiceRecognition: () => Promise<{ success: boolean, error?: string }>
  
  onHistoryUpdate: (callback: any) => () => void
  onUnifiedHistoryUpdate: (callback: any) => () => void
  
  // 添加流式处理方法
  processFastVoiceTextStream: (text: string) => Promise<{ success: boolean, responseId?: string, error?: string }>
  processAccurateVoiceTextStream: (text: string) => Promise<{ success: boolean, responseId?: string, error?: string }>
  
  // 添加流式响应事件监听
  onFastVoiceTextChunk: (callback: (event: any, data: { id: string, chunk: string, done: boolean, error?: boolean }) => void) => () => void
  onAccurateVoiceTextChunk: (callback: (event: any, data: { id: string, chunk: string, done: boolean, error?: boolean }) => void) => () => void

  // 配置相关API
  shouldShowMicrophoneButton: () => Promise<boolean>

  // Voice panel button event listeners
  onVoiceBackClicked: (callback: () => void) => () => void
  onVoiceMicrophoneClicked: (callback: () => void) => () => void
  onVoiceSystemAudioClicked: (callback: () => void) => () => void
  onVoiceOneClickStartClicked: (callback: () => void) => () => void
  onVoiceSendToAIClicked: (callback: () => void) => () => void
}

export const PROCESSING_EVENTS = {
  //global states
  UNAUTHORIZED: "procesing-unauthorized",
  NO_SCREENSHOTS: "processing-no-screenshots",
  CONFIG_INIT_FAILED: "config-init-failed",
  API_KEY_OUT_OF_CREDITS: "processing-api-key-out-of-credits",
  API_KEY_INVALID: "processing-api-key-invalid",

  //states for generating the initial solution
  INITIAL_START: "initial-start",
  PROBLEM_EXTRACTED: "problem-extracted",
  SOLUTION_SUCCESS: "solution-success",
  INITIAL_SOLUTION_ERROR: "solution-error",

  //states for processing the debugging
  DEBUG_START: "debug-start",
  DEBUG_SUCCESS: "debug-success",
  DEBUG_ERROR: "debug-error"
} as const

// Expose the Electron API to the renderer process
contextBridge.exposeInMainWorld("electronAPI", {
  updateContentDimensions: (dimensions: { width: number; height: number }) =>
    ipcRenderer.invoke("update-content-dimensions", dimensions),

  getApiKey: () => ipcRenderer.invoke("get-api-key"),
  getCurrentModel: () => ipcRenderer.invoke("get-current-model"),
  getCurrentCodeLanguage: () => ipcRenderer.invoke("get-current-code-language"),
  takeScreenshot: () => ipcRenderer.invoke("take-screenshot"),
  takeFullScreenshot: () => ipcRenderer.invoke("take-full-screenshot"),
  generateSolution: () => ipcRenderer.invoke("generate-solution"),
  getScreenshots: () => ipcRenderer.invoke("get-screenshots"),
  deleteScreenshot: (path: string) =>
    ipcRenderer.invoke("delete-screenshot", path),

  // Generic IPC methods
  invoke: (channel: string, ...args: any[]) => ipcRenderer.invoke(channel, ...args),
  on: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.on(channel, callback);
  },
  off: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.removeListener(channel, callback);
  },

  // Event listeners
  onScreenshotTaken: (
    callback: (data: { path: string; preview: string }) => void
  ) => {
    const subscription = (_: any, data: { path: string; preview: string }) =>
      callback(data)
    ipcRenderer.on("screenshot-taken", subscription)
    return () => {
      ipcRenderer.removeListener("screenshot-taken", subscription)
    }
  },
  onSolutionsReady: (callback: (solutions: string) => void) => {
    const subscription = (_: any, solutions: string) => callback(solutions)
    ipcRenderer.on("solutions-ready", subscription)
    return () => {
      ipcRenderer.removeListener("solutions-ready", subscription)
    }
  },
  onResetView: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("reset-view", subscription)
    return () => {
      ipcRenderer.removeListener("reset-view", subscription)
    }
  },
  onSolutionStart: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.INITIAL_START, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.INITIAL_START, subscription)
    }
  },
  onDebugStart: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.DEBUG_START, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.DEBUG_START, subscription)
    }
  },

  onDebugSuccess: (callback: (data: any) => void) => {
    ipcRenderer.on("debug-success", (_event, data) => callback(data))
    return () => {
      ipcRenderer.removeListener("debug-success", (_event, data) =>
        callback(data)
      )
    }
  },
  onDebugError: (callback: (error: string) => void) => {
    const subscription = (_: any, error: string) => callback(error)
    ipcRenderer.on(PROCESSING_EVENTS.DEBUG_ERROR, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.DEBUG_ERROR, subscription)
    }
  },
  onSolutionError: (callback: (error: string) => void) => {
    const subscription = (_: any, error: string) => callback(error)
    ipcRenderer.on(PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR, subscription)
    return () => {
      ipcRenderer.removeListener(
        PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
        subscription
      )
    }
  },
  onProcessingNoScreenshots: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.NO_SCREENSHOTS, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.NO_SCREENSHOTS, subscription)
    }
  },
  onConfigInitFailed: (callback: (data: { message: string, detail: string }) => void) => {
    const subscription = (_: any, data: { message: string, detail: string }) => callback(data)
    ipcRenderer.on(PROCESSING_EVENTS.CONFIG_INIT_FAILED, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.CONFIG_INIT_FAILED, subscription)
    }
  },
  onConfigUpdated: (callback: (data: { success: boolean, message: string }) => void) => {
    const subscription = (_: any, data: { success: boolean, message: string }) => callback(data)
    ipcRenderer.on("config-updated", subscription)
    return () => {
      ipcRenderer.removeListener("config-updated", subscription)
    }
  },

  onProblemExtracted: (callback: (data: any) => void) => {
    const subscription = (_: any, data: any) => callback(data)
    ipcRenderer.on(PROCESSING_EVENTS.PROBLEM_EXTRACTED, subscription)
    return () => {
      ipcRenderer.removeListener(
        PROCESSING_EVENTS.PROBLEM_EXTRACTED,
        subscription
      )
    }
  },
  onSolutionSuccess: (callback: (data: any) => void) => {
    const subscription = (_: any, data: any) => callback(data)
    ipcRenderer.on(PROCESSING_EVENTS.SOLUTION_SUCCESS, subscription)
    return () => {
      ipcRenderer.removeListener(
        PROCESSING_EVENTS.SOLUTION_SUCCESS,
        subscription
      )
    }
  },
  onFirstPointRecorded: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("first-point-recorded", subscription)
    return () => {
      ipcRenderer.removeListener("first-point-recorded", subscription)
    }
  },
  onUnauthorized: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.UNAUTHORIZED, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.UNAUTHORIZED, subscription)
    }
  },
  onApiKeyOutOfCredits: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.API_KEY_OUT_OF_CREDITS, subscription)
    return () => {
      ipcRenderer.removeListener(
        PROCESSING_EVENTS.API_KEY_OUT_OF_CREDITS,
        subscription
      )
    }
  },
  onCopySolutionContent: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("copy-solution-content", subscription)
    return () => {
      ipcRenderer.removeListener("copy-solution-content", subscription)
    }
  },
  onModelChanged: (callback: (event: any, data: any) => void) => {
    const subscription = (event: any, data: any) => callback(event, data)
    ipcRenderer.on("model-changed", subscription)
    return () => {
      ipcRenderer.removeListener("model-changed", subscription)
    }
  },
  onCodeLanguageChanged: (callback: (event: any, data: any) => void) => {
    const subscription = (event: any, data: any) => callback(event, data)
    ipcRenderer.on("code-language-changed", subscription)
    return () => {
      ipcRenderer.removeListener("code-language-changed", subscription)
    }
  },
  onWindowMoved: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("window-moved", subscription)
    return () => {
      ipcRenderer.removeListener("window-moved", subscription)
    }
  },
  onToggleVoiceAssistant: (callback: (event: any, data: any) => void) => {
    const subscription = (event: any, data: any) => callback(event, data)
    ipcRenderer.on("toggle-voice-assistant", subscription)
    return () => {
      ipcRenderer.removeListener("toggle-voice-assistant", subscription)
    }
  },
  onNavigateToVoiceView: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("navigate-to-voice-view", subscription)
    return () => {
      ipcRenderer.removeListener("navigate-to-voice-view", subscription)
    }
  },
  onStartVoiceRecognition: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("start-microphone-recognition", subscription)
    return () => {
      ipcRenderer.removeListener("start-microphone-recognition", subscription)
    }
  },
  onAsrTranscription: (callback: (event: any, data: any) => void) => {
    const subscription = (event: any, data: any) => callback(event, data)
    ipcRenderer.on("asr:transcription", subscription)
    return () => {
      ipcRenderer.removeListener("asr:transcription", subscription)
    }
  },
  onAsrConnectionStatus: (callback: (event: any, data: any) => void) => {
    const subscription = (event: any, data: any) => callback(event, data)
    ipcRenderer.on("asr:connection-status", subscription)
    return () => {
      ipcRenderer.removeListener("asr:connection-status", subscription)
    }
  },
  onAsrError: (callback: (event: any, data: any) => void) => {
    const subscription = (event: any, data: any) => callback(event, data)
    ipcRenderer.on("asr:error", subscription)
    return () => {
      ipcRenderer.removeListener("asr:error", subscription)
    }
  },
  onMicrophoneStatus: (callback: (event: any, data: any) => void) => {
    const subscription = (event: any, data: { recording: boolean }) => callback(event, data)
    ipcRenderer.on("microphone:status", subscription)
    return () => {
      ipcRenderer.removeListener("microphone:status", subscription)
    }
  },
  updateApiKey: (apiKey: string) =>
    ipcRenderer.invoke("update-api-key", apiKey),

  setApiKey: (apiKey: string) => ipcRenderer.invoke("set-api-key", apiKey),

  openExternal: (url: string) => {
    shell.openExternal(url)
  },

  moveWindowLeft: () => ipcRenderer.invoke("move-window-left"),
  moveWindowRight: () => ipcRenderer.invoke("move-window-right"),

  updateToolbarBounds: (bounds: { x: number; y: number; width: number; height: number }) => 
    ipcRenderer.invoke("update-toolbar-bounds", bounds),
  
  updateToolbarRegions: (regions: Array<{ x: number; y: number; width: number; height: number; type: 'model' | 'reset' | 'capture' | 'solution' | 'other' }>) => 
    ipcRenderer.invoke("update-toolbar-regions", regions),
  
  processVoiceToText: (text: string) => ipcRenderer.invoke("process-voice-to-text", text),
  processFastVoiceText: (text: string) => ipcRenderer.invoke("process-fast-voice-text", text),
  processAccurateVoiceText: (text: string) => ipcRenderer.invoke("process-accurate-voice-text", text),
  
  // 系统音频捕获相关API
  startSystemAudioCapturing: () => ipcRenderer.invoke("system-audio:start-capturing"),
  stopSystemAudioCapturing: () => ipcRenderer.invoke("system-audio:stop-capturing"),
  getSystemAudioStatus: () => ipcRenderer.invoke("system-audio:get-status"),
  onSystemAudioStatus: (callback: (event: any, data: { recording: boolean }) => void) => {
    const subscription = (event: any, data: { recording: boolean }) => callback(event, data)
    ipcRenderer.on("system-audio:status", subscription)
    return () => {
      ipcRenderer.removeListener("system-audio:status", subscription)
    }
  },
  
  // 麦克风音频捕获相关API
  startMicrophoneCapturing: () => ipcRenderer.invoke("microphone:start-capturing"),
  stopMicrophoneCapturing: () => ipcRenderer.invoke("microphone:stop-capturing"),
  processMicrophoneAudio: (audioData: any) => ipcRenderer.invoke("microphone:process-audio", audioData),
  getMicrophoneStatus: () => ipcRenderer.invoke("microphone:get-status"),
  
  // 语音识别流程统一控制API
  startVoiceRecognition: (enableMicrophone: boolean = true) => ipcRenderer.invoke("voice-recognition:start", enableMicrophone),
  stopVoiceRecognition: () => ipcRenderer.invoke("voice-recognition:stop"),
  
  onHistoryUpdate: (callback: any) => {
    ipcRenderer.on('asr:history-update', callback);
    return () => {
      ipcRenderer.removeListener('asr:history-update', callback);
    };
  },

  onUnifiedHistoryUpdate: (callback: any) => {
    ipcRenderer.on('asr:unified-history-update', callback);
    return () => {
      ipcRenderer.removeListener('asr:unified-history-update', callback);
    };
  },
  
  // 添加流式处理方法
  processFastVoiceTextStream: (text: string) => ipcRenderer.invoke("process-fast-voice-text-stream", text),
  processAccurateVoiceTextStream: (text: string) => ipcRenderer.invoke("process-accurate-voice-text-stream", text),
  
  // 添加流式响应事件监听
  onFastVoiceTextChunk: (callback: (event: any, data: { id: string, chunk: string, done: boolean, error?: boolean }) => void) => {
    const subscription = (event: any, data: { id: string, chunk: string, done: boolean, error?: boolean }) => callback(event, data)
    ipcRenderer.on("fast-voice-text-chunk", subscription)
    return () => {
      ipcRenderer.removeListener("fast-voice-text-chunk", subscription)
    }
  },
  
  onAccurateVoiceTextChunk: (callback: (event: any, data: { id: string, chunk: string, done: boolean, error?: boolean }) => void) => {
    const subscription = (event: any, data: { id: string, chunk: string, done: boolean, error?: boolean }) => callback(event, data)
    ipcRenderer.on("accurate-voice-text-chunk", subscription)
    return () => {
      ipcRenderer.removeListener("accurate-voice-text-chunk", subscription)
    }
  },

  // 配置相关API实现
  shouldShowMicrophoneButton: () => ipcRenderer.invoke("should-show-microphone-button"),

  // Voice panel button event listeners
  onVoiceBackClicked: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("voice-back-clicked", subscription)
    return () => {
      ipcRenderer.removeListener("voice-back-clicked", subscription)
    }
  },
  onVoiceMicrophoneClicked: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("voice-microphone-clicked", subscription)
    return () => {
      ipcRenderer.removeListener("voice-microphone-clicked", subscription)
    }
  },
  onVoiceSystemAudioClicked: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("voice-system-audio-clicked", subscription)
    return () => {
      ipcRenderer.removeListener("voice-system-audio-clicked", subscription)
    }
  },
  onVoiceOneClickStartClicked: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("voice-one-click-start-clicked", subscription)
    return () => {
      ipcRenderer.removeListener("voice-one-click-start-clicked", subscription)
    }
  },
  onVoiceSendToAIClicked: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("voice-send-to-ai-clicked", subscription)
    return () => {
      ipcRenderer.removeListener("voice-send-to-ai-clicked", subscription)
    }
  },
} as ElectronAPI)

// Add this focus restoration handler
ipcRenderer.on("restore-focus", () => {
  // Try to focus the active element if it exists
  const activeElement = document.activeElement as HTMLElement
  if (activeElement && typeof activeElement.focus === "function") {
    activeElement.focus()
  }
})
