"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requestPermissions = requestPermissions;
const electron_1 = require("electron");
const permissions_1 = require("../system-audio-loopback/utils/permissions");
async function requestPermissions() {
    if (process.platform === 'darwin') {
        // macOS
        // 检查辅助功能权限
        if (!electron_1.systemPreferences.isTrustedAccessibilityClient(false)) {
            electron_1.systemPreferences.isTrustedAccessibilityClient(true);
            electron_1.shell.openExternal('x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility');
        }
        // 检查麦克风权限
        let microphoneStatus = electron_1.systemPreferences.getMediaAccessStatus('microphone');
        if (microphoneStatus !== 'granted') {
            try {
                // 尝试直接请求麦克风权限
                const granted = await electron_1.systemPreferences.askForMediaAccess('microphone');
                microphoneStatus = granted ? 'granted' : electron_1.systemPreferences.getMediaAccessStatus('microphone');
            }
            catch (error) {
                console.error('Failed to request microphone access:', error);
            }
            if (microphoneStatus !== 'granted') {
                // 打开系统设置
                electron_1.shell.openExternal('x-apple.systempreferences:com.apple.preference.security?Privacy_Microphone');
            }
        }
        // 检查屏幕录制权限
        let screenStatus = await (0, permissions_1.checkRecordingPermissions)();
        if (!screenStatus) {
            try {
                const sources = await electron_1.desktopCapturer.getSources({ types: ['screen'] });
                console.log('获取屏幕源成功', sources);
            }
            catch (error) {
                console.error('Failed to request screen access:', error);
            }
        }
    }
    else if (process.platform === 'win32') {
        // Windows
        // const response = await dialog.showMessageBox({
        //   message: '需要麦克风和屏幕录制权限才能正常使用。请在系统设置中手动授予权限。',
        //   buttons: ['打开系统设置', '取消']
        // });
        // if (response.response === 0) {
        //   // 打开系统设置 (Windows)
        //   shell.openExternal('ms-settings:privacy-microphone');
        //   shell.openExternal('ms-settings:privacy-screenrecording');
        // }
    }
}
