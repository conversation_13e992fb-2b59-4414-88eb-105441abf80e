
> secure-kernel@1.1.3 dev
> cross-env NODE_ENV=development vite

[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m

  VITE v5.4.19  ready in 7183 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
  ➜  press h + enter to show help
vite v5.4.19 building for development...

watching for file changes...
vite v5.4.19 building for development...

watching for file changes...

build started...

build started...
transforming...
transforming...
✓ 1 modules transformed.
rendering chunks...
computing gzip size...
dist-electron/preload.js  14.49 kB │ gzip: 2.37 kB
built in 86ms.
✓ 776 modules transformed.
rendering chunks...
[plugin:vite:reporter] [plugin vite:reporter] 
(!) /Users/<USER>/Desktop/coder-master/electron/store.ts is dynamically imported by /Users/<USER>/Desktop/coder-master/electron/main.ts but also statically imported by /Users/<USER>/Desktop/coder-master/electron/MouseTrackingHelper.ts, /Users/<USER>/Desktop/coder-master/electron/WindowHelper.ts, /Users/<USER>/Desktop/coder-master/electron/handlers/MicrophoneASRManager.ts, /Users/<USER>/Desktop/coder-master/electron/handlers/SystemASRManager.ts, /Users/<USER>/Desktop/coder-master/electron/handlers/problemHandler.ts, /Users/<USER>/Desktop/coder-master/electron/ipcHandlers.ts, dynamic import will not move module into another chunk.

[plugin:vite:reporter] [plugin vite:reporter] 
(!) /Users/<USER>/Desktop/coder-master/electron/handlers/MicrophoneASRManager.ts is dynamically imported by /Users/<USER>/Desktop/coder-master/electron/ipc/voiceHandlers.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts but also statically imported by /Users/<USER>/Desktop/coder-master/electron/handlers/MicrophoneManager.ts, dynamic import will not move module into another chunk.

[plugin:vite:reporter] [plugin vite:reporter] 
(!) /Users/<USER>/Desktop/coder-master/electron/handlers/MicrophoneManager.ts is dynamically imported by /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts but also statically imported by /Users/<USER>/Desktop/coder-master/electron/ipc/microphoneHandlers.ts, /Users/<USER>/Desktop/coder-master/electron/ipc/voiceHandlers.ts, dynamic import will not move module into another chunk.

[plugin:vite:reporter] [plugin vite:reporter] 
(!) /Users/<USER>/Desktop/coder-master/electron/handlers/SystemASRManager.ts is dynamically imported by /Users/<USER>/Desktop/coder-master/electron/ipc/voiceHandlers.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts but also statically imported by /Users/<USER>/Desktop/coder-master/electron/handlers/SystemAudioManager.ts, dynamic import will not move module into another chunk.

[plugin:vite:reporter] [plugin vite:reporter] 
(!) /Users/<USER>/Desktop/coder-master/electron/handlers/SystemAudioManager.ts is dynamically imported by /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts but also statically imported by /Users/<USER>/Desktop/coder-master/electron/ipc/systemAudioHandlers.ts, /Users/<USER>/Desktop/coder-master/electron/ipc/voiceHandlers.ts, dynamic import will not move module into another chunk.

[plugin:vite:reporter] [plugin vite:reporter] 
(!) /Users/<USER>/Desktop/coder-master/electron/handlers/problemHandler.ts is dynamically imported by /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts, /Users/<USER>/Desktop/coder-master/electron/main.ts but also statically imported by /Users/<USER>/Desktop/coder-master/electron/ProcessingHelper.ts, /Users/<USER>/Desktop/coder-master/electron/ipc/voiceHandlers.ts, dynamic import will not move module into another chunk.

computing gzip size...
dist-electron/UnifiedHistoryManager-C2gkAgoH.js      7.84 kB │ gzip:   2.63 kB
dist-electron/main.js                            1,221.49 kB │ gzip: 251.78 kB
built in 2800ms.
从配置中读取模型列表: [
  'doubao-pro',
  'doubao-thinking',
  'deepseek-v3',
  'deepseek-r1',
  'gemini-2.5',
  'claude',
  'grok-4'
]
WebSocket module loaded successfully for MicrophoneASR
MicrophoneASRManager instance created
MicrophoneASR: 静音检测状态已重置
MicrophoneASR: 静音检测已启用 - 阈值=0.01, 最小持续时间=500ms
MicrophoneManager: 初始化麦克风管理器
WebSocket module loaded successfully for SystemASR
SystemASRManager instance created
Setting up app.whenReady() handler...
Main.ts loaded, app state: { isReady: false, version: '29.4.6' }
App 'ready' event fired
Electron app is ready, starting initialization...
initializeApp: Starting app initialization...
initializeApp: Creating app state...
所有快捷键已注销
快捷键 Alt+C 注册成功，已阻止事件传播
快捷键 Alt+V 注册成功，已阻止事件传播
快捷键 Alt+A 注册成功，已阻止事件传播
快捷键 CommandOrControl+Left 注册成功，已阻止事件传播
快捷键 CommandOrControl+Right 注册成功，已阻止事件传播
快捷键 CommandOrControl+Down 注册成功，已阻止事件传播
快捷键 CommandOrControl+Up 注册成功，已阻止事件传播
快捷键 CommandOrControl+B 注册成功，已阻止事件传播
快捷键 Alt+Z 注册成功，已阻止事件传播
快捷键 Alt+2 注册成功，已阻止事件传播
快捷键 Alt+3 注册成功，已阻止事件传播
快捷键 Alt+1 注册成功，已阻止事件传播
快捷键 CommandOrControl+Q 注册成功，已阻止事件传播
所有快捷键注册成功
键盘钩子已激活
设置 macOS 键盘钩子
注册系统级键盘监听器
添加键盘钩子: Alt+C
添加键盘钩子: Alt+Z
添加键盘钩子: Alt+1
添加键盘钩子: Alt+2
添加键盘钩子: Alt+3
添加键盘钩子: Alt+V
添加键盘钩子: Alt+A
添加键盘钩子: CommandOrControl+R
添加键盘钩子: CommandOrControl+Left
添加键盘钩子: CommandOrControl+Right
添加键盘钩子: CommandOrControl+Down
添加键盘钩子: CommandOrControl+Up
添加键盘钩子: CommandOrControl+B
添加键盘钩子: CommandOrControl+Q
系统级键盘拦截已设置
ShortcutsHelper initialized
鼠标点击事件监听设置完成
ioHook started
initializeApp: Creating main window...
初始化SystemAudioManager...
初始化MicrophoneManager...
初始化UnifiedHistoryManager...
注册语音处理IPC处理器...
注册语音助手 IPC 处理程序...
initializeApp: Initializing IPC handlers...
initializeApp: 注册全局快捷键...
重新注册全局快捷键
快捷键 Alt+C 已注销
快捷键 Alt+V 已注销
快捷键 Alt+A 已注销
快捷键 CommandOrControl+Left 已注销
快捷键 CommandOrControl+Right 已注销
快捷键 CommandOrControl+Down 已注销
快捷键 CommandOrControl+Up 已注销
快捷键 CommandOrControl+B 已注销
快捷键 Alt+Z 已注销
快捷键 Alt+2 已注销
快捷键 Alt+3 已注销
快捷键 Alt+1 已注销
快捷键 CommandOrControl+Q 已注销
所有快捷键已注销
快捷键 Alt+C 注册成功，已阻止事件传播
快捷键 Alt+V 注册成功，已阻止事件传播
快捷键 Alt+A 注册成功，已阻止事件传播
快捷键 CommandOrControl+Left 注册成功，已阻止事件传播
快捷键 CommandOrControl+Right 注册成功，已阻止事件传播
快捷键 CommandOrControl+Down 注册成功，已阻止事件传播
快捷键 CommandOrControl+Up 注册成功，已阻止事件传播
快捷键 CommandOrControl+B 注册成功，已阻止事件传播
快捷键 Alt+Z 注册成功，已阻止事件传播
快捷键 Alt+2 注册成功，已阻止事件传播
快捷键 Alt+3 注册成功，已阻止事件传播
快捷键 Alt+1 注册成功，已阻止事件传播
快捷键 CommandOrControl+Q 注册成功，已阻止事件传播
所有快捷键注册成功
initializeApp: 等待窗口加载完成后检查配置...
initializeApp: 窗口正在加载，等待加载完成...
UnifiedHistoryManager: 实例已创建
SystemAudioManager: 主窗口已设置
注册系统音频IPC处理器...
SystemAudioManager: 主窗口已设置
移除已存在的system-audio:start-capturing处理器
移除已存在的system-audio:stop-capturing处理器
移除已存在的system-audio:get-status处理器
系统音频相关的IPC处理器已注册
MicrophoneManager: 主窗口已设置
注册麦克风IPC处理器...
MicrophoneManager: 主窗口已设置
移除已存在的microphone:start-capturing处理器
移除已存在的microphone:stop-capturing处理器
移除已存在的microphone:get-status处理器
移除已存在的microphone:process-audio处理器
麦克风相关的IPC处理器已注册
UnifiedHistoryManager: 🧹 设置新主窗口，清空历史记录
UnifiedHistoryManager: 🚀 发送统一历史更新，共 0 条记录 {}
UnifiedHistoryManager: 历史记录已清空
UnifiedHistoryManager 已初始化并设置主窗口
2025-07-30 11:08:39.477 Electron[85790:60409603] +[IMKClient subclass]: chose IMKClient_Modern
2025-07-30 11:08:39.544 Electron[85790:60409603] +[IMKInputSession subclass]: chose IMKInputSession_Modern
initializeApp: 窗口加载完成，开始配置检查
从配置中读取编程语言列表: [
  'Java',    'Python',
  'Python3', 'JavaScript',
  'C++',     'C',
  'Go',      'SQL',
  'Ruby',    'Typescript',
  'Kotlin',  'Swift',
  'Rust'
]
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3',
      'deepseek-r1',
      'gemini-2.5',
      'claude',
      'grok-4'
    ],
    codeLanguages: [
      'Java',    'Python',
      'Python3', 'JavaScript',
      'C++',     'C',
      'Go',      'SQL',
      'Ruby',    'Typescript',
      'Kotlin',  'Swift',
      'Rust'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
initializeApp: 开始检查并初始化配置...
checkAndInitializeConfig called
检测到已存储的 API Key，开始初始化配置...
开始初始化配置... sk-1192bc4bdb2e4fa5993198aeac83f010
从配置中读取编程语言列表: [
  'Java',    'Python',
  'Python3', 'JavaScript',
  'C++',     'C',
  'Go',      'SQL',
  'Ruby',    'Typescript',
  'Kotlin',  'Swift',
  'Rust'
]
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3',
      'deepseek-r1',
      'gemini-2.5',
      'claude',
      'grok-4'
    ],
    codeLanguages: [
      'Java',    'Python',
      'Python3', 'JavaScript',
      'C++',     'C',
      'Go',      'SQL',
      'Ruby',    'Typescript',
      'Kotlin',  'Swift',
      'Rust'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
工具条位置已更新: { x: 16, y: 12, width: 517, height: 49 }
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'model', x: 257, y: 24, width: 80, height: 10 },
    { type: 'code-language', x: 353, y: 24, width: 47, height: 10 },
    { type: 'reset', x: 116, y: 19, width: 83, height: 19 },
    { type: 'capture', x: 17, y: 21, width: 66, height: 16 },
    { type: 'solution', x: 442, y: 23, width: 58, height: 11 }
  ]
}
configResult: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3',
      'deepseek-r1',
      'gemini-2.5',
      'claude',
      'grok-4'
    ],
    codeLanguages: [
      'Java',    'Python',
      'Python3', 'JavaScript',
      'C++',     'C',
      'Go',      'SQL',
      'Ruby',    'Typescript',
      'Kotlin',  'Swift',
      'Rust'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
配置初始化成功
通知渲染进程更新麦克风按钮状态...
✅ 配置更新事件已发送到渲染进程
initializeApp: 开始请求系统权限...
initializeApp: 权限请求完成
initializeApp: 应用初始化完成
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3',
      'deepseek-r1',
      'gemini-2.5',
      'claude',
      'grok-4'
    ],
    codeLanguages: [
      'Java',    'Python',
      'Python3', 'JavaScript',
      'C++',     'C',
      'Go',      'SQL',
      'Ruby',    'Typescript',
      'Kotlin',  'Swift',
      'Rust'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3',
      'deepseek-r1',
      'gemini-2.5',
      'claude',
      'grok-4'
    ],
    codeLanguages: [
      'Java',    'Python',
      'Python3', 'JavaScript',
      'C++',     'C',
      'Go',      'SQL',
      'Ruby',    'Typescript',
      'Kotlin',  'Swift',
      'Rust'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
工具条位置已更新: { x: 16, y: 12, width: 598, height: 49 }
📍 工具条区域已更新: {
  regionsCount: 6,
  regions: [
    { type: 'model', x: 257, y: 24, width: 89, height: 10 },
    { type: 'code-language', x: 363, y: 24, width: 59, height: 10 },
    { type: 'reset', x: 116, y: 19, width: 83, height: 19 },
    { type: 'capture', x: 17, y: 21, width: 66, height: 16 },
    { type: 'solution', x: 462, y: 23, width: 58, height: 11 },
    { type: 'voice', x: 542, y: 21, width: 46, height: 16 }
  ]
}
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3',
      'deepseek-r1',
      'gemini-2.5',
      'claude',
      'grok-4'
    ],
    codeLanguages: [
      'Java',    'Python',
      'Python3', 'JavaScript',
      'C++',     'C',
      'Go',      'SQL',
      'Ruby',    'Typescript',
      'Kotlin',  'Swift',
      'Rust'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
🖱️ 检测到鼠标点击事件: { x: 592, y: 37, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 592, y: 37 },
  windowBounds: { x: 0, y: 0, width: 646, height: 73 },
  toolbarBounds: { x: 16, y: 12, width: 598, height: 49 },
  regionsCount: 6
}
使用渲染进程报告的工具条位置: { x: 16, y: 12, width: 598, height: 49 }
鼠标位置 (592, 37) 在工具条范围内
点击在 voice 区域内
Voice区域被点击，显示语音识别界面
工具条位置已更新: { x: 5, y: 1, width: 764, height: 749 }
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 704, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 106, y: 59, width: 28, height: 28 },
    { type: 'voice-system-audio', x: 70, y: 59, width: 28, height: 28 },
    {
      type: 'voice-one-click-start',
      x: 142,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 174, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 704, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 106, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 70, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 142,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 174, height: 36, type: 'voice-send-to-ai' }
]
📍 更新AI回复框位置信息: {
  fastResponse: { x: 196, y: 50, width: 289, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 485,
    y: 50,
    width: 285,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 204, y: 50, width: 301, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 505,
    y: 50,
    width: 297,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 204, y: 50, width: 301, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 505,
    y: 50,
    width: 297,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 220, y: 50, width: 325, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 545,
    y: 50,
    width: 321,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 228, y: 50, width: 337, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 565,
    y: 50,
    width: 333,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 244, y: 50, width: 361, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 605,
    y: 50,
    width: 357,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 244, y: 50, width: 361, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 605,
    y: 50,
    width: 357,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 260, y: 50, width: 385, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 645,
    y: 50,
    width: 381,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 260, y: 50, width: 385, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 645,
    y: 50,
    width: 381,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 260, y: 50, width: 385, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 645,
    y: 50,
    width: 381,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 276, y: 50, width: 409, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 685,
    y: 50,
    width: 405,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 284, y: 50, width: 421, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 705,
    y: 50,
    width: 417,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 292, y: 50, width: 433, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 725,
    y: 50,
    width: 429,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 300, y: 50, width: 445, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 745,
    y: 50,
    width: 441,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 308, y: 50, width: 457, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 765,
    y: 50,
    width: 453,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 316, y: 50, width: 469, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 785,
    y: 50,
    width: 465,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 324, y: 50, width: 481, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 805,
    y: 50,
    width: 477,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 332, y: 50, width: 493, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 825,
    y: 50,
    width: 489,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 332, y: 50, width: 493, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 825,
    y: 50,
    width: 489,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 340, y: 50, width: 505, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 845,
    y: 50,
    width: 501,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 356, y: 50, width: 529, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 885,
    y: 50,
    width: 525,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 364, y: 50, width: 541, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 905,
    y: 50,
    width: 537,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 380, y: 50, width: 565, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 945,
    y: 50,
    width: 561,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 380, y: 50, width: 565, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 945,
    y: 50,
    width: 561,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 388, y: 50, width: 577, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 965,
    y: 50,
    width: 573,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 396, y: 50, width: 589, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 985,
    y: 50,
    width: 585,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 406, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1004,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 422, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1020,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 419, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1017,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 438, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1036,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 435, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1033,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
工具条位置已更新: { x: 37, y: 1, width: 1590, height: 749 }
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
📍 更新AI回复框位置信息: {
  fastResponse: { x: 435, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1033,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 454, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1052,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 451, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1049,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 470, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1068,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 470, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1068,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 467, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1065,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 483, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1081,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 499, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1097,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 499, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1097,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 499, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1097,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 499, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1097,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
工具条位置已更新: { x: 101, y: 1, width: 1590, height: 749 }
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🖱️ 检测到鼠标点击事件: { x: 470, y: 76, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 470, y: 76 },
  windowBounds: { x: 0, y: 0, width: 1792, height: 751 },
  toolbarBounds: { x: 101, y: 1, width: 1590, height: 749 },
  regionsCount: 5
}
使用渲染进程报告的工具条位置: { x: 101, y: 1, width: 1590, height: 749 }
鼠标位置 (470, 76) 在工具条范围内
点击在 voice-one-click-start 区域内
语音面板一键启动按钮被点击
🚀 执行语音识别流程启动: 系统音频=true, 麦克风=true
📡 步骤1: 启动系统音频ASR连接...
Starting SystemASR session for system audio
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
SystemASRManager: 语音配置加载成功 {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
SystemASRManager: 系统音频识别启用状态: true
SystemASR: Starting connection attempt (ID: 144803ce-f10b-4c6c-8957-dd33c2a324f2)
SystemASR: WebSocket connection established
SystemASR: 发送初始化消息 (bigmodel_async)
连接状态更新: systemASR = true {
  systemASR: true,
  microphoneASR: false,
  systemAudio: false,
  microphone: false
}
✅ 系统音频ASR连接成功
SystemASR: 构建消息 - 序列号=1, 载荷大小=183, 总大小=195
SystemASR: 发送配置消息，序列号=1, 消息大小=195
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x0',
  serialization: 1,
  compression: 0,
  rawBytes: '11901000000000487b22726573756c74'
}
SystemASR: 初始载荷长度: 76
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: { additions: { log_id: '202507301108479A1993F0EA20ABA39668' } },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"}}
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
📡 启动系统音频捕获...
SystemAudioManager: 开始启动系统音频捕获
SystemAudioManager: 第一步 - 启动SystemASR服务
Starting SystemASR session for system audio
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
SystemASRManager: 系统音频识别启用状态: true
SystemASR: Active WebSocket connection already exists
SystemAudioManager: SystemASR服务启动成功
SystemAudioManager: 第二步 - 启动系统音频捕获
SystemAudioManager: macOS音频捕获命令: /Users/<USER>/Desktop/coder-master/bin/macos/system-audio-capture --output /Users/<USER>/Library/Application Support/secure-kernel/temp/system-audio.pcm
SystemAudioManager: 启动音频捕获进程... (尝试 1/4)
SystemAudioManager: 音频捕获进程已启动
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
SystemAudioManager: stdout: {"code":"STREAM_FUNCTION_NOT_CALLED","message":"没有检测到音频流"}

SystemAudioManager: 收到未预期的响应代码: STREAM_FUNCTION_NOT_CALLED
SystemAudioManager: 将在 200ms 后尝试重新启动录音 (尝试 1/5)
SystemAudioManager: 音频捕获进程退出，代码: 0, 信号: null
SystemAudioManager: 正在重新启动录音 (尝试 1/5)...
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
SystemAudioManager (重试): stdout: {"sampleRate":48000,"format":"PCM 16-bit","timestamp":"2025-07-30T03:08:52Z","code":"RECORDING_STARTED","path":"\/Users\/<USER>\/Library\/Application Support\/secure-kernel\/temp\/system-audio.pcm","channels":2}

SystemAudioManager: 录音已开始，文件保存在: /Users/<USER>/Library/Application Support/secure-kernel/temp/system-audio.pcm
macOS音频参数: 采样率=48000, 通道数=2, 位数=16
SystemAudioManager: 输出文件大小: 4096字节
开始监控PCM文件 (安全版本): /Users/<USER>/Library/Application Support/secure-kernel/temp/system-audio.pcm
采用安全读取机制，避免读写竞争
位置指针管理器已重置
启动音频文件监控，首次读取将在200ms后开始
连接状态更新: systemAudio = true {
  systemASR: true,
  microphoneASR: false,
  systemAudio: true,
  microphone: false
}
✅ 系统音频捕获启动成功
🎤 步骤2: 启动麦克风ASR连接...
🎤 Starting MicrophoneASR session for microphone audio
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
MicrophoneASRManager: 语音配置加载成功 {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
MicrophoneASRManager: 麦克风音频识别启用状态: true
MicrophoneASR: Starting connection attempt (ID: a95499e5-856e-4e93-bd10-e1999bfd951e)
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
MicrophoneASR: WebSocket connection established
MicrophoneASR: 使用固定采样率 16000Hz 进行配置
MicrophoneASR: 发送初始化消息 (与websocket-record-rtc.js一致)
MicrophoneASR: 发送配置消息，消息大小=279字节
连接状态更新: microphoneASR = true {
  systemASR: true,
  microphoneASR: true,
  systemAudio: true,
  microphone: false
}
✅ 麦克风ASR连接成功
MicrophoneASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x0',
  serialization: 1,
  compression: 0,
  rawBytes: '11901000000000487b22726573756c74'
}
MicrophoneASR: 初始载荷长度: 76
MicrophoneASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
MicrophoneASR: 处理转录结果 {
  result: { additions: { log_id: '2025073011085253B5DFDFD90F6BA408ED' } },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"2025073011085253B5DFDFD90F6BA408ED"}}
安全读取: 位置=0, 文件大小=57856, 待读取=57856字节
帧处理: 总数据=57856字节, 完整帧=57856字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57856字节, 缓存完整帧=0字节, 有效帧=57856字节, 新不完整帧=0字节
当前文件位置: 57856字节, 总读取: 0字节
发送音频数据: 9642字节, 4821样本, 能量=509.89, 静音=false
SystemASR: 处理音频数据，长度=9642字节, 采样率=16000Hz
安全读取并处理 57856 字节音频数据，新位置: 57856
SystemASR: 构建消息 - 序列号=2, 载荷大小=1476, 总大小=1488
SystemASR: 发送音频数据，序列号=2, 总大小=1488字节
安全读取: 位置=57856, 文件大小=119296, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 119296字节, 总读取: 57856字节
发送音频数据: 10240字节, 5120样本, 能量=0.98, 静音=true
安全读取并处理 61440 字节音频数据，新位置: 119296
SystemASR: 构建消息 - 序列号=3, 载荷大小=1676, 总大小=1688
SystemASR: 发送音频数据，序列号=3, 总大小=1688字节
安全读取: 位置=119296, 文件大小=180736, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到静音开始
当前文件位置: 180736字节, 总读取: 119296字节
发送音频数据: 10240字节, 5120样本, 能量=0.84, 静音=true
安全读取并处理 61440 字节音频数据，新位置: 180736
SystemASR: 构建消息 - 序列号=4, 载荷大小=1720, 总大小=1732
SystemASR: 发送音频数据，序列号=4, 总大小=1732字节
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎤 启动麦克风捕获...
🎤 MicrophoneManager: 开始启动麦克风音频捕获
🎤 MicrophoneManager: 第一步 - 启动MicrophoneASRManager服务
🎤 Starting MicrophoneASR session for microphone audio
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
MicrophoneASRManager: 麦克风音频识别启用状态: true
✅ MicrophoneASR: Active WebSocket connection already exists
🎤 MicrophoneManager: MicrophoneASRManager启动结果: { success: true }
✅ MicrophoneManager: MicrophoneASRManager服务启动成功
🎤 MicrophoneManager: 第二步 - 启动麦克风音频捕获
📡 MicrophoneManager: 发送麦克风状态到渲染进程
✅ MicrophoneManager: 已通知渲染进程录制开始
✅ MicrophoneManager: 麦克风音频捕获启动成功
连接状态更新: microphone = true {
  systemASR: true,
  microphoneASR: true,
  systemAudio: true,
  microphone: true
}
✅ 麦克风捕获启动成功
📡 发送 start-microphone-recognition 事件到渲染进程
🎯 语音识别流程启动结果: 整体=true, 系统音频=true, 麦克风=true
📊 连接状态: {
  systemASR: true,
  microphoneASR: true,
  systemAudio: true,
  microphone: true
}
安全读取: 位置=180736, 文件大小=242176, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 242176字节, 总读取: 180736字节
发送音频数据: 10240字节, 5120样本, 能量=0.60, 静音=true
安全读取并处理 61440 字节音频数据，新位置: 242176
SystemASR: 构建消息 - 序列号=5, 载荷大小=1503, 总大小=1515
SystemASR: 发送音频数据，序列号=5, 总大小=1515字节
安全读取: 位置=242176, 文件大小=303616, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到静音结束，重置音频特征
当前文件位置: 303616字节, 总读取: 242176字节
发送音频数据: 10240字节, 5120样本, 能量=1177.58, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 303616
SystemASR: 构建消息 - 序列号=6, 载荷大小=8733, 总大小=8745
SystemASR: 发送音频数据，序列号=6, 总大小=8745字节
安全读取: 位置=303616, 文件大小=365056, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 365056字节, 总读取: 303616字节
发送音频数据: 10240字节, 5120样本, 能量=2099.07, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 365056
SystemASR: 构建消息 - 序列号=7, 载荷大小=9457, 总大小=9469
SystemASR: 发送音频数据，序列号=7, 总大小=9469字节
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
IPC: 收到启动麦克风音频捕获请求
🎤 MicrophoneManager: 开始启动麦克风音频捕获
🎤 MicrophoneManager: 麦克风已在录制中
IPC: 收到启动麦克风音频捕获请求
🎤 MicrophoneManager: 开始启动麦克风音频捕获
🎤 MicrophoneManager: 麦克风已在录制中
安全读取: 位置=365056, 文件大小=430336, 待读取=65280字节
帧处理: 总数据=65280字节, 完整帧=65280字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=65280字节, 缓存完整帧=0字节, 有效帧=65280字节, 新不完整帧=0字节
当前文件位置: 430336字节, 总读取: 365056字节
发送音频数据: 10880字节, 5440样本, 能量=2230.02, 静音=false
安全读取并处理 65280 字节音频数据，新位置: 430336
SystemASR: 构建消息 - 序列号=8, 载荷大小=10066, 总大小=10078
SystemASR: 发送音频数据，序列号=8, 总大小=10078字节
[85932:0730/110854.690387:ERROR:system_services.cc(34)] SetApplicationIsDaemon: Error Domain=NSOSStatusErrorDomain Code=-50 "paramErr: error in user parameter list" (-50)
安全读取: 位置=430336, 文件大小=480256, 待读取=49920字节
帧处理: 总数据=49920字节, 完整帧=49920字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=49920字节, 缓存完整帧=0字节, 有效帧=49920字节, 新不完整帧=0字节
当前文件位置: 480256字节, 总读取: 430336字节
发送音频数据: 8320字节, 4160样本, 能量=2956.56, 静音=false
SystemASR: 发送缓冲的音频数据，大小=8320字节
安全读取并处理 49920 字节音频数据，新位置: 480256
SystemASR: 构建消息 - 序列号=9, 载荷大小=7823, 总大小=7835
SystemASR: 发送音频数据，序列号=9, 总大小=7835字节
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000001000000ab7b226175'
}
SystemASR: 初始载荷长度: 179
SystemASR: 解析序列号
SystemASR: 序列号: 1 剩余载荷长度: 175
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"utterances":[{"definite":false,"end_time":1742,"start_time":1662}]}
SystemASR: 处理utterances转录 { count: 1, currentText: 'undefined...', historyCount: 0 }
安全读取: 位置=480256, 文件大小=545536, 待读取=65280字节
帧处理: 总数据=65280字节, 完整帧=65280字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=65280字节, 缓存完整帧=0字节, 有效帧=65280字节, 新不完整帧=0字节
当前文件位置: 545536字节, 总读取: 480256字节
发送音频数据: 10880字节, 5440样本, 能量=2005.19, 静音=false
安全读取并处理 65280 字节音频数据，新位置: 545536
SystemASR: 构建消息 - 序列号=10, 载荷大小=9873, 总大小=9885
SystemASR: 发送音频数据，序列号=10, 总大小=9885字节
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000002000001367b226175'
}
SystemASR: 初始载荷长度: 318
SystemASR: 解析序列号
SystemASR: 序列号: 2 剩余载荷长度: 314
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'Series.',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"Series.","utterances":[{"definite":false,"end_time":2222,"start_time":1682,"text":"Series.","words":[{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2222,"start_time":2142,"text":"Series"}]}]}
SystemASR: 处理utterances转录 { count: 1, currentText: 'Series....', historyCount: 0 }
SystemASR: 当前转录 { text: 'Series.', isFinal: false, startTime: 1682, endTime: 2222 }
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
安全读取: 位置=545536, 文件大小=606976, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 606976字节, 总读取: 545536字节
发送音频数据: 10240字节, 5120样本, 能量=1805.86, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 606976
SystemASR: 构建消息 - 序列号=11, 载荷大小=9382, 总大小=9394
SystemASR: 发送音频数据，序列号=11, 总大小=9394字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0145, Max=0.0478, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844935557 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=00 00 00 00 00 00 00 00
MicrophoneASR: 音频数据分析 - 非零字节: 3660/5462 (67.01%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [0, 0, 0, 0]
MicrophoneASR: 前8字节原始数据: 00 00 00 00 00 00 00 00
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0145, Max=0.0478, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844935558 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000003000001767b226175'
}
SystemASR: 初始载荷长度: 382
SystemASR: 解析序列号
SystemASR: 序列号: 3 剩余载荷长度: 378
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来","utterances":[{"definite":false,"end_time":2622,"start_time":1522,"text":"series 未来","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2622,"start_time":2542,"text":"未来"}]}]}
SystemASR: 处理utterances转录 { count: 1, currentText: 'series 未来...', historyCount: 0 }
SystemASR: 当前转录 { text: 'series 未来', isFinal: false, startTime: 1522, endTime: 2622 }
安全读取: 位置=606976, 文件大小=668416, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 668416字节, 总读取: 606976字节
发送音频数据: 10240字节, 5120样本, 能量=1635.17, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 668416
SystemASR: 构建消息 - 序列号=12, 载荷大小=9295, 总大小=9307
SystemASR: 发送音频数据，序列号=12, 总大小=9307字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0118, Max=0.0425, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844935761 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=45 fd 65 fd b4 fd 8f fd
MicrophoneASR: 音频数据分析 - 非零字节: 4724/5462 (86.49%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-699, -667, -588, -625]
MicrophoneASR: 前8字节原始数据: 45 fd 65 fd b4 fd 8f fd
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0108, Max=0.0405, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844935763 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000004000001f67b226175'
}
SystemASR: 初始载荷长度: 510
SystemASR: 解析序列号
SystemASR: 序列号: 4 剩余载荷长度: 506
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户","utterances":[{"definite":false,"end_time":3022,"start_time":1682,"text":"series 未来方便用户","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":3022,"start_time":2942,"text":"用户"}]}]}
SystemASR: 处理utterances转录 { count: 1, currentText: 'series 未来方便用户...', historyCount: 0 }
SystemASR: 当前转录 {
  text: 'series 未来方便用户',
  isFinal: false,
  startTime: 1682,
  endTime: 3022
}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0043, Max=0.0239, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844935969 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=c1 02 0b 03 1c fe 77 ff
MicrophoneASR: 音频数据分析 - 非零字节: 4130/5462 (75.61%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [705, 779, -484, -137]
MicrophoneASR: 前8字节原始数据: c1 02 0b 03 1c fe 77 ff
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0053, Max=0.0313, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844935970 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=668416, 文件大小=729856, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 729856字节, 总读取: 668416字节
发送音频数据: 10240字节, 5120样本, 能量=550.32, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 729856
SystemASR: 构建消息 - 序列号=13, 载荷大小=8399, 总大小=8411
SystemASR: 发送音频数据，序列号=13, 总大小=8411字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0042, Max=0.0233, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844936175 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=a6 ff 79 ff 8d ff a8 ff
MicrophoneASR: 音频数据分析 - 非零字节: 4159/5462 (76.14%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-90, -135, -115, -88]
MicrophoneASR: 前8字节原始数据: a6 ff 79 ff 8d ff a8 ff
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0042, Max=0.0304, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844936175 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000005000002367b226175'
}
SystemASR: 初始载荷长度: 574
SystemASR: 解析序列号
SystemASR: 序列号: 5 剩余载荷长度: 570
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发","utterances":[{"definite":false,"end_time":3262,"start_time":1682,"text":"series 未来方便用户开发","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3262,"start_time":3182,"text":"开发"}]}]}
SystemASR: 处理utterances转录 { count: 1, currentText: 'series 未来方便用户开发...', historyCount: 0 }
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发',
  isFinal: false,
  startTime: 1682,
  endTime: 3262
}
安全读取: 位置=729856, 文件大小=791296, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 791296字节, 总读取: 729856字节
发送音频数据: 10240字节, 5120样本, 能量=1609.15, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 791296
SystemASR: 构建消息 - 序列号=14, 载荷大小=9259, 总大小=9271
SystemASR: 发送音频数据，序列号=14, 总大小=9271字节
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0052, Max=0.0446, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844936382 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=31 00 48 00 23 00 eb ff
MicrophoneASR: 音频数据分析 - 非零字节: 6319/8192 (77.14%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [49, 72, 35, -21]
MicrophoneASR: 前8字节原始数据: 31 00 48 00 23 00 eb ff
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0055, Max=0.0465, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844936384 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0034, Max=0.0172, 阈值=0.01, 静音=true
🔇 MicrophoneASR: 检测到静音 (连续1帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续2帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '11911000000000060000026d7b226175'
}
SystemASR: 初始载荷长度: 629
SystemASR: 解析序列号
SystemASR: 序列号: 6 剩余载荷长度: 625
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发还',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发还","utterances":[{"definite":false,"end_time":3582,"start_time":1682,"text":"series 未来方便用户开发还","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3582,"start_time":3502,"text":"还"}]}]}
SystemASR: 处理utterances转录 { count: 1, currentText: 'series 未来方便用户开发还...', historyCount: 0 }
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发还',
  isFinal: false,
  startTime: 1682,
  endTime: 3582
}
安全读取: 位置=791296, 文件大小=852736, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 852736字节, 总读取: 791296字节
发送音频数据: 10240字节, 5120样本, 能量=1947.64, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 852736
SystemASR: 构建消息 - 序列号=15, 载荷大小=9611, 总大小=9623
SystemASR: 发送音频数据，序列号=15, 总大小=9623字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0139, Max=0.1094, 阈值=0.01, 静音=false
🔊 MicrophoneASR: 检测到声音，结束静音状态 (之前连续2帧静音)
🎵 MicrophoneASR: 1753844936796 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=09 00 88 ff 64 ff 7e ff
MicrophoneASR: 音频数据分析 - 非零字节: 6987/8192 (85.29%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [9, -120, -156, -130]
MicrophoneASR: 前8字节原始数据: 09 00 88 ff 64 ff 7e ff
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0137, Max=0.0895, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844936797 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000007000002ea7b226175'
}
SystemASR: 初始载荷长度: 754
SystemASR: 解析序列号
SystemASR: 序列号: 7 剩余载荷长度: 750
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了","utterances":[{"definite":false,"end_time":3902,"start_time":1682,"text":"series 未来方便用户开发，还提供了","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3902,"start_time":3822,"text":"了"}]}]}
SystemASR: 处理utterances转录 { count: 1, currentText: 'series 未来方便用户开发，还提供了...', historyCount: 0 }
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了',
  isFinal: false,
  startTime: 1682,
  endTime: 3902
}
安全读取: 位置=852736, 文件大小=910336, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
当前文件位置: 910336字节, 总读取: 852736字节
发送音频数据: 9600字节, 4800样本, 能量=1565.40, 静音=false
安全读取并处理 57600 字节音频数据，新位置: 910336
SystemASR: 构建消息 - 序列号=16, 载荷大小=8610, 总大小=8622
SystemASR: 发送音频数据，序列号=16, 总大小=8622字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0033, Max=0.0138, 阈值=0.01, 静音=true
🔇 MicrophoneASR: 检测到静音 (连续1帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续2帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0073, Max=0.0276, 阈值=0.01, 静音=false
🔊 MicrophoneASR: 检测到声音，结束静音状态 (之前连续2帧静音)
🎵 MicrophoneASR: 1753844937209 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=e9 ff 9d ff b0 ff 75 00
MicrophoneASR: 音频数据分析 - 非零字节: 4464/5462 (81.73%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-23, -99, -80, 117]
MicrophoneASR: 前8字节原始数据: e9 ff 9d ff b0 ff 75 00
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0073, Max=0.0282, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844937210 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000008000003587b226175'
}
SystemASR: 初始载荷长度: 864
SystemASR: 解析序列号
SystemASR: 序列号: 8 剩余载荷长度: 860
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个","utterances":[{"definite":false,"end_time":4142,"start_time":1682,"text":"series 未来方便用户开发，还提供了一个","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4142,"start_time":4061,"text":"个"}]}]}
SystemASR: 处理utterances转录 { count: 1, currentText: 'series 未来方便用户开发，还提供了一个...', historyCount: 0 }
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个',
  isFinal: false,
  startTime: 1682,
  endTime: 4142
}
安全读取: 位置=910336, 文件大小=971776, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 971776字节, 总读取: 910336字节
发送音频数据: 10240字节, 5120样本, 能量=1559.09, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 971776
SystemASR: 构建消息 - 序列号=17, 载荷大小=9322, 总大小=9334
SystemASR: 发送音频数据，序列号=17, 总大小=9334字节
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0031, Max=0.0114, 阈值=0.01, 静音=true
🔇 MicrophoneASR: 检测到静音 (连续1帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续2帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000009000003987b226175'
}
SystemASR: 初始载荷长度: 928
SystemASR: 解析序列号
SystemASR: 序列号: 9 剩余载荷长度: 924
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地","utterances":[{"definite":false,"end_time":4542,"start_time":1682,"text":"series 未来方便用户开发，还提供了一个本地","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4542,"start_time":4462,"text":"本地"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地',
  isFinal: false,
  startTime: 1682,
  endTime: 4542
}
安全读取: 位置=971776, 文件大小=1033216, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 1033216字节, 总读取: 971776字节
发送音频数据: 10240字节, 5120样本, 能量=485.36, 静音=false
SystemASR: 处理音频数据，长度=10240字节, 采样率=16000Hz
安全读取并处理 61440 字节音频数据，新位置: 1033216
SystemASR: 构建消息 - 序列号=18, 载荷大小=7716, 总大小=7728
SystemASR: 发送音频数据，序列号=18, 总大小=7728字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续3帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续4帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续5帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续6帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000000a000004187b226175'
}
SystemASR: 初始载荷长度: 1056
SystemASR: 解析序列号
SystemASR: 序列号: 10 剩余载荷长度: 1052
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式","utterances":[{"definite":false,"end_time":4942,"start_time":1682,"text":"series 未来方便用户开发，还提供了一个本地开发模式","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4942,"start_time":4862,"text":"模式"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式',
  isFinal: false,
  startTime: 1682,
  endTime: 4942
}
安全读取: 位置=1033216, 文件大小=1094656, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 1094656字节, 总读取: 1033216字节
发送音频数据: 10240字节, 5120样本, 能量=38.41, 静音=true
安全读取并处理 61440 字节音频数据，新位置: 1094656
SystemASR: 构建消息 - 序列号=19, 载荷大小=6702, 总大小=6714
SystemASR: 发送音频数据，序列号=19, 总大小=6714字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续7帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续8帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1094656, 文件大小=1148416, 待读取=53760字节
帧处理: 总数据=53760字节, 完整帧=53760字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=53760字节, 缓存完整帧=0字节, 有效帧=53760字节, 新不完整帧=0字节
当前文件位置: 1148416字节, 总读取: 1094656字节
发送音频数据: 8960字节, 4480样本, 能量=629.52, 静音=false
安全读取并处理 53760 字节音频数据，新位置: 1148416
SystemASR: 构建消息 - 序列号=20, 载荷大小=5553, 总大小=5565
SystemASR: 发送音频数据，序列号=20, 总大小=5565字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续9帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0745, Max=0.6624, 阈值=0.01, 静音=false
🔊 MicrophoneASR: 检测到声音，结束静音状态 (之前连续9帧静音)
🎵 MicrophoneASR: 1753844938243 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=ce ff f7 ff 3e 00 3d 00
MicrophoneASR: 音频数据分析 - 非零字节: 6617/8192 (80.77%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [-50, -9, 62, 61]
MicrophoneASR: 前8字节原始数据: ce ff f7 ff 3e 00 3d 00
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0823, Max=0.3553, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844938452 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=73 dc 7a dc 1c f7 ff ee
MicrophoneASR: 音频数据分析 - 非零字节: 5362/5462 (98.17%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-9101, -9094, -2276, -4353]
MicrophoneASR: 前8字节原始数据: 73 dc 7a dc 1c f7 ff ee
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.1010, Max=0.6814, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844938453 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1148416, 文件大小=1213696, 待读取=65280字节
帧处理: 总数据=65280字节, 完整帧=65280字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=65280字节, 缓存完整帧=0字节, 有效帧=65280字节, 新不完整帧=0字节
当前文件位置: 1213696字节, 总读取: 1148416字节
发送音频数据: 10880字节, 5440样本, 能量=1228.62, 静音=false
安全读取并处理 65280 字节音频数据，新位置: 1213696
SystemASR: 构建消息 - 序列号=21, 载荷大小=9623, 总大小=9635
SystemASR: 发送音频数据，序列号=21, 总大小=9635字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0224, Max=0.1375, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844938657 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=29 0b 2c 0c e2 0e ea 10
MicrophoneASR: 音频数据分析 - 非零字节: 4438/5462 (81.25%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [2857, 3116, 3810, 4330]
MicrophoneASR: 前8字节原始数据: 29 0b 2c 0c e2 0e ea 10
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0165, Max=0.1135, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844938657 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1213696, 文件大小=1275136, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 1275136字节, 总读取: 1213696字节
发送音频数据: 10240字节, 5120样本, 能量=3431.16, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 1275136
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0033, Max=0.0171, 阈值=0.01, 静音=true
🔇 MicrophoneASR: 检测到静音 (连续1帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续2帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 构建消息 - 序列号=22, 载荷大小=9625, 总大小=9637
SystemASR: 发送音频数据，序列号=22, 总大小=9637字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续3帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续4帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000000b000004187b226175'
}
SystemASR: 初始载荷长度: 1056
SystemASR: 解析序列号
SystemASR: 序列号: 11 剩余载荷长度: 1052
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式","utterances":[{"definite":false,"end_time":6062,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式',
  isFinal: false,
  startTime: 1522,
  endTime: 6062
}
安全读取: 位置=1275136, 文件大小=1332736, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
当前文件位置: 1332736字节, 总读取: 1275136字节
发送音频数据: 9600字节, 4800样本, 能量=2302.36, 静音=false
安全读取并处理 57600 字节音频数据，新位置: 1332736
SystemASR: 构建消息 - 序列号=23, 载荷大小=8884, 总大小=8896
SystemASR: 发送音频数据，序列号=23, 总大小=8896字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0088, Max=0.0414, 阈值=0.01, 静音=false
🔊 MicrophoneASR: 检测到声音，结束静音状态 (之前连续4帧静音)
🎵 MicrophoneASR: 1753844939277 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=23 00 7e 00 ad 00 de 00
MicrophoneASR: 音频数据分析 - 非零字节: 4467/5462 (81.78%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [35, 126, 173, 222]
MicrophoneASR: 前8字节原始数据: 23 00 7e 00 ad 00 de 00
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0075, Max=0.0385, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844939277 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000000c000004557b226175'
}
SystemASR: 初始载荷长度: 1117
SystemASR: 解析序列号
SystemASR: 序列号: 12 剩余载荷长度: 1113
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他","utterances":[{"definite":false,"end_time":6462,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6462,"start_time":6382,"text":"他"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他',
  isFinal: false,
  startTime: 1522,
  endTime: 6462
}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0033, Max=0.0150, 阈值=0.01, 静音=true
🔇 MicrophoneASR: 检测到静音 (连续1帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续2帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1332736, 文件大小=1394176, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 1394176字节, 总读取: 1332736字节
发送音频数据: 10240字节, 5120样本, 能量=1516.63, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 1394176
SystemASR: 构建消息 - 序列号=24, 载荷大小=9129, 总大小=9141
SystemASR: 发送音频数据，序列号=24, 总大小=9141字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续3帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续4帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000000d000004957b226175'
}
SystemASR: 初始载荷长度: 1181
SystemASR: 解析序列号
SystemASR: 序列号: 13 剩余载荷长度: 1177
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式。他意思',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式。他意思","utterances":[{"definite":false,"end_time":6782,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式。他意思","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6782,"start_time":6702,"text":"意思"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式。他意思...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式。他意思',
  isFinal: false,
  startTime: 1522,
  endTime: 6782
}
安全读取: 位置=1394176, 文件大小=1455616, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 1455616字节, 总读取: 1394176字节
发送音频数据: 10240字节, 5120样本, 能量=1090.62, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 1455616
SystemASR: 构建消息 - 序列号=25, 载荷大小=7660, 总大小=7672
SystemASR: 发送音频数据，序列号=25, 总大小=7672字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续5帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续6帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000000e000004d57b226175'
}
SystemASR: 初始载荷长度: 1245
SystemASR: 解析序列号
SystemASR: 序列号: 14 剩余载荷长度: 1241
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是","utterances":[{"definite":false,"end_time":7102,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":7102,"start_time":7022,"text":"就是"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是',
  isFinal: false,
  startTime: 1522,
  endTime: 7102
}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0066, Max=0.0524, 阈值=0.01, 静音=false
🔊 MicrophoneASR: 检测到声音，结束静音状态 (之前连续6帧静音)
🎵 MicrophoneASR: 1753844940104 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=11 00 00 00 ef ff e8 ff
MicrophoneASR: 音频数据分析 - 非零字节: 4248/5462 (77.77%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [17, 0, -17, -24]
MicrophoneASR: 前8字节原始数据: 11 00 00 00 ef ff e8 ff
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0066, Max=0.0524, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844940105 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1455616, 文件大小=1517056, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 1517056字节, 总读取: 1455616字节
发送音频数据: 10240字节, 5120样本, 能量=1178.08, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 1517056
SystemASR: 构建消息 - 序列号=26, 载荷大小=9031, 总大小=9043
SystemASR: 发送音频数据，序列号=26, 总大小=9043字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0107, Max=0.0664, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844940311 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=5c ff 10 ff de fe d9 fe
MicrophoneASR: 音频数据分析 - 非零字节: 6862/8192 (83.76%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [-164, -240, -290, -295]
MicrophoneASR: 前8字节原始数据: 5c ff 10 ff de fe d9 fe
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0107, Max=0.0664, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844940313 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
安全读取: 位置=1517056, 文件大小=1574656, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
当前文件位置: 1574656字节, 总读取: 1517056字节
发送音频数据: 9600字节, 4800样本, 能量=2650.60, 静音=false
安全读取并处理 57600 字节音频数据，新位置: 1574656
SystemASR: 构建消息 - 序列号=27, 载荷大小=8925, 总大小=8937
SystemASR: 发送音频数据，序列号=27, 总大小=8937字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0121, Max=0.0546, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844940519 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=41 fe 93 fd 08 ff 3e ff
MicrophoneASR: 音频数据分析 - 非零字节: 4776/5462 (87.44%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-447, -621, -248, -194]
MicrophoneASR: 前8字节原始数据: 41 fe 93 fd 08 ff 3e ff
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0121, Max=0.0546, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844940520 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000000f0000050c7b226175'
}
SystemASR: 初始载荷长度: 1300
SystemASR: 解析序列号
SystemASR: 序列号: 15 剩余载荷长度: 1296
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你","utterances":[{"definite":false,"end_time":7822,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7822,"start_time":7742,"text":"你"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你',
  isFinal: false,
  startTime: 1522,
  endTime: 7822
}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0099, Max=0.0630, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844940725 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=8a fd 35 fd c1 fd eb fd
MicrophoneASR: 音频数据分析 - 非零字节: 4568/5462 (83.63%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-630, -715, -575, -533]
MicrophoneASR: 前8字节原始数据: 8a fd 35 fd c1 fd eb fd
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0099, Max=0.0630, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844940726 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1574656, 文件大小=1636096, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 1636096字节, 总读取: 1574656字节
发送音频数据: 10240字节, 5120样本, 能量=1160.93, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 1636096
SystemASR: 构建消息 - 序列号=28, 载荷大小=8914, 总大小=8926
SystemASR: 发送音频数据，序列号=28, 总大小=8926字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0070, Max=0.0310, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844940932 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=c6 fd 5d 00 8b 00 57 fe
MicrophoneASR: 音频数据分析 - 非零字节: 6632/8192 (80.96%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [-570, 93, 139, -425]
MicrophoneASR: 前8字节原始数据: c6 fd 5d 00 8b 00 57 fe
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0070, Max=0.0310, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844940933 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '11911000000000100000054c7b226175'
}
SystemASR: 初始载荷长度: 1364
SystemASR: 解析序列号
SystemASR: 序列号: 16 剩余载荷长度: 1360
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以","utterances":[{"definite":false,"end_time":7902,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7902,"start_time":7822,"text":"可以"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以',
  isFinal: false,
  startTime: 1522,
  endTime: 7902
}
安全读取: 位置=1636096, 文件大小=1701376, 待读取=65280字节
帧处理: 总数据=65280字节, 完整帧=65280字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=65280字节, 缓存完整帧=0字节, 有效帧=65280字节, 新不完整帧=0字节
当前文件位置: 1701376字节, 总读取: 1636096字节
发送音频数据: 10880字节, 5440样本, 能量=2087.00, 静音=false
安全读取并处理 65280 字节音频数据，新位置: 1701376
SystemASR: 构建消息 - 序列号=29, 载荷大小=9822, 总大小=9834
SystemASR: 发送音频数据，序列号=29, 总大小=9834字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0023, Max=0.0091, 阈值=0.01, 静音=true
🔇 MicrophoneASR: 检测到静音 (连续1帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续2帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0208, Max=0.2076, 阈值=0.01, 静音=false
🔊 MicrophoneASR: 检测到声音，结束静音状态 (之前连续2帧静音)
🎵 MicrophoneASR: 1753844941344 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=3e 00 1f 00 29 00 79 00
MicrophoneASR: 音频数据分析 - 非零字节: 6515/8192 (79.53%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [62, 31, 41, 121]
MicrophoneASR: 前8字节原始数据: 3e 00 1f 00 29 00 79 00
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0206, Max=0.2055, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844941344 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '11911000000000110000058c7b226175'
}
SystemASR: 初始载荷长度: 1428
SystemASR: 解析序列号
SystemASR: 序列号: 17 剩余载荷长度: 1424
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置","utterances":[{"definite":false,"end_time":8462,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8462,"start_time":8382,"text":"配置"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置',
  isFinal: false,
  startTime: 1522,
  endTime: 8462
}
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
安全读取: 位置=1701376, 文件大小=1762816, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 1762816字节, 总读取: 1701376字节
发送音频数据: 10240字节, 5120样本, 能量=1880.03, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 1762816
SystemASR: 构建消息 - 序列号=30, 载荷大小=9331, 总大小=9343
SystemASR: 发送音频数据，序列号=30, 总大小=9343字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0137, Max=0.0877, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844941550 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=e7 04 14 ff 2d f8 85 f5
MicrophoneASR: 音频数据分析 - 非零字节: 4468/5462 (81.80%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [1255, -236, -2003, -2683]
MicrophoneASR: 前8字节原始数据: e7 04 14 ff 2d f8 85 f5
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0129, Max=0.0932, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844941551 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1762816, 文件大小=1824256, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 1824256字节, 总读取: 1762816字节
发送音频数据: 10240字节, 5120样本, 能量=1321.54, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 1824256
SystemASR: 构建消息 - 序列号=31, 载荷大小=8930, 总大小=8942
SystemASR: 发送音频数据，序列号=31, 总大小=8942字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0052, Max=0.0522, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844941756 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=fc ff d6 ff c0 ff d0 ff
MicrophoneASR: 音频数据分析 - 非零字节: 4146/5462 (75.91%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-4, -42, -64, -48]
MicrophoneASR: 前8字节原始数据: fc ff d6 ff c0 ff d0 ff
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0054, Max=0.0473, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844941756 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000012000005fa7b226175'
}
SystemASR: 初始载荷长度: 1538
SystemASR: 解析序列号
SystemASR: 序列号: 18 剩余载荷长度: 1534
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在","utterances":[{"definite":false,"end_time":8782,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8782,"start_time":8702,"text":"在"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在',
  isFinal: false,
  startTime: 1522,
  endTime: 8782
}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0067, Max=0.0580, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844941960 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=db ff 3c 00 56 ff eb ff
MicrophoneASR: 音频数据分析 - 非零字节: 4350/5462 (79.64%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-37, 60, -170, -21]
MicrophoneASR: 前8字节原始数据: db ff 3c 00 56 ff eb ff
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0067, Max=0.0648, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844941961 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1824256, 文件大小=1885696, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 1885696字节, 总读取: 1824256字节
发送音频数据: 10240字节, 5120样本, 能量=1735.61, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 1885696
SystemASR: 构建消息 - 序列号=32, 载荷大小=9191, 总大小=9203
SystemASR: 发送音频数据，序列号=32, 总大小=9203字节
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '11911000000000130000067a7b226175'
}
SystemASR: 初始载荷长度: 1666
SystemASR: 解析序列号
SystemASR: 序列号: 19 剩余载荷长度: 1662
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发","utterances":[{"definite":false,"end_time":9022,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":9022,"start_time":8942,"text":"开发"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发',
  isFinal: false,
  startTime: 1522,
  endTime: 9022
}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0211, Max=0.0739, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844942167 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=ff ff ef ff a2 ff 3f ff
MicrophoneASR: 音频数据分析 - 非零字节: 4847/5462 (88.74%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-1, -17, -94, -193]
MicrophoneASR: 前8字节原始数据: ff ff ef ff a2 ff 3f ff
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0212, Max=0.0806, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844942168 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000014000006f17b226175'
}
SystemASR: 初始载荷长度: 1785
SystemASR: 解析序列号
SystemASR: 序列号: 20 剩余载荷长度: 1781
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候","utterances":[{"definite":false,"end_time":9342,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9342,"start_time":9262,"text":"时候"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候',
  isFinal: false,
  startTime: 1522,
  endTime: 9342
}
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
安全读取: 位置=1885696, 文件大小=1950976, 待读取=65280字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0071, Max=0.0625, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844942373 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=ad 01 78 01 e8 01 c8 01
MicrophoneASR: 音频数据分析 - 非零字节: 6540/8192 (79.83%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [429, 376, 488, 456]
MicrophoneASR: 前8字节原始数据: ad 01 78 01 e8 01 c8 01
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0072, Max=0.0613, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844942374 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
帧处理: 总数据=65280字节, 完整帧=65280字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=65280字节, 缓存完整帧=0字节, 有效帧=65280字节, 新不完整帧=0字节
当前文件位置: 1950976字节, 总读取: 1885696字节
发送音频数据: 10880字节, 5440样本, 能量=359.77, 静音=false
安全读取并处理 65280 字节音频数据，新位置: 1950976
音频监控统计 - 运行时间: 10.2s, 总读取: 32, 成功: 32, 空读: 0, 错误: 0
数据统计 - 总字节: 1950976, 平均速率: 191987 bytes/s, 不完整帧缓存: 0字节
SystemASR: 构建消息 - 序列号=33, 载荷大小=7392, 总大小=7404
SystemASR: 发送音频数据，序列号=33, 总大小=7404字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0058, Max=0.0271, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844942581 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=67 00 38 00 33 00 34 00
MicrophoneASR: 音频数据分析 - 非零字节: 4260/5462 (77.99%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [103, 56, 51, 52]
MicrophoneASR: 前8字节原始数据: 67 00 38 00 33 00 34 00
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0055, Max=0.0250, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844942582 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000015000007327b226175'
}
SystemASR: 初始载荷长度: 1850
SystemASR: 解析序列号
SystemASR: 序列号: 21 剩余载荷长度: 1846
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以","utterances":[{"definite":false,"end_time":9502,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9502,"start_time":9422,"text":"可以"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以',
  isFinal: false,
  startTime: 1522,
  endTime: 9502
}
安全读取: 位置=1950976, 文件大小=2012416, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 2012416字节, 总读取: 1950976字节
发送音频数据: 10240字节, 5120样本, 能量=258.92, 静音=false
SystemASR: 处理音频数据，长度=10240字节, 采样率=16000Hz
安全读取并处理 61440 字节音频数据，新位置: 2012416
SystemASR: 构建消息 - 序列号=34, 载荷大小=6012, 总大小=6024
SystemASR: 发送音频数据，序列号=34, 总大小=6024字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0072, Max=0.0331, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844942789 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=79 03 a1 03 4b 03 f5 02
MicrophoneASR: 音频数据分析 - 非零字节: 6460/8192 (78.86%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [889, 929, 843, 757]
MicrophoneASR: 前8字节原始数据: 79 03 a1 03 4b 03 f5 02
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0072, Max=0.0331, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844942789 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000016000007787b226175'
}
SystemASR: 初始载荷长度: 1920
SystemASR: 解析序列号
SystemASR: 序列号: 22 剩余载荷长度: 1916
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置，你在本地开发的时候可以配置',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置，你在本地开发的时候可以配置","utterances":[{"definite":false,"end_time":9822,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置，你在本地开发的时候可以配置","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9822,"start_time":9742,"text":"配置"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置，你在本地开发的时候可...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置，你在本地开发的时候可...',
  isFinal: false,
  startTime: 1522,
  endTime: 9822
}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0027, Max=0.0132, 阈值=0.01, 静音=true
🔇 MicrophoneASR: 检测到静音 (连续1帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续2帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2012416, 文件大小=2073856, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 2073856字节, 总读取: 2012416字节
发送音频数据: 10240字节, 5120样本, 能量=1311.99, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 2073856
SystemASR: 构建消息 - 序列号=35, 载荷大小=9112, 总大小=9124
SystemASR: 发送音频数据，序列号=35, 总大小=9124字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续3帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续4帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2073856, 文件大小=2135296, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 2135296字节, 总读取: 2073856字节
发送音频数据: 10240字节, 5120样本, 能量=357.90, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 2135296
SystemASR: 构建消息 - 序列号=36, 载荷大小=5499, 总大小=5511
SystemASR: 发送音频数据，序列号=36, 总大小=5511字节
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000017000007ac7b226175'
}
SystemASR: 初始载荷长度: 1972
SystemASR: 解析序列号
SystemASR: 序列号: 23 剩余载荷长度: 1968
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你","utterances":[{"definite":false,"end_time":10382,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10382,"start_time":10302,"text":"你"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 10382
}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0057, Max=0.0415, 阈值=0.01, 静音=false
🔊 MicrophoneASR: 检测到声音，结束静音状态 (之前连续4帧静音)
🎵 MicrophoneASR: 1753844943409 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=df ff 01 00 28 00 58 00
MicrophoneASR: 音频数据分析 - 非零字节: 6281/8192 (76.67%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [-33, 1, 40, 88]
MicrophoneASR: 前8字节原始数据: df ff 01 00 28 00 58 00
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0056, Max=0.0461, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844943410 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0035, Max=0.0156, 阈值=0.01, 静音=true
🔇 MicrophoneASR: 检测到静音 (连续1帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续2帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2135296, 文件大小=2196736, 待读取=61440字节
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000018000007e57b226175'
}
SystemASR: 初始载荷长度: 2029
SystemASR: 解析序列号
SystemASR: 序列号: 24 剩余载荷长度: 2025
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的","utterances":[{"definite":false,"end_time":10542,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10542,"start_time":10462,"text":"的"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 10542
}
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 2196736字节, 总读取: 2135296字节
发送音频数据: 10240字节, 5120样本, 能量=1838.53, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 2196736
SystemASR: 构建消息 - 序列号=37, 载荷大小=9367, 总大小=9379
SystemASR: 发送音频数据，序列号=37, 总大小=9379字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0054, Max=0.0348, 阈值=0.01, 静音=false
🔊 MicrophoneASR: 检测到声音，结束静音状态 (之前连续2帧静音)
🎵 MicrophoneASR: 1753844943824 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=61 00 4f 00 34 00 0a 00
MicrophoneASR: 音频数据分析 - 非零字节: 6371/8192 (77.77%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [97, 79, 52, 10]
MicrophoneASR: 前8字节原始数据: 61 00 4f 00 34 00 0a 00
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0054, Max=0.0337, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844943825 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2196736, 文件大小=2258176, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 2258176字节, 总读取: 2196736字节
发送音频数据: 10240字节, 5120样本, 能量=1735.04, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 2258176
SystemASR: 构建消息 - 序列号=38, 载荷大小=9272, 总大小=9284
SystemASR: 发送音频数据，序列号=38, 总大小=9284字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0041, Max=0.0359, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844944029 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=3d 00 28 00 23 00 66 00
MicrophoneASR: 音频数据分析 - 非零字节: 4162/5462 (76.20%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [61, 40, 35, 102]
MicrophoneASR: 前8字节原始数据: 3d 00 28 00 23 00 66 00
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0041, Max=0.0291, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844944029 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0079, Max=0.0448, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844944235 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=0c 01 ef 00 45 01 41 01
MicrophoneASR: 音频数据分析 - 非零字节: 4377/5462 (80.14%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [268, 239, 325, 321]
MicrophoneASR: 前8字节原始数据: 0c 01 ef 00 45 01 41 01
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0079, Max=0.0445, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844944235 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000019000008277b226175'
}
SystemASR: 初始载荷长度: 2095
SystemASR: 解析序列号
SystemASR: 序列号: 25 剩余载荷长度: 2091
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码","utterances":[{"definite":false,"end_time":11182,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11182,"start_time":11102,"text":"代码"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 11182
}
安全读取: 位置=2258176, 文件大小=2319616, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 2319616字节, 总读取: 2258176字节
发送音频数据: 10240字节, 5120样本, 能量=1830.24, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 2319616
SystemASR: 构建消息 - 序列号=39, 载荷大小=9404, 总大小=9416
SystemASR: 发送音频数据，序列号=39, 总大小=9416字节
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0109, Max=0.0723, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844944441 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=8f ff 97 ff 9b ff a0 ff
MicrophoneASR: 音频数据分析 - 非零字节: 6808/8192 (83.11%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [-113, -105, -101, -96]
MicrophoneASR: 前8字节原始数据: 8f ff 97 ff 9b ff a0 ff
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0109, Max=0.0723, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844944441 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000001a000008697b226175'
}
SystemASR: 初始载荷长度: 2161
SystemASR: 解析序列号
SystemASR: 序列号: 26 剩余载荷长度: 2157
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问","utterances":[{"definite":false,"end_time":11582,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11582,"start_time":11502,"text":"访问"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 11582
}
安全读取: 位置=2319616, 文件大小=2381056, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 2381056字节, 总读取: 2319616字节
发送音频数据: 10240字节, 5120样本, 能量=1200.69, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 2381056
SystemASR: 构建消息 - 序列号=40, 载荷大小=9083, 总大小=9095
SystemASR: 发送音频数据，序列号=40, 总大小=9095字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0102, Max=0.0465, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844944647 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=50 03 5f 00 63 00 f2 02
MicrophoneASR: 音频数据分析 - 非零字节: 4525/5462 (82.85%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [848, 95, 99, 754]
MicrophoneASR: 前8字节原始数据: 50 03 5f 00 63 00 f2 02
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0102, Max=0.0465, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844944649 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0091, Max=0.0811, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844944853 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=df ff 79 ff 8c ff 73 ff
MicrophoneASR: 音频数据分析 - 非零字节: 6736/8192 (82.23%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [-33, -135, -116, -141]
MicrophoneASR: 前8字节原始数据: df ff 79 ff 8c ff 73 ff
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0091, Max=0.0811, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844944854 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000001b000008a27b226175'
}
SystemASR: 初始载荷长度: 2218
SystemASR: 解析序列号
SystemASR: 序列号: 27 剩余载荷长度: 2214
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的","utterances":[{"definite":false,"end_time":11742,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11742,"start_time":11662,"text":"的"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 11742
}
安全读取: 位置=2381056, 文件大小=2442496, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 2442496字节, 总读取: 2381056字节
发送音频数据: 10240字节, 5120样本, 能量=1362.80, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 2442496
SystemASR: 构建消息 - 序列号=41, 载荷大小=9330, 总大小=9342
SystemASR: 发送音频数据，序列号=41, 总大小=9342字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0081, Max=0.0358, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844945059 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=a7 ff dd fe 10 fe 37 ff
MicrophoneASR: 音频数据分析 - 非零字节: 4464/5462 (81.73%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-89, -291, -496, -201]
MicrophoneASR: 前8字节原始数据: a7 ff dd fe 10 fe 37 ff
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0081, Max=0.0358, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844945059 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000001c0000090e7b226175'
}
SystemASR: 初始载荷长度: 2326
SystemASR: 解析序列号
SystemASR: 序列号: 28 剩余载荷长度: 2322
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis","utterances":[{"definite":false,"end_time":12062,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12062,"start_time":11982,"text":"Redis"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 12062
}
安全读取: 位置=2442496, 文件大小=2500096, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
当前文件位置: 2500096字节, 总读取: 2442496字节
发送音频数据: 9600字节, 4800样本, 能量=782.04, 静音=false
安全读取并处理 57600 字节音频数据，新位置: 2500096
SystemASR: 构建消息 - 序列号=42, 载荷大小=8082, 总大小=8094
SystemASR: 发送音频数据，序列号=42, 总大小=8094字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0081, Max=0.0555, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844945264 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=86 fe 19 ff cc 00 98 00
MicrophoneASR: 音频数据分析 - 非零字节: 6645/8192 (81.12%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [-378, -231, 204, 152]
MicrophoneASR: 前8字节原始数据: 86 fe 19 ff cc 00 98 00
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0081, Max=0.0555, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844945266 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0172, Max=0.0906, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844945471 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=91 ff 6b ff 41 00 11 00
MicrophoneASR: 音频数据分析 - 非零字节: 4709/5462 (86.21%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-111, -149, 65, 17]
MicrophoneASR: 前8字节原始数据: 91 ff 6b ff 41 00 11 00
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0172, Max=0.0906, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844945472 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000001d000009b67b226175'
}
SystemASR: 初始载荷长度: 2494
SystemASR: 解析序列号
SystemASR: 序列号: 29 剩余载荷长度: 2490
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例","utterances":[{"definite":false,"end_time":12622,"start_time":1682,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6402,"start_time":6322,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12622,"start_time":12542,"text":"实例"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1682,
  endTime: 12622
}
安全读取: 位置=2500096, 文件大小=2557696, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
当前文件位置: 2557696字节, 总读取: 2500096字节
发送音频数据: 9600字节, 4800样本, 能量=17.62, 静音=true
安全读取并处理 57600 字节音频数据，新位置: 2557696
SystemASR: 构建消息 - 序列号=43, 载荷大小=4936, 总大小=4948
SystemASR: 发送音频数据，序列号=43, 总大小=4948字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0243, Max=0.1392, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844945676 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=4b 00 6d 00 48 00 11 00
MicrophoneASR: 音频数据分析 - 非零字节: 4842/5462 (88.65%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [75, 109, 72, 17]
MicrophoneASR: 前8字节原始数据: 4b 00 6d 00 48 00 11 00
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0243, Max=0.1392, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844945676 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000001e00000a317b226175'
}
SystemASR: 初始载荷长度: 2617
SystemASR: 解析序列号
SystemASR: 序列号: 30 剩余载荷长度: 2613
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么","utterances":[{"definite":false,"end_time":12782,"start_time":1682,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6402,"start_time":6322,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12782,"start_time":12702,"text":"什么"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1682,
  endTime: 12782
}
安全读取: 位置=2557696, 文件大小=2619136, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 2619136字节, 总读取: 2557696字节
发送音频数据: 10240字节, 5120样本, 能量=1967.76, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 2619136
SystemASR: 构建消息 - 序列号=44, 载荷大小=9395, 总大小=9407
SystemASR: 发送音频数据，序列号=44, 总大小=9407字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0092, Max=0.0658, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844945880 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=ce 03 1e 08 c0 05 74 ff
MicrophoneASR: 音频数据分析 - 非零字节: 6525/8192 (79.65%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [974, 2078, 1472, -140]
MicrophoneASR: 前8字节原始数据: ce 03 1e 08 c0 05 74 ff
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0092, Max=0.0658, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844945882 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0038, Max=0.0402, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844946088 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=07 00 0b 00 00 00 09 00
MicrophoneASR: 音频数据分析 - 非零字节: 4114/5462 (75.32%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [7, 11, 0, 9]
MicrophoneASR: 前8字节原始数据: 07 00 0b 00 00 00 09 00
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0038, Max=0.0402, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844946089 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2619136, 文件大小=2680576, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 2680576字节, 总读取: 2619136字节
发送音频数据: 10240字节, 5120样本, 能量=1873.53, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 2680576
SystemASR: 构建消息 - 序列号=45, 载荷大小=9362, 总大小=9374
SystemASR: 发送音频数据，序列号=45, 总大小=9374字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0034, Max=0.0191, 阈值=0.01, 静音=true
🔇 MicrophoneASR: 检测到静音 (连续1帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续2帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000001f00000a797b226175'
}
SystemASR: 初始载荷长度: 2689
SystemASR: 解析序列号
SystemASR: 序列号: 31 剩余载荷长度: 2685
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么？这样',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么？这样","utterances":[{"definite":false,"end_time":13582,"start_time":1682,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么？这样","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6402,"start_time":6322,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13582,"start_time":13502,"text":"这样"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1682,
  endTime: 13582
}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续3帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续4帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2680576, 文件大小=2742016, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 2742016字节, 总读取: 2680576字节
发送音频数据: 10240字节, 5120样本, 能量=1067.27, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 2742016
SystemASR: 构建消息 - 序列号=46, 载荷大小=8973, 总大小=8985
SystemASR: 发送音频数据，序列号=46, 总大小=8985字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续5帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续6帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000002000000af47b226175'
}
SystemASR: 初始载荷长度: 2812
SystemASR: 解析序列号
SystemASR: 序列号: 32 剩余载荷长度: 2808
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么？这样的话就',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么？这样的话就","utterances":[{"definite":false,"end_time":13902,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么？这样的话就","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13902,"start_time":13822,"text":"就"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 13902
}
安全读取: 位置=2742016, 文件大小=2803456, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 2803456字节, 总读取: 2742016字节
发送音频数据: 10240字节, 5120样本, 能量=2317.45, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 2803456
SystemASR: 构建消息 - 序列号=47, 载荷大小=9457, 总大小=9469
SystemASR: 发送音频数据，序列号=47, 总大小=9469字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0108, Max=0.0554, 阈值=0.01, 静音=false
🔊 MicrophoneASR: 检测到声音，结束静音状态 (之前连续6帧静音)
🎵 MicrophoneASR: 1753844946913 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=20 00 94 ff b2 ff ef ff
MicrophoneASR: 音频数据分析 - 非零字节: 6902/8192 (84.25%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [32, -108, -78, -17]
MicrophoneASR: 前8字节原始数据: 20 00 94 ff b2 ff ef ff
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0108, Max=0.0554, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844946914 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000002100000ba87b226175'
}
SystemASR: 初始载荷长度: 2992
SystemASR: 解析序列号
SystemASR: 序列号: 33 剩余载荷长度: 2988
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你","utterances":[{"definite":false,"end_time":14142,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14142,"start_time":14062,"text":"你"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 14142
}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0061, Max=0.0408, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844947123 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=e7 00 f1 00 08 01 cf ff
MicrophoneASR: 音频数据分析 - 非零字节: 4317/5462 (79.04%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [231, 241, 264, -49]
MicrophoneASR: 前8字节原始数据: e7 00 f1 00 08 01 cf ff
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0061, Max=0.0408, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844947124 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2803456, 文件大小=2864896, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 2864896字节, 总读取: 2803456字节
发送音频数据: 10240字节, 5120样本, 能量=1530.56, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 2864896
SystemASR: 构建消息 - 序列号=48, 载荷大小=9383, 总大小=9395
SystemASR: 发送音频数据，序列号=48, 总大小=9395字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0237, Max=0.1228, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844947330 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=ce ff a1 ff bf ff bf ff
MicrophoneASR: 音频数据分析 - 非零字节: 6817/8192 (83.22%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [-50, -95, -65, -65]
MicrophoneASR: 前8字节原始数据: ce ff a1 ff bf ff bf ff
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0237, Max=0.1228, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844947331 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000002200000bea7b226175'
}
SystemASR: 初始载荷长度: 3058
SystemASR: 解析序列号
SystemASR: 序列号: 34 剩余载荷长度: 3054
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发","utterances":[{"definite":false,"end_time":14462,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14462,"start_time":14382,"text":"开发"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 14462
}
安全读取: 位置=2864896, 文件大小=2922496, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
当前文件位置: 2922496字节, 总读取: 2864896字节
发送音频数据: 9600字节, 4800样本, 能量=827.42, 静音=false
安全读取并处理 57600 字节音频数据，新位置: 2922496
SystemASR: 构建消息 - 序列号=49, 载荷大小=8393, 总大小=8405
SystemASR: 发送音频数据，序列号=49, 总大小=8405字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0143, Max=0.0492, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844947544 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=f9 fc 1c fd 4e fd 80 fd
MicrophoneASR: 音频数据分析 - 非零字节: 4587/5462 (83.98%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-775, -740, -690, -640]
MicrophoneASR: 前8字节原始数据: f9 fc 1c fd 4e fd 80 fd
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0143, Max=0.0492, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844947545 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000002300000c5c7b226175'
}
SystemASR: 初始载荷长度: 3172
SystemASR: 解析序列号
SystemASR: 序列号: 35 剩余载荷长度: 3168
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的","utterances":[{"definite":false,"end_time":14782,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14322,"start_time":14242,"text":"开发"},{"end_time":14562,"start_time":14482,"text":"用"},{"end_time":14782,"start_time":14702,"text":"的"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 14782
}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0051, Max=0.0234, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844947754 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=82 00 1c 00 14 00 19 00
MicrophoneASR: 音频数据分析 - 非零字节: 6462/8192 (78.88%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [130, 28, 20, 25]
MicrophoneASR: 前8字节原始数据: 82 00 1c 00 14 00 19 00
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0051, Max=0.0234, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844947756 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2922496, 文件大小=2983936, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 2983936字节, 总读取: 2922496字节
发送音频数据: 10240字节, 5120样本, 能量=1496.35, 静音=false
SystemASR: 处理音频数据，长度=10240字节, 采样率=16000Hz
安全读取并处理 61440 字节音频数据，新位置: 2983936
SystemASR: 构建消息 - 序列号=50, 载荷大小=9201, 总大小=9213
SystemASR: 发送音频数据，序列号=50, 总大小=9213字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0052, Max=0.0221, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844947959 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=e1 ff cb ff 27 00 14 00
MicrophoneASR: 音频数据分析 - 非零字节: 4295/5462 (78.63%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-31, -53, 39, 20]
MicrophoneASR: 前8字节原始数据: e1 ff cb ff 27 00 14 00
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0052, Max=0.0221, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844947960 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2983936, 文件大小=3045376, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 3045376字节, 总读取: 2983936字节
发送音频数据: 10240字节, 5120样本, 能量=819.55, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 3045376
SystemASR: 构建消息 - 序列号=51, 载荷大小=7425, 总大小=7437
SystemASR: 发送音频数据，序列号=51, 总大小=7437字节
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000002400000c9e7b226175'
}
SystemASR: 初始载荷长度: 3238
SystemASR: 解析序列号
SystemASR: 序列号: 36 剩余载荷长度: 3234
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例","utterances":[{"definite":false,"end_time":15102,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14322,"start_time":14242,"text":"开发"},{"end_time":14562,"start_time":14482,"text":"用"},{"end_time":14642,"start_time":14562,"text":"的"},{"end_time":15102,"start_time":15022,"text":"实例"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 15102
}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0095, Max=0.0352, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844948167 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=17 00 fb ff 84 00 7e 00
MicrophoneASR: 音频数据分析 - 非零字节: 6824/8192 (83.30%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [23, -5, 132, 126]
MicrophoneASR: 前8字节原始数据: 17 00 fb ff 84 00 7e 00
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0095, Max=0.0352, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844948168 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0043, Max=0.0179, 阈值=0.01, 静音=true
🔇 MicrophoneASR: 检测到静音 (连续1帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续2帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000002500000cd77b226175'
}
SystemASR: 初始载荷长度: 3295
SystemASR: 解析序列号
SystemASR: 序列号: 37 剩余载荷长度: 3291
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟","utterances":[{"definite":false,"end_time":15342,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14322,"start_time":14242,"text":"开发"},{"end_time":14562,"start_time":14482,"text":"用"},{"end_time":14642,"start_time":14562,"text":"的"},{"end_time":14962,"start_time":14882,"text":"实例"},{"end_time":15342,"start_time":15262,"text":"跟"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 15342
}
安全读取: 位置=3045376, 文件大小=3102976, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
当前文件位置: 3102976字节, 总读取: 3045376字节
发送音频数据: 9600字节, 4800样本, 能量=1621.68, 静音=false
安全读取并处理 57600 字节音频数据，新位置: 3102976
SystemASR: 构建消息 - 序列号=52, 载荷大小=8700, 总大小=8712
SystemASR: 发送音频数据，序列号=52, 总大小=8712字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续3帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续4帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=3102976, 文件大小=3164416, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 3164416字节, 总读取: 3102976字节
发送音频数据: 10240字节, 5120样本, 能量=2361.46, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 3164416
SystemASR: 构建消息 - 序列号=53, 载荷大小=9457, 总大小=9469
SystemASR: 发送音频数据，序列号=53, 总大小=9469字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续5帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续6帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续7帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续8帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000002600000d227b226175'
}
SystemASR: 初始载荷长度: 3370
SystemASR: 解析序列号
SystemASR: 序列号: 38 剩余载荷长度: 3366
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人","utterances":[{"definite":false,"end_time":15982,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14322,"start_time":14242,"text":"开发"},{"end_time":14562,"start_time":14482,"text":"用"},{"end_time":14642,"start_time":14562,"text":"的"},{"end_time":14962,"start_time":14882,"text":"实例"},{"end_time":15202,"start_time":15122,"text":"跟"},{"end_time":15982,"start_time":15902,"text":"其他人"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 15982
}
安全读取: 位置=3164416, 文件大小=3225856, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 3225856字节, 总读取: 3164416字节
发送音频数据: 10240字节, 5120样本, 能量=1056.76, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 3225856
SystemASR: 构建消息 - 序列号=54, 载荷大小=8978, 总大小=8990
SystemASR: 发送音频数据，序列号=54, 总大小=8990字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0100, Max=0.0648, 阈值=0.01, 静音=false
🔊 MicrophoneASR: 检测到声音，结束静音状态 (之前连续8帧静音)
🎵 MicrophoneASR: 1753844949203 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=b2 00 8c 00 b6 ff fe ff
MicrophoneASR: 音频数据分析 - 非零字节: 6525/8192 (79.65%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [178, 140, -74, -2]
MicrophoneASR: 前8字节原始数据: b2 00 8c 00 b6 ff fe ff
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0100, Max=0.0648, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844949203 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000002700000d9d7b226175'
}
SystemASR: 初始载荷长度: 3493
SystemASR: 解析序列号
SystemASR: 序列号: 39 剩余载荷长度: 3489
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的","utterances":[{"definite":false,"end_time":16462,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14322,"start_time":14242,"text":"开发"},{"end_time":14562,"start_time":14482,"text":"用"},{"end_time":14642,"start_time":14562,"text":"的"},{"end_time":14962,"start_time":14882,"text":"实例"},{"end_time":15202,"start_time":15122,"text":"跟"},{"end_time":15842,"start_time":15762,"text":"其他人"},{"end_time":16082,"start_time":16002,"text":"开发"},{"end_time":16462,"start_time":16382,"text":"的"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 16462
}
安全读取: 位置=3225856, 文件大小=3287296, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 3287296字节, 总读取: 3225856字节
发送音频数据: 10240字节, 5120样本, 能量=1206.59, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 3287296
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
SystemASR: 构建消息 - 序列号=55, 载荷大小=9069, 总大小=9081
SystemASR: 发送音频数据，序列号=55, 总大小=9081字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0069, Max=0.0307, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844949408 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=17 03 97 00 1d fd 80 fd
MicrophoneASR: 音频数据分析 - 非零字节: 4328/5462 (79.24%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [791, 151, -739, -640]
MicrophoneASR: 前8字节原始数据: 17 03 97 00 1d fd 80 fd
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0069, Max=0.0307, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844949409 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000002800000ddf7b226175'
}
SystemASR: 初始载荷长度: 3559
SystemASR: 解析序列号
SystemASR: 序列号: 40 剩余载荷长度: 3555
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例","utterances":[{"definite":false,"end_time":16622,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14322,"start_time":14242,"text":"开发"},{"end_time":14562,"start_time":14482,"text":"用"},{"end_time":14642,"start_time":14562,"text":"的"},{"end_time":14962,"start_time":14882,"text":"实例"},{"end_time":15202,"start_time":15122,"text":"跟"},{"end_time":15842,"start_time":15762,"text":"其他人"},{"end_time":16082,"start_time":16002,"text":"开发"},{"end_time":16322,"start_time":16242,"text":"的"},{"end_time":16622,"start_time":16542,"text":"实例"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 16622
}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0088, Max=0.0560, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844949617 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=63 fe c9 00 4a 01 32 00
MicrophoneASR: 音频数据分析 - 非零字节: 4542/5462 (83.16%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-413, 201, 330, 50]
MicrophoneASR: 前8字节原始数据: 63 fe c9 00 4a 01 32 00
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0088, Max=0.0560, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844949619 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=3287296, 文件大小=3348736, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 3348736字节, 总读取: 3287296字节
发送音频数据: 10240字节, 5120样本, 能量=1260.04, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 3348736
SystemASR: 构建消息 - 序列号=56, 载荷大小=9199, 总大小=9211
SystemASR: 发送音频数据，序列号=56, 总大小=9211字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0055, Max=0.0332, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844949823 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=18 00 9e 00 af 00 48 00
MicrophoneASR: 音频数据分析 - 非零字节: 6378/8192 (77.86%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [24, 158, 175, 72]
MicrophoneASR: 前8字节原始数据: 18 00 9e 00 af 00 48 00
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0055, Max=0.0332, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844949824 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000002900000e277b226175'
}
SystemASR: 初始载荷长度: 3631
SystemASR: 解析序列号
SystemASR: 序列号: 41 剩余载荷长度: 3627
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例，或者',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例，或者","utterances":[{"definite":false,"end_time":17022,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例，或者","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14322,"start_time":14242,"text":"开发"},{"end_time":14562,"start_time":14482,"text":"用"},{"end_time":14642,"start_time":14562,"text":"的"},{"end_time":14962,"start_time":14882,"text":"实例"},{"end_time":15202,"start_time":15122,"text":"跟"},{"end_time":15842,"start_time":15762,"text":"其他人"},{"end_time":16082,"start_time":16002,"text":"开发"},{"end_time":16322,"start_time":16242,"text":"的"},{"end_time":16482,"start_time":16402,"text":"实例"},{"end_time":17022,"start_time":16942,"text":"或者"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 17022
}
安全读取: 位置=3348736, 文件大小=3410176, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 3410176字节, 总读取: 3348736字节
发送音频数据: 10240字节, 5120样本, 能量=1175.57, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 3410176
SystemASR: 构建消息 - 序列号=57, 载荷大小=9126, 总大小=9138
SystemASR: 发送音频数据，序列号=57, 总大小=9138字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0035, Max=0.0219, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844950031 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=57 00 db ff 67 ff be 00
MicrophoneASR: 音频数据分析 - 非零字节: 4132/5462 (75.65%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [87, -37, -153, 190]
MicrophoneASR: 前8字节原始数据: 57 00 db ff 67 ff be 00
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0035, Max=0.0219, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844950032 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0054, Max=0.0331, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844950236 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=0a 00 c6 ff c0 ff 00 00
MicrophoneASR: 音频数据分析 - 非零字节: 6312/8192 (77.05%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [10, -58, -64, 0]
MicrophoneASR: 前8字节原始数据: 0a 00 c6 ff c0 ff 00 00
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0054, Max=0.0331, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844950237 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=3410176, 文件大小=3471616, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 3471616字节, 总读取: 3410176字节
发送音频数据: 10240字节, 5120样本, 能量=1348.17, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 3471616
SystemASR: 构建消息 - 序列号=58, 载荷大小=9201, 总大小=9213
SystemASR: 发送音频数据，序列号=58, 总大小=9213字节
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0044, Max=0.0221, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844950443 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=36 00 8f 00 a7 00 6a 00
MicrophoneASR: 音频数据分析 - 非零字节: 4216/5462 (77.19%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [54, 143, 167, 106]
MicrophoneASR: 前8字节原始数据: 36 00 8f 00 a7 00 6a 00
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0044, Max=0.0221, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844950443 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000002a00000ea57b226175'
}
SystemASR: 初始载荷长度: 3757
SystemASR: 解析序列号
SystemASR: 序列号: 42 剩余载荷长度: 3753
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例","utterances":[{"definite":false,"end_time":17662,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14322,"start_time":14242,"text":"开发"},{"end_time":14562,"start_time":14482,"text":"用"},{"end_time":14642,"start_time":14562,"text":"的"},{"end_time":14962,"start_time":14882,"text":"实例"},{"end_time":15202,"start_time":15122,"text":"跟"},{"end_time":15842,"start_time":15762,"text":"其他人"},{"end_time":16082,"start_time":16002,"text":"开发"},{"end_time":16322,"start_time":16242,"text":"的"},{"end_time":16482,"start_time":16402,"text":"实例"},{"end_time":16882,"start_time":16802,"text":"或者"},{"end_time":17362,"start_time":17282,"text":"测试"},{"end_time":17662,"start_time":17582,"text":"实例"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 17662
}
安全读取: 位置=3471616, 文件大小=3533056, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 3533056字节, 总读取: 3471616字节
发送音频数据: 10240字节, 5120样本, 能量=1568.13, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 3533056
SystemASR: 构建消息 - 序列号=59, 载荷大小=9188, 总大小=9200
SystemASR: 发送音频数据，序列号=59, 总大小=9200字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0032, Max=0.0123, 阈值=0.01, 静音=true
🔇 MicrophoneASR: 检测到静音 (连续1帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续2帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续3帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续4帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=3533056, 文件大小=3594496, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 3594496字节, 总读取: 3533056字节
发送音频数据: 10240字节, 5120样本, 能量=1765.93, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 3594496
SystemASR: 构建消息 - 序列号=60, 载荷大小=9233, 总大小=9245
SystemASR: 发送音频数据，序列号=60, 总大小=9245字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0062, Max=0.0237, 阈值=0.01, 静音=false
🔊 MicrophoneASR: 检测到声音，结束静音状态 (之前连续4帧静音)
🎵 MicrophoneASR: 1753844951064 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=b5 00 66 00 a0 00 33 00
MicrophoneASR: 音频数据分析 - 非零字节: 4329/5462 (79.26%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [181, 102, 160, 51]
MicrophoneASR: 前8字节原始数据: b5 00 66 00 a0 00 33 00
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0063, Max=0.0240, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844951065 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=3594496, 文件大小=3655936, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 3655936字节, 总读取: 3594496字节
发送音频数据: 10240字节, 5120样本, 能量=250.44, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 3655936
SystemASR: 构建消息 - 序列号=61, 载荷大小=7055, 总大小=7067
SystemASR: 发送音频数据，序列号=61, 总大小=7067字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0201, Max=0.1042, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844951271 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=ac ff b6 ff c0 ff 54 00
MicrophoneASR: 音频数据分析 - 非零字节: 7312/8192 (89.26%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [-84, -74, -64, 84]
MicrophoneASR: 前8字节原始数据: ac ff b6 ff c0 ff 54 00
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0197, Max=0.0874, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844951272 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000002b00000ede7b226175'
}
SystemASR: 初始载荷长度: 3814
SystemASR: 解析序列号
SystemASR: 序列号: 43 剩余载荷长度: 3810
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给","utterances":[{"definite":false,"end_time":18142,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14322,"start_time":14242,"text":"开发"},{"end_time":14562,"start_time":14482,"text":"用"},{"end_time":14642,"start_time":14562,"text":"的"},{"end_time":14962,"start_time":14882,"text":"实例"},{"end_time":15202,"start_time":15122,"text":"跟"},{"end_time":15842,"start_time":15762,"text":"其他人"},{"end_time":16082,"start_time":16002,"text":"开发"},{"end_time":16322,"start_time":16242,"text":"的"},{"end_time":16482,"start_time":16402,"text":"实例"},{"end_time":16882,"start_time":16802,"text":"或者"},{"end_time":17362,"start_time":17282,"text":"测试"},{"end_time":17522,"start_time":17442,"text":"实例"},{"end_time":18142,"start_time":18062,"text":"给"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 18142
}
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0138, Max=0.0526, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844951482 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=54 fd c7 fc 6d fe dc fe
MicrophoneASR: 音频数据分析 - 非零字节: 4761/5462 (87.17%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-684, -825, -403, -292]
MicrophoneASR: 前8字节原始数据: 54 fd c7 fc 6d fe dc fe
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0138, Max=0.0616, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844951484 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000002c00000f597b226175'
}
SystemASR: 初始载荷长度: 3937
SystemASR: 解析序列号
SystemASR: 序列号: 44 剩余载荷长度: 3933
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开","utterances":[{"definite":false,"end_time":18542,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14322,"start_time":14242,"text":"开发"},{"end_time":14562,"start_time":14482,"text":"用"},{"end_time":14642,"start_time":14562,"text":"的"},{"end_time":14962,"start_time":14882,"text":"实例"},{"end_time":15202,"start_time":15122,"text":"跟"},{"end_time":15842,"start_time":15762,"text":"其他人"},{"end_time":16082,"start_time":16002,"text":"开发"},{"end_time":16322,"start_time":16242,"text":"的"},{"end_time":16482,"start_time":16402,"text":"实例"},{"end_time":16882,"start_time":16802,"text":"或者"},{"end_time":17362,"start_time":17282,"text":"测试"},{"end_time":17522,"start_time":17442,"text":"实例"},{"end_time":18002,"start_time":17922,"text":"给"},{"end_time":18322,"start_time":18242,"text":"隔"},{"end_time":18542,"start_time":18462,"text":"离开"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 18542
}
安全读取: 位置=3655936, 文件大小=3713536, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
当前文件位置: 3713536字节, 总读取: 3655936字节
发送音频数据: 9600字节, 4800样本, 能量=48.82, 静音=true
安全读取并处理 57600 字节音频数据，新位置: 3713536
SystemASR: 构建消息 - 序列号=62, 载荷大小=6468, 总大小=6480
SystemASR: 发送音频数据，序列号=62, 总大小=6480字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0057, Max=0.0376, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844951689 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=0a fe 36 ff c3 ff 43 ff
MicrophoneASR: 音频数据分析 - 非零字节: 6347/8192 (77.48%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [-502, -202, -61, -189]
MicrophoneASR: 前8字节原始数据: 0a fe 36 ff c3 ff 43 ff
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0056, Max=0.0362, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844951690 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000002d00000f927b226175'
}
SystemASR: 初始载荷长度: 3994
SystemASR: 解析序列号
SystemASR: 序列号: 45 剩余载荷长度: 3990
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来","utterances":[{"definite":false,"end_time":18782,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14322,"start_time":14242,"text":"开发"},{"end_time":14562,"start_time":14482,"text":"用"},{"end_time":14642,"start_time":14562,"text":"的"},{"end_time":14962,"start_time":14882,"text":"实例"},{"end_time":15202,"start_time":15122,"text":"跟"},{"end_time":15842,"start_time":15762,"text":"其他人"},{"end_time":16082,"start_time":16002,"text":"开发"},{"end_time":16322,"start_time":16242,"text":"的"},{"end_time":16482,"start_time":16402,"text":"实例"},{"end_time":16882,"start_time":16802,"text":"或者"},{"end_time":17362,"start_time":17282,"text":"测试"},{"end_time":17522,"start_time":17442,"text":"实例"},{"end_time":18002,"start_time":17922,"text":"给"},{"end_time":18322,"start_time":18242,"text":"隔"},{"end_time":18402,"start_time":18322,"text":"离开"},{"end_time":18782,"start_time":18702,"text":"来"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 18782
}
安全读取: 位置=3713536, 文件大小=3774976, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 3774976字节, 总读取: 3713536字节
发送音频数据: 10240字节, 5120样本, 能量=147.30, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 3774976
SystemASR: 构建消息 - 序列号=63, 载荷大小=4770, 总大小=4782
SystemASR: 发送音频数据，序列号=63, 总大小=4782字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0025, Max=0.0104, 阈值=0.01, 静音=true
🔇 MicrophoneASR: 检测到静音 (连续1帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续2帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续3帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续4帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=3774976, 文件大小=3836416, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 3836416字节, 总读取: 3774976字节
发送音频数据: 10240字节, 5120样本, 能量=1848.59, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 3836416
SystemASR: 构建消息 - 序列号=64, 载荷大小=9480, 总大小=9492
SystemASR: 发送音频数据，序列号=64, 总大小=9492字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0099, Max=0.1069, 阈值=0.01, 静音=false
🔊 MicrophoneASR: 检测到声音，结束静音状态 (之前连续4帧静音)
🎵 MicrophoneASR: 1753844952310 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=cf fe 89 fe 46 00 1c 01
MicrophoneASR: 音频数据分析 - 非零字节: 6631/8192 (80.94%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [-305, -375, 70, 284]
MicrophoneASR: 前8字节原始数据: cf fe 89 fe 46 00 1c 01
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0098, Max=0.1040, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844952311 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
安全读取: 位置=3836416, 文件大小=3894016, 待读取=57600字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0061, Max=0.0659, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844952519 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=41 ff e5 fe f1 01 4e fe
MicrophoneASR: 音频数据分析 - 非零字节: 4160/5462 (76.16%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-191, -283, 497, -434]
MicrophoneASR: 前8字节原始数据: 41 ff e5 fe f1 01 4e fe
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0058, Max=0.0697, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844952520 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
当前文件位置: 3894016字节, 总读取: 3836416字节
发送音频数据: 9600字节, 4800样本, 能量=1791.52, 静音=false
安全读取并处理 57600 字节音频数据，新位置: 3894016
音频监控统计 - 运行时间: 20.3s, 总读取: 64, 成功: 64, 空读: 0, 错误: 0
数据统计 - 总字节: 3894016, 平均速率: 191871 bytes/s, 不完整帧缓存: 0字节
SystemASR: 构建消息 - 序列号=65, 载荷大小=8861, 总大小=8873
SystemASR: 发送音频数据，序列号=65, 总大小=8873字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0069, Max=0.0454, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844952725 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=2c 00 09 00 ec ff 27 fe
MicrophoneASR: 音频数据分析 - 非零字节: 6565/8192 (80.14%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [44, 9, -20, -473]
MicrophoneASR: 前8字节原始数据: 2c 00 09 00 ec ff 27 fe
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0072, Max=0.0466, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844952726 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000002e000010047b226175'
}
SystemASR: 初始载荷长度: 4108
SystemASR: 解析序列号
SystemASR: 序列号: 46 剩余载荷长度: 4104
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来他的',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来他的","utterances":[{"definite":false,"end_time":19982,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来他的","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14322,"start_time":14242,"text":"开发"},{"end_time":14562,"start_time":14482,"text":"用"},{"end_time":14642,"start_time":14562,"text":"的"},{"end_time":14962,"start_time":14882,"text":"实例"},{"end_time":15202,"start_time":15122,"text":"跟"},{"end_time":15842,"start_time":15762,"text":"其他人"},{"end_time":16082,"start_time":16002,"text":"开发"},{"end_time":16322,"start_time":16242,"text":"的"},{"end_time":16482,"start_time":16402,"text":"实例"},{"end_time":16882,"start_time":16802,"text":"或者"},{"end_time":17362,"start_time":17282,"text":"测试"},{"end_time":17522,"start_time":17442,"text":"实例"},{"end_time":18002,"start_time":17922,"text":"给"},{"end_time":18322,"start_time":18242,"text":"隔"},{"end_time":18402,"start_time":18322,"text":"离开"},{"end_time":18642,"start_time":18562,"text":"来"},{"end_time":19602,"start_time":19522,"text":"他"},{"end_time":19982,"start_time":19902,"text":"的"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 19982
}
安全读取: 位置=3894016, 文件大小=3955456, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 3955456字节, 总读取: 3894016字节
发送音频数据: 10240字节, 5120样本, 能量=1807.81, 静音=false
SystemASR: 处理音频数据，长度=10240字节, 采样率=16000Hz
安全读取并处理 61440 字节音频数据，新位置: 3955456
SystemASR: 构建消息 - 序列号=66, 载荷大小=9378, 总大小=9390
SystemASR: 发送音频数据，序列号=66, 总大小=9390字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0037, Max=0.0222, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844952933 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=29 00 36 00 ad ff 6f ff
MicrophoneASR: 音频数据分析 - 非零字节: 4135/5462 (75.70%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [41, 54, -83, -145]
MicrophoneASR: 前8字节原始数据: 29 00 36 00 ad ff 6f ff
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0038, Max=0.0229, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844952934 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0062, Max=0.0354, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844953139 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=22 00 94 00 58 00 e1 ff
MicrophoneASR: 音频数据分析 - 非零字节: 6475/8192 (79.04%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [34, 148, 88, -31]
MicrophoneASR: 前8字节原始数据: 22 00 94 00 58 00 e1 ff
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0062, Max=0.0350, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844953140 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=3955456, 文件大小=4016896, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 4016896字节, 总读取: 3955456字节
发送音频数据: 10240字节, 5120样本, 能量=279.38, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 4016896
SystemASR: 构建消息 - 序列号=67, 载荷大小=7513, 总大小=7525
SystemASR: 发送音频数据，序列号=67, 总大小=7525字节
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000002f000010887b226175'
}
SystemASR: 初始载荷长度: 4240
SystemASR: 解析序列号
SystemASR: 序列号: 47 剩余载荷长度: 4236
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来他的使用方式',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来他的使用方式","utterances":[{"definite":false,"end_time":20142,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来他的使用方式","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14322,"start_time":14242,"text":"开发"},{"end_time":14562,"start_time":14482,"text":"用"},{"end_time":14642,"start_time":14562,"text":"的"},{"end_time":14962,"start_time":14882,"text":"实例"},{"end_time":15202,"start_time":15122,"text":"跟"},{"end_time":15842,"start_time":15762,"text":"其他人"},{"end_time":16082,"start_time":16002,"text":"开发"},{"end_time":16322,"start_time":16242,"text":"的"},{"end_time":16482,"start_time":16402,"text":"实例"},{"end_time":16882,"start_time":16802,"text":"或者"},{"end_time":17362,"start_time":17282,"text":"测试"},{"end_time":17522,"start_time":17442,"text":"实例"},{"end_time":18002,"start_time":17922,"text":"给"},{"end_time":18322,"start_time":18242,"text":"隔"},{"end_time":18402,"start_time":18322,"text":"离开"},{"end_time":18642,"start_time":18562,"text":"来"},{"end_time":19602,"start_time":19522,"text":"他"},{"end_time":19842,"start_time":19762,"text":"的"},{"end_time":19922,"start_time":19842,"text":"使用"},{"end_time":20142,"start_time":20062,"text":"方式"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 20142
}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0058, Max=0.0364, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844953350 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=7f 00 1e 00 1f 00 82 ff
MicrophoneASR: 音频数据分析 - 非零字节: 4297/5462 (78.67%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [127, 30, 31, -126]
MicrophoneASR: 前8字节原始数据: 7f 00 1e 00 1f 00 82 ff
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0056, Max=0.0364, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844953351 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000030000010887b226175'
}
SystemASR: 初始载荷长度: 4240
SystemASR: 解析序列号
SystemASR: 序列号: 48 剩余载荷长度: 4236
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来他的使用方式',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来他的使用方式","utterances":[{"definite":false,"end_time":20382,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来他的使用方式","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14322,"start_time":14242,"text":"开发"},{"end_time":14562,"start_time":14482,"text":"用"},{"end_time":14642,"start_time":14562,"text":"的"},{"end_time":14962,"start_time":14882,"text":"实例"},{"end_time":15202,"start_time":15122,"text":"跟"},{"end_time":15842,"start_time":15762,"text":"其他人"},{"end_time":16082,"start_time":16002,"text":"开发"},{"end_time":16322,"start_time":16242,"text":"的"},{"end_time":16482,"start_time":16402,"text":"实例"},{"end_time":16882,"start_time":16802,"text":"或者"},{"end_time":17362,"start_time":17282,"text":"测试"},{"end_time":17522,"start_time":17442,"text":"实例"},{"end_time":18002,"start_time":17922,"text":"给"},{"end_time":18322,"start_time":18242,"text":"隔"},{"end_time":18402,"start_time":18322,"text":"离开"},{"end_time":18642,"start_time":18562,"text":"来"},{"end_time":19602,"start_time":19522,"text":"他"},{"end_time":19842,"start_time":19762,"text":"的"},{"end_time":19922,"start_time":19842,"text":"使用"},{"end_time":20002,"start_time":19922,"text":"方式"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 20382
}
安全读取: 位置=4016896, 文件大小=4078336, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 4078336字节, 总读取: 4016896字节
发送音频数据: 10240字节, 5120样本, 能量=2719.56, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 4078336
SystemASR: 构建消息 - 序列号=68, 载荷大小=9419, 总大小=9431
SystemASR: 发送音频数据，序列号=68, 总大小=9431字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0189, Max=0.1097, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844953556 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=ac 00 f1 ff 3d 00 e7 ff
MicrophoneASR: 音频数据分析 - 非零字节: 6700/8192 (81.79%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [172, -15, 61, -25]
MicrophoneASR: 前8字节原始数据: ac 00 f1 ff 3d 00 e7 ff
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0189, Max=0.1097, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844953557 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0138, Max=0.0567, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844953761 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=bc 01 db 01 40 02 19 03
MicrophoneASR: 音频数据分析 - 非零字节: 4698/5462 (86.01%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [444, 475, 576, 793]
MicrophoneASR: 前8字节原始数据: bc 01 db 01 40 02 19 03
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0141, Max=0.0608, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844953762 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=4078336, 文件大小=4139776, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 4139776字节, 总读取: 4078336字节
发送音频数据: 10240字节, 5120样本, 能量=909.06, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 4139776
SystemASR: 构建消息 - 序列号=69, 载荷大小=8857, 总大小=8869
SystemASR: 发送音频数据，序列号=69, 总大小=8869字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0139, Max=0.0645, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844953972 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=01 01 c6 02 4d 04 f6 04
MicrophoneASR: 音频数据分析 - 非零字节: 4545/5462 (83.21%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [257, 710, 1101, 1270]
MicrophoneASR: 前8字节原始数据: 01 01 c6 02 4d 04 f6 04
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0134, Max=0.0575, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844953973 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000031000010d07b226175'
}
SystemASR: 初始载荷长度: 4312
SystemASR: 解析序列号
SystemASR: 序列号: 49 剩余载荷长度: 4308
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来，他的使用方式是用',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来，他的使用方式是用","utterances":[{"definite":false,"end_time":21182,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来，他的使用方式是用","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14322,"start_time":14242,"text":"开发"},{"end_time":14562,"start_time":14482,"text":"用"},{"end_time":14642,"start_time":14562,"text":"的"},{"end_time":14962,"start_time":14882,"text":"实例"},{"end_time":15202,"start_time":15122,"text":"跟"},{"end_time":15842,"start_time":15762,"text":"其他人"},{"end_time":16082,"start_time":16002,"text":"开发"},{"end_time":16322,"start_time":16242,"text":"的"},{"end_time":16482,"start_time":16402,"text":"实例"},{"end_time":16882,"start_time":16802,"text":"或者"},{"end_time":17362,"start_time":17282,"text":"测试"},{"end_time":17522,"start_time":17442,"text":"实例"},{"end_time":18002,"start_time":17922,"text":"给"},{"end_time":18322,"start_time":18242,"text":"隔"},{"end_time":18402,"start_time":18322,"text":"离开"},{"end_time":18642,"start_time":18562,"text":"来"},{"end_time":19602,"start_time":19522,"text":"他"},{"end_time":19842,"start_time":19762,"text":"的"},{"end_time":19922,"start_time":19842,"text":"使用"},{"end_time":20002,"start_time":19922,"text":"方式"},{"end_time":21182,"start_time":21102,"text":"是用"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 21182
}
安全读取: 位置=4139776, 文件大小=4201216, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 4201216字节, 总读取: 4139776字节
发送音频数据: 10240字节, 5120样本, 能量=1372.70, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 4201216
SystemASR: 构建消息 - 序列号=70, 载荷大小=9071, 总大小=9083
SystemASR: 发送音频数据，序列号=70, 总大小=9083字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0063, Max=0.1005, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844954176 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=53 05 a9 fb f2 f9 bf 06
MicrophoneASR: 音频数据分析 - 非零字节: 6268/8192 (76.51%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [1363, -1111, -1550, 1727]
MicrophoneASR: 前8字节原始数据: 53 05 a9 fb f2 f9 bf 06
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0066, Max=0.0921, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844954177 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000032000011127b226175'
}
SystemASR: 初始载荷长度: 4378
SystemASR: 解析序列号
SystemASR: 序列号: 50 剩余载荷长度: 4374
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来，他的使用方式是用配置',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来，他的使用方式是用配置","utterances":[{"definite":false,"end_time":21422,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来，他的使用方式是用配置","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14322,"start_time":14242,"text":"开发"},{"end_time":14562,"start_time":14482,"text":"用"},{"end_time":14642,"start_time":14562,"text":"的"},{"end_time":14962,"start_time":14882,"text":"实例"},{"end_time":15202,"start_time":15122,"text":"跟"},{"end_time":15842,"start_time":15762,"text":"其他人"},{"end_time":16082,"start_time":16002,"text":"开发"},{"end_time":16322,"start_time":16242,"text":"的"},{"end_time":16482,"start_time":16402,"text":"实例"},{"end_time":16882,"start_time":16802,"text":"或者"},{"end_time":17362,"start_time":17282,"text":"测试"},{"end_time":17522,"start_time":17442,"text":"实例"},{"end_time":18002,"start_time":17922,"text":"给"},{"end_time":18322,"start_time":18242,"text":"隔"},{"end_time":18402,"start_time":18322,"text":"离开"},{"end_time":18642,"start_time":18562,"text":"来"},{"end_time":19602,"start_time":19522,"text":"他"},{"end_time":19842,"start_time":19762,"text":"的"},{"end_time":19922,"start_time":19842,"text":"使用"},{"end_time":20002,"start_time":19922,"text":"方式"},{"end_time":21042,"start_time":20962,"text":"是用"},{"end_time":21422,"start_time":21342,"text":"配置"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 21422
}
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0070, Max=0.0302, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844954385 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=c8 ff e2 ff 01 00 64 00
MicrophoneASR: 音频数据分析 - 非零字节: 4366/5462 (79.93%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-56, -30, 1, 100]
MicrophoneASR: 前8字节原始数据: c8 ff e2 ff 01 00 64 00
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0066, Max=0.0298, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844954386 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=4201216, 文件大小=4262656, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 4262656字节, 总读取: 4201216字节
发送音频数据: 10240字节, 5120样本, 能量=1335.98, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 4262656
SystemASR: 构建消息 - 序列号=71, 载荷大小=9151, 总大小=9163
SystemASR: 发送音频数据，序列号=71, 总大小=9163字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0053, Max=0.0258, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844954590 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=55 01 dc 00 20 01 d0 00
MicrophoneASR: 音频数据分析 - 非零字节: 6307/8192 (76.99%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [341, 220, 288, 208]
MicrophoneASR: 前8字节原始数据: 55 01 dc 00 20 01 d0 00
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0050, Max=0.0256, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844954591 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000033000011547b226175'
}
SystemASR: 初始载荷长度: 4444
SystemASR: 解析序列号
SystemASR: 序列号: 51 剩余载荷长度: 4440
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来，他的使用方式是用配置两个',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来，他的使用方式是用配置两个","utterances":[{"definite":false,"end_time":21932,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来，他的使用方式是用配置两个","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14322,"start_time":14242,"text":"开发"},{"end_time":14562,"start_time":14482,"text":"用"},{"end_time":14642,"start_time":14562,"text":"的"},{"end_time":14962,"start_time":14882,"text":"实例"},{"end_time":15202,"start_time":15122,"text":"跟"},{"end_time":15842,"start_time":15762,"text":"其他人"},{"end_time":16082,"start_time":16002,"text":"开发"},{"end_time":16322,"start_time":16242,"text":"的"},{"end_time":16482,"start_time":16402,"text":"实例"},{"end_time":16882,"start_time":16802,"text":"或者"},{"end_time":17362,"start_time":17282,"text":"测试"},{"end_time":17522,"start_time":17442,"text":"实例"},{"end_time":18002,"start_time":17922,"text":"给"},{"end_time":18322,"start_time":18242,"text":"隔"},{"end_time":18402,"start_time":18322,"text":"离开"},{"end_time":18642,"start_time":18562,"text":"来"},{"end_time":19602,"start_time":19522,"text":"他"},{"end_time":19842,"start_time":19762,"text":"的"},{"end_time":19922,"start_time":19842,"text":"使用"},{"end_time":20002,"start_time":19922,"text":"方式"},{"end_time":21042,"start_time":20962,"text":"是用"},{"end_time":21422,"start_time":21342,"text":"配置"},{"end_time":21932,"start_time":21852,"text":"两个"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 21932
}
安全读取: 位置=4262656, 文件大小=4327936, 待读取=65280字节
帧处理: 总数据=65280字节, 完整帧=65280字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=65280字节, 缓存完整帧=0字节, 有效帧=65280字节, 新不完整帧=0字节
当前文件位置: 4327936字节, 总读取: 4262656字节
发送音频数据: 10880字节, 5440样本, 能量=41.99, 静音=true
安全读取并处理 65280 字节音频数据，新位置: 4327936
SystemASR: 构建消息 - 序列号=72, 载荷大小=6102, 总大小=6114
SystemASR: 发送音频数据，序列号=72, 总大小=6114字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0021, Max=0.0074, 阈值=0.01, 静音=true
🔇 MicrophoneASR: 检测到静音 (连续1帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续2帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔇 MicrophoneASR: 检测到静音 (连续3帧)，跳过发送
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0114, Max=0.0858, 阈值=0.01, 静音=false
🔊 MicrophoneASR: 检测到声音，结束静音状态 (之前连续3帧静音)
🎵 MicrophoneASR: 1753844955004 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=b8 ff e6 ff ca ff b7 ff
MicrophoneASR: 音频数据分析 - 非零字节: 6538/8192 (79.81%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [-72, -26, -54, -73]
MicrophoneASR: 前8字节原始数据: b8 ff e6 ff ca ff b7 ff
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000034000011967b226175'
}
SystemASR: 初始载荷长度: 4510
SystemASR: 解析序列号
SystemASR: 序列号: 52 剩余载荷长度: 4506
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来，他的使用方式是用配置两个文件',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来，他的使用方式是用配置两个文件","utterances":[{"definite":false,"end_time":22092,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来，他的使用方式是用配置两个文件","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14322,"start_time":14242,"text":"开发"},{"end_time":14562,"start_time":14482,"text":"用"},{"end_time":14642,"start_time":14562,"text":"的"},{"end_time":14962,"start_time":14882,"text":"实例"},{"end_time":15202,"start_time":15122,"text":"跟"},{"end_time":15842,"start_time":15762,"text":"其他人"},{"end_time":16082,"start_time":16002,"text":"开发"},{"end_time":16322,"start_time":16242,"text":"的"},{"end_time":16482,"start_time":16402,"text":"实例"},{"end_time":16882,"start_time":16802,"text":"或者"},{"end_time":17362,"start_time":17282,"text":"测试"},{"end_time":17522,"start_time":17442,"text":"实例"},{"end_time":18002,"start_time":17922,"text":"给"},{"end_time":18322,"start_time":18242,"text":"隔"},{"end_time":18402,"start_time":18322,"text":"离开"},{"end_time":18642,"start_time":18562,"text":"来"},{"end_time":19602,"start_time":19522,"text":"他"},{"end_time":19842,"start_time":19762,"text":"的"},{"end_time":19922,"start_time":19842,"text":"使用"},{"end_time":20002,"start_time":19922,"text":"方式"},{"end_time":21042,"start_time":20962,"text":"是用"},{"end_time":21422,"start_time":21342,"text":"配置"},{"end_time":21792,"start_time":21712,"text":"两个"},{"end_time":22092,"start_time":22012,"text":"文件"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: false,
  startTime: 1522,
  endTime: 22092
}
安全读取: 位置=4327936, 文件大小=4381696, 待读取=53760字节
帧处理: 总数据=53760字节, 完整帧=53760字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=53760字节, 缓存完整帧=0字节, 有效帧=53760字节, 新不完整帧=0字节
当前文件位置: 4381696字节, 总读取: 4327936字节
发送音频数据: 8960字节, 4480样本, 能量=1302.98, 静音=false
SystemASR: 发送缓冲的音频数据，大小=8960字节
安全读取并处理 53760 字节音频数据，新位置: 4381696
SystemASR: 构建消息 - 序列号=73, 载荷大小=6114, 总大小=6126
SystemASR: 发送音频数据，序列号=73, 总大小=6126字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0310, Max=0.1137, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844955210 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=c9 fd d7 fd 76 fe 20 fe
MicrophoneASR: 音频数据分析 - 非零字节: 5148/5462 (94.25%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-567, -553, -394, -480]
MicrophoneASR: 前8字节原始数据: c9 fd d7 fd 76 fe 20 fe
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0278, Max=0.1123, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844955211 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
安全读取: 位置=4381696, 文件大小=4443136, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 4443136字节, 总读取: 4381696字节
发送音频数据: 10240字节, 5120样本, 能量=2130.86, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 4443136
SystemASR: 构建消息 - 序列号=74, 载荷大小=9525, 总大小=9537
SystemASR: 发送音频数据，序列号=74, 总大小=9537字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0135, Max=0.0724, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844955421 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=20 03 e0 01 58 01 62 ff
MicrophoneASR: 音频数据分析 - 非零字节: 4692/5462 (85.90%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [800, 480, 344, -158]
MicrophoneASR: 前8字节原始数据: 20 03 e0 01 58 01 62 ff
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0132, Max=0.0740, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844955422 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0130, Max=0.0639, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844955628 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=1d fa a9 fe 82 fe d7 fe
MicrophoneASR: 音频数据分析 - 非零字节: 6965/8192 (85.02%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [-1507, -343, -382, -297]
MicrophoneASR: 前8字节原始数据: 1d fa a9 fe 82 fe d7 fe
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0127, Max=0.0669, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844955629 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000035000012587b226175'
}
SystemASR: 初始载荷长度: 4704
SystemASR: 解析序列号
SystemASR: 序列号: 53 剩余载荷长度: 4700
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来，他的使用方式是用配置两个文件。第一',
    utterances: [ [Object], [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来，他的使用方式是用配置两个文件。第一","utterances":[{"definite":true,"end_time":21952,"start_time":1522,"text":"series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以配置你的代码访问的 Redis 的实例是什么，这样的话就可以把你开发用的实例跟其他人开发的实例或者测试实例给隔离开来，他的使用方式是用配置两个文件。","words":[{"end_time":2082,"start_time":2002,"text":"series"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":2482,"start_time":2402,"text":"未来"},{"end_time":2802,"start_time":2722,"text":"方便"},{"end_time":2882,"start_time":2802,"text":"用户"},{"end_time":3122,"start_time":3042,"text":"开发"},{"end_time":3442,"start_time":3362,"text":"还"},{"end_time":3602,"start_time":3522,"text":"提供"},{"end_time":3762,"start_time":3682,"text":"了"},{"end_time":3922,"start_time":3842,"text":"一"},{"end_time":4001,"start_time":3922,"text":"个"},{"end_time":4402,"start_time":4322,"text":"本地"},{"end_time":4642,"start_time":4562,"text":"开发"},{"end_time":4802,"start_time":4722,"text":"模式"},{"end_time":6322,"start_time":6242,"text":"他"},{"end_time":6642,"start_time":6562,"text":"意思"},{"end_time":6962,"start_time":6882,"text":"就是"},{"end_time":7682,"start_time":7602,"text":"你"},{"end_time":7762,"start_time":7682,"text":"可以"},{"end_time":8322,"start_time":8242,"text":"配置"},{"end_time":8562,"start_time":8482,"text":"你"},{"end_time":8642,"start_time":8562,"text":"在"},{"end_time":8722,"start_time":8642,"text":"本地"},{"end_time":8882,"start_time":8802,"text":"开发"},{"end_time":9122,"start_time":9042,"text":"的"},{"end_time":9202,"start_time":9122,"text":"时候"},{"end_time":9362,"start_time":9282,"text":"可以"},{"end_time":9682,"start_time":9602,"text":"配置"},{"end_time":10242,"start_time":10162,"text":"你"},{"end_time":10402,"start_time":10322,"text":"的"},{"end_time":11042,"start_time":10962,"text":"代码"},{"end_time":11442,"start_time":11362,"text":"访问"},{"end_time":11602,"start_time":11522,"text":"的"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":11922,"start_time":11842,"text":"Redis"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":12322,"start_time":12242,"text":"的"},{"end_time":12482,"start_time":12402,"text":"实例"},{"end_time":12562,"start_time":12482,"text":"是"},{"end_time":12642,"start_time":12562,"text":"什么"},{"end_time":13442,"start_time":13362,"text":"这样"},{"end_time":13602,"start_time":13522,"text":"的话"},{"end_time":13762,"start_time":13682,"text":"就"},{"end_time":13842,"start_time":13762,"text":"可以"},{"end_time":13922,"start_time":13842,"text":"把"},{"end_time":14002,"start_time":13922,"text":"你"},{"end_time":14322,"start_time":14242,"text":"开发"},{"end_time":14562,"start_time":14482,"text":"用"},{"end_time":14642,"start_time":14562,"text":"的"},{"end_time":14962,"start_time":14882,"text":"实例"},{"end_time":15202,"start_time":15122,"text":"跟"},{"end_time":15842,"start_time":15762,"text":"其他人"},{"end_time":16082,"start_time":16002,"text":"开发"},{"end_time":16322,"start_time":16242,"text":"的"},{"end_time":16482,"start_time":16402,"text":"实例"},{"end_time":16882,"start_time":16802,"text":"或者"},{"end_time":17362,"start_time":17282,"text":"测试"},{"end_time":17522,"start_time":17442,"text":"实例"},{"end_time":18002,"start_time":17922,"text":"给"},{"end_time":18322,"start_time":18242,"text":"隔"},{"end_time":18402,"start_time":18322,"text":"离开"},{"end_time":18642,"start_time":18562,"text":"来"},{"end_time":19602,"start_time":19522,"text":"他"},{"end_time":19842,"start_time":19762,"text":"的"},{"end_time":19922,"start_time":19842,"text":"使用"},{"end_time":20002,"start_time":19922,"text":"方式"},{"end_time":21042,"start_time":20962,"text":"是用"},{"end_time":21422,"start_time":21342,"text":"配置"},{"end_time":21792,"start_time":21712,"text":"两个"},{"end_time":21952,"start_time":21872,"text":"文件"}]},{"definite":false,"end_time":22891,"start_time":22592,"text":"第一","words":[{"end_time":22671,"start_time":22592,"text":"第"},{"end_time":22891,"start_time":22811,"text":"一"}]}]}
SystemASR: 处理utterances转录 {
  count: 2,
  currentText: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  historyCount: 1
}
SystemASR: 当前转录 {
  text: 'series 未来方便用户开发，还提供了一个本地开发模式，他意思就是你可以配置你在本地开发的时候可以...',
  isFinal: true,
  startTime: 1522,
  endTime: 21952
}
SystemASR: 处理历史转录 { historyCount: 1 }
SystemASR: 历史转录[1] { text: '第一', isFinal: false, startTime: 22592, endTime: 22891 }
安全读取: 位置=4443136, 文件大小=4508416, 待读取=65280字节
帧处理: 总数据=65280字节, 完整帧=65280字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=65280字节, 缓存完整帧=0字节, 有效帧=65280字节, 新不完整帧=0字节
当前文件位置: 4508416字节, 总读取: 4443136字节
发送音频数据: 10880字节, 5440样本, 能量=1701.18, 静音=false
安全读取并处理 65280 字节音频数据，新位置: 4508416
SystemASR: 构建消息 - 序列号=75, 载荷大小=9911, 总大小=9923
SystemASR: 发送音频数据，序列号=75, 总大小=9923字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0138, Max=0.0688, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844955835 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=9c ff d1 ff 69 ff 58 ff
MicrophoneASR: 音频数据分析 - 非零字节: 4618/5462 (84.55%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-100, -47, -151, -168]
MicrophoneASR: 前8字节原始数据: 9c ff d1 ff 69 ff 58 ff
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0138, Max=0.0688, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844955836 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000036000001b97b226175'
}
SystemASR: 初始载荷长度: 449
SystemASR: 解析序列号
SystemASR: 序列号: 54 剩余载荷长度: 445
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: '第一个文件',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"第一个文件","utterances":[{"definite":false,"end_time":23131,"start_time":22592,"text":"第一个文件","words":[{"end_time":22671,"start_time":22592,"text":"第"},{"end_time":22751,"start_time":22671,"text":"一"},{"end_time":22831,"start_time":22751,"text":"个"},{"end_time":23131,"start_time":23051,"text":"文件"}]}]}
SystemASR: 处理utterances转录 { count: 1, currentText: '第一个文件...', historyCount: 0 }
SystemASR: 当前转录 { text: '第一个文件', isFinal: false, startTime: 22592, endTime: 23131 }
安全读取: 位置=4508416, 文件大小=4569856, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 4569856字节, 总读取: 4508416字节
发送音频数据: 10240字节, 5120样本, 能量=1310.30, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 4569856
SystemASR: 构建消息 - 序列号=76, 载荷大小=9116, 总大小=9128
SystemASR: 发送音频数据，序列号=76, 总大小=9128字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0192, Max=0.0865, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844956041 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=3b 03 08 03 d0 08 87 04
MicrophoneASR: 音频数据分析 - 非零字节: 7127/8192 (87.00%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [827, 776, 2256, 1159]
MicrophoneASR: 前8字节原始数据: 3b 03 08 03 d0 08 87 04
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0192, Max=0.0865, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844956041 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0283, Max=0.0910, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844956247 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=36 02 38 02 6e 01 41 00
MicrophoneASR: 音频数据分析 - 非零字节: 5149/5462 (94.27%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [566, 568, 366, 65]
MicrophoneASR: 前8字节原始数据: 36 02 38 02 6e 01 41 00
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0283, Max=0.0910, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844956248 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000037000002347b226175'
}
SystemASR: 初始载荷长度: 572
SystemASR: 解析序列号
SystemASR: 序列号: 55 剩余载荷长度: 568
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: '第一个文件是我们',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"第一个文件是我们","utterances":[{"definite":false,"end_time":23532,"start_time":22592,"text":"第一个文件是我们","words":[{"end_time":22671,"start_time":22592,"text":"第"},{"end_time":22751,"start_time":22671,"text":"一"},{"end_time":22831,"start_time":22751,"text":"个"},{"end_time":22991,"start_time":22911,"text":"文件"},{"end_time":23311,"start_time":23231,"text":"是"},{"end_time":23532,"start_time":23452,"text":"我们"}]}]}
SystemASR: 处理utterances转录 { count: 1, currentText: '第一个文件是我们...', historyCount: 0 }
SystemASR: 当前转录 { text: '第一个文件是我们', isFinal: false, startTime: 22592, endTime: 23532 }
安全读取: 位置=4569856, 文件大小=4631296, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 4631296字节, 总读取: 4569856字节
发送音频数据: 10240字节, 5120样本, 能量=1777.85, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 4631296
SystemASR: 构建消息 - 序列号=77, 载荷大小=9283, 总大小=9295
SystemASR: 发送音频数据，序列号=77, 总大小=9295字节
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0257, Max=0.1026, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844956457 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=49 fe 9c ff d2 fd 39 fe
MicrophoneASR: 音频数据分析 - 非零字节: 7621/8192 (93.03%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [-439, -100, -558, -455]
MicrophoneASR: 前8字节原始数据: 49 fe 9c ff d2 fd 39 fe
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0257, Max=0.1026, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844956459 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=4631296, 文件大小=4692736, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 4692736字节, 总读取: 4631296字节
发送音频数据: 10240字节, 5120样本, 能量=1704.91, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 4692736
SystemASR: 构建消息 - 序列号=78, 载荷大小=9219, 总大小=9231
SystemASR: 发送音频数据，序列号=78, 总大小=9231字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0198, Max=0.0782, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844956667 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=14 ff aa fd 09 ff 4c 01
MicrophoneASR: 音频数据分析 - 非零字节: 5010/5462 (91.72%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-236, -598, -247, 332]
MicrophoneASR: 前8字节原始数据: 14 ff aa fd 09 ff 4c 01
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0198, Max=0.0782, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844956667 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0282, Max=0.1056, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844956871 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=78 04 4e 04 e4 03 f0 04
MicrophoneASR: 音频数据分析 - 非零字节: 5063/5462 (92.69%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [1144, 1102, 996, 1264]
MicrophoneASR: 前8字节原始数据: 78 04 4e 04 e4 03 f0 04
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0285, Max=0.1028, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844956872 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000038000002af7b226175'
}
SystemASR: 初始载荷长度: 695
SystemASR: 解析序列号
SystemASR: 序列号: 56 剩余载荷长度: 691
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: '第一个文件是我们本期的',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"第一个文件是我们本期的","utterances":[{"definite":false,"end_time":24171,"start_time":22592,"text":"第一个文件是我们本期的","words":[{"end_time":22671,"start_time":22592,"text":"第"},{"end_time":22751,"start_time":22671,"text":"一"},{"end_time":22831,"start_time":22751,"text":"个"},{"end_time":22991,"start_time":22911,"text":"文件"},{"end_time":23311,"start_time":23231,"text":"是"},{"end_time":23391,"start_time":23311,"text":"我们"},{"end_time":23792,"start_time":23712,"text":"本期"},{"end_time":24171,"start_time":24092,"text":"的"}]}]}
SystemASR: 处理utterances转录 { count: 1, currentText: '第一个文件是我们本期的...', historyCount: 0 }
SystemASR: 当前转录 {
  text: '第一个文件是我们本期的',
  isFinal: false,
  startTime: 22592,
  endTime: 24171
}
安全读取: 位置=4692736, 文件大小=4750336, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
当前文件位置: 4750336字节, 总读取: 4692736字节
发送音频数据: 9600字节, 4800样本, 能量=2307.48, 静音=false
安全读取并处理 57600 字节音频数据，新位置: 4750336
SystemASR: 构建消息 - 序列号=79, 载荷大小=8889, 总大小=8901
SystemASR: 发送音频数据，序列号=79, 总大小=8901字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0101, Max=0.0573, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844957077 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=be ff 00 00 24 00 97 00
MicrophoneASR: 音频数据分析 - 非零字节: 6860/8192 (83.74%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [-66, 0, 36, 151]
MicrophoneASR: 前8字节原始数据: be ff 00 00 24 00 97 00
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0101, Max=0.0574, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844957077 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '1191100000000039000003217b226175'
}
SystemASR: 初始载荷长度: 809
SystemASR: 解析序列号
SystemASR: 序列号: 57 剩余载荷长度: 805
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: '第一个文件是我们本期的一个',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"第一个文件是我们本期的一个","utterances":[{"definite":false,"end_time":24331,"start_time":22592,"text":"第一个文件是我们本期的一个","words":[{"end_time":22671,"start_time":22592,"text":"第"},{"end_time":22751,"start_time":22671,"text":"一"},{"end_time":22831,"start_time":22751,"text":"个"},{"end_time":22991,"start_time":22911,"text":"文件"},{"end_time":23311,"start_time":23231,"text":"是"},{"end_time":23391,"start_time":23311,"text":"我们"},{"end_time":23792,"start_time":23712,"text":"本期"},{"end_time":24032,"start_time":23952,"text":"的"},{"end_time":24112,"start_time":24032,"text":"一"},{"end_time":24331,"start_time":24251,"text":"个"}]}]}
SystemASR: 处理utterances转录 { count: 1, currentText: '第一个文件是我们本期的一个...', historyCount: 0 }
SystemASR: 当前转录 {
  text: '第一个文件是我们本期的一个',
  isFinal: false,
  startTime: 22592,
  endTime: 24331
}
安全读取: 位置=4750336, 文件大小=4811776, 待读取=61440字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0096, Max=0.0494, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844957288 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=e8 00 09 01 24 00 bb ff
MicrophoneASR: 音频数据分析 - 非零字节: 4525/5462 (82.85%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [232, 265, 36, -69]
MicrophoneASR: 前8字节原始数据: e8 00 09 01 24 00 bb ff
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0100, Max=0.0697, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844957290 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 4811776字节, 总读取: 4750336字节
发送音频数据: 10240字节, 5120样本, 能量=1850.04, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 4811776
SystemASR: 构建消息 - 序列号=80, 载荷大小=9243, 总大小=9255
SystemASR: 发送音频数据，序列号=80, 总大小=9255字节
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0119, Max=0.1407, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844957497 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=a7 fe 6f fe 7f ff b1 ff
MicrophoneASR: 音频数据分析 - 非零字节: 6999/8192 (85.44%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [-345, -401, -129, -79]
MicrophoneASR: 前8字节原始数据: a7 fe 6f fe 7f ff b1 ff
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0111, Max=0.0725, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844957498 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000003a000003637b226175'
}
SystemASR: 初始载荷长度: 875
SystemASR: 解析序列号
SystemASR: 序列号: 58 剩余载荷长度: 871
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: '第一个文件是我们本期的一个社会',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"第一个文件是我们本期的一个社会","utterances":[{"definite":false,"end_time":24651,"start_time":22592,"text":"第一个文件是我们本期的一个社会","words":[{"end_time":22671,"start_time":22592,"text":"第"},{"end_time":22751,"start_time":22671,"text":"一"},{"end_time":22831,"start_time":22751,"text":"个"},{"end_time":22991,"start_time":22911,"text":"文件"},{"end_time":23311,"start_time":23231,"text":"是"},{"end_time":23391,"start_time":23311,"text":"我们"},{"end_time":23792,"start_time":23712,"text":"本期"},{"end_time":24032,"start_time":23952,"text":"的"},{"end_time":24112,"start_time":24032,"text":"一"},{"end_time":24192,"start_time":24112,"text":"个"},{"end_time":24651,"start_time":24571,"text":"社会"}]}]}
SystemASR: 处理utterances转录 { count: 1, currentText: '第一个文件是我们本期的一个社会...', historyCount: 0 }
SystemASR: 当前转录 {
  text: '第一个文件是我们本期的一个社会',
  isFinal: false,
  startTime: 22592,
  endTime: 24651
}
安全读取: 位置=4811776, 文件大小=4873216, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 4873216字节, 总读取: 4811776字节
发送音频数据: 10240字节, 5120样本, 能量=894.01, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 4873216
SystemASR: 构建消息 - 序列号=81, 载荷大小=8639, 总大小=8651
SystemASR: 发送音频数据，序列号=81, 总大小=8651字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0130, Max=0.1386, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844957712 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=12 01 8b 03 60 02 88 fe
MicrophoneASR: 音频数据分析 - 非零字节: 4511/5462 (82.59%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [274, 907, 608, -376]
MicrophoneASR: 前8字节原始数据: 12 01 8b 03 60 02 88 fe
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0115, Max=0.0639, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844957714 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000003b0000039c7b226175'
}
SystemASR: 初始载荷长度: 932
SystemASR: 解析序列号
SystemASR: 序列号: 59 剩余载荷长度: 928
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: '第一个文件是我们本期的一个社会点',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"第一个文件是我们本期的一个社会点","utterances":[{"definite":false,"end_time":25051,"start_time":22592,"text":"第一个文件是我们本期的一个社会点","words":[{"end_time":22671,"start_time":22592,"text":"第"},{"end_time":22751,"start_time":22671,"text":"一"},{"end_time":22831,"start_time":22751,"text":"个"},{"end_time":22991,"start_time":22911,"text":"文件"},{"end_time":23311,"start_time":23231,"text":"是"},{"end_time":23391,"start_time":23311,"text":"我们"},{"end_time":23792,"start_time":23712,"text":"本期"},{"end_time":24032,"start_time":23952,"text":"的"},{"end_time":24112,"start_time":24032,"text":"一"},{"end_time":24192,"start_time":24112,"text":"个"},{"end_time":24512,"start_time":24432,"text":"社会"},{"end_time":25051,"start_time":24971,"text":"点"}]}]}
SystemASR: 处理utterances转录 { count: 1, currentText: '第一个文件是我们本期的一个社会点...', historyCount: 0 }
SystemASR: 当前转录 {
  text: '第一个文件是我们本期的一个社会点',
  isFinal: false,
  startTime: 22592,
  endTime: 25051
}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0083, Max=0.0331, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844957924 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=ab 00 cf 02 e2 02 c2 ff
MicrophoneASR: 音频数据分析 - 非零字节: 4528/5462 (82.90%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [171, 719, 738, -62]
MicrophoneASR: 前8字节原始数据: ab 00 cf 02 e2 02 c2 ff
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0075, Max=0.0395, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844957925 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=4873216, 文件大小=4938496, 待读取=65280字节
帧处理: 总数据=65280字节, 完整帧=65280字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=65280字节, 缓存完整帧=0字节, 有效帧=65280字节, 新不完整帧=0字节
当前文件位置: 4938496字节, 总读取: 4873216字节
发送音频数据: 10880字节, 5440样本, 能量=938.91, 静音=false
SystemASR: 处理音频数据，长度=10880字节, 采样率=16000Hz
安全读取并处理 65280 字节音频数据，新位置: 4938496
SystemASR: 构建消息 - 序列号=82, 载荷大小=9019, 总大小=9031
SystemASR: 发送音频数据，序列号=82, 总大小=9031字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0087, Max=0.0363, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844958133 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=fe ff 54 00 02 00 4f 00
MicrophoneASR: 音频数据分析 - 非零字节: 6527/8192 (79.68%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [-2, 84, 2, 79]
MicrophoneASR: 前8字节原始数据: fe ff 54 00 02 00 4f 00
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0102, Max=0.0362, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844958134 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000003c000004117b226175'
}
SystemASR: 初始载荷长度: 1049
SystemASR: 解析序列号
SystemASR: 序列号: 60 剩余载荷长度: 1045
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: '第一个文件是我们本期的一个事物点 property',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"第一个文件是我们本期的一个事物点 property","utterances":[{"definite":false,"end_time":25292,"start_time":22592,"text":"第一个文件是我们本期的一个事物点 property","words":[{"end_time":22671,"start_time":22592,"text":"第"},{"end_time":22751,"start_time":22671,"text":"一"},{"end_time":22831,"start_time":22751,"text":"个"},{"end_time":22991,"start_time":22911,"text":"文件"},{"end_time":23311,"start_time":23231,"text":"是"},{"end_time":23391,"start_time":23311,"text":"我们"},{"end_time":23792,"start_time":23712,"text":"本期"},{"end_time":24032,"start_time":23952,"text":"的"},{"end_time":24112,"start_time":24032,"text":"一"},{"end_time":24192,"start_time":24112,"text":"个"},{"end_time":24512,"start_time":24432,"text":"事物"},{"end_time":24911,"start_time":24831,"text":"点"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":25292,"start_time":25212,"text":"property"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: '第一个文件是我们本期的一个事物点 property...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: '第一个文件是我们本期的一个事物点 property',
  isFinal: false,
  startTime: 22592,
  endTime: 25292
}
安全读取: 位置=4938496, 文件大小=4996096, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
当前文件位置: 4996096字节, 总读取: 4938496字节
发送音频数据: 9600字节, 4800样本, 能量=13.64, 静音=true
安全读取并处理 57600 字节音频数据，新位置: 4996096
SystemASR: 构建消息 - 序列号=83, 载荷大小=4438, 总大小=4450
SystemASR: 发送音频数据，序列号=83, 总大小=4450字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0129, Max=0.0784, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844958345 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=b4 fc e8 fc ce fc 2a fd
MicrophoneASR: 音频数据分析 - 非零字节: 4621/5462 (84.60%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-844, -792, -818, -726]
MicrophoneASR: 前8字节原始数据: b4 fc e8 fc ce fc 2a fd
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0129, Max=0.0784, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844958346 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000003d000004807b226175'
}
SystemASR: 初始载荷长度: 1160
SystemASR: 解析序列号
SystemASR: 序列号: 61 剩余载荷长度: 1156
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: '第一个文件是我们本期的一个事物点 property 文件',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"第一个文件是我们本期的一个事物点 property 文件","utterances":[{"definite":false,"end_time":25612,"start_time":22592,"text":"第一个文件是我们本期的一个事物点 property 文件","words":[{"end_time":22671,"start_time":22592,"text":"第"},{"end_time":22751,"start_time":22671,"text":"一"},{"end_time":22831,"start_time":22751,"text":"个"},{"end_time":22991,"start_time":22911,"text":"文件"},{"end_time":23311,"start_time":23231,"text":"是"},{"end_time":23391,"start_time":23311,"text":"我们"},{"end_time":23792,"start_time":23712,"text":"本期"},{"end_time":24032,"start_time":23952,"text":"的"},{"end_time":24112,"start_time":24032,"text":"一"},{"end_time":24192,"start_time":24112,"text":"个"},{"end_time":24512,"start_time":24432,"text":"事物"},{"end_time":24911,"start_time":24831,"text":"点"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":25151,"start_time":25071,"text":"property"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":25612,"start_time":25532,"text":"文件"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: '第一个文件是我们本期的一个事物点 property 文件...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: '第一个文件是我们本期的一个事物点 property 文件',
  isFinal: false,
  startTime: 22592,
  endTime: 25612
}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0386, Max=0.1869, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844958558 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=c6 f8 93 f8 ad f8 c6 f8
MicrophoneASR: 音频数据分析 - 非零字节: 7306/8192 (89.18%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [-1850, -1901, -1875, -1850]
MicrophoneASR: 前8字节原始数据: c6 f8 93 f8 ad f8 c6 f8
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0386, Max=0.1869, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844958559 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=4996096, 文件大小=5057536, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 5057536字节, 总读取: 4996096字节
发送音频数据: 10240字节, 5120样本, 能量=1934.65, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 5057536
SystemASR: 构建消息 - 序列号=84, 载荷大小=8398, 总大小=8410
SystemASR: 发送音频数据，序列号=84, 总大小=8410字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0414, Max=0.1342, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844958764 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=75 00 06 00 80 ff 2a ff
MicrophoneASR: 音频数据分析 - 非零字节: 5074/5462 (92.90%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [117, 6, -128, -214]
MicrophoneASR: 前8字节原始数据: 75 00 06 00 80 ff 2a ff
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0414, Max=0.1342, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844958765 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=5057536, 文件大小=5118976, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 5118976字节, 总读取: 5057536字节
发送音频数据: 10240字节, 5120样本, 能量=1499.82, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 5118976
SystemASR: 构建消息 - 序列号=85, 载荷大小=9338, 总大小=9350
SystemASR: 发送音频数据，序列号=85, 总大小=9350字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0357, Max=0.1589, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844958974 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=58 f9 a9 f9 16 fb 16 fc
MicrophoneASR: 音频数据分析 - 非零字节: 7717/8192 (94.20%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [-1704, -1623, -1258, -1002]
MicrophoneASR: 前8字节原始数据: 58 f9 a9 f9 16 fb 16 fc
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0357, Max=0.1589, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844958974 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 解析消息头 {
  headerSize: 1,
  messageType: '0x9',
  flags: '0x1',
  serialization: 1,
  compression: 0,
  rawBytes: '119110000000003e000005017b226175'
}
SystemASR: 初始载荷长度: 1289
SystemASR: 解析序列号
SystemASR: 序列号: 62 剩余载荷长度: 1285
SystemASR: 处理解析后的响应 { code: 0, event: 0, isLastPackage: false, hasPayload: true }
SystemASR: 处理转录结果 {
  result: {
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: '第一个文件是我们本期的一个事物点 property 文件，需要把',
    utterances: [ [Object] ]
  },
  isLastPackage: false
}
SystemASR: 主要转录文本 {"additions":{"log_id":"202507301108479A1993F0EA20ABA39668"},"text":"第一个文件是我们本期的一个事物点 property 文件，需要把","utterances":[{"definite":false,"end_time":26411,"start_time":22592,"text":"第一个文件是我们本期的一个事物点 property 文件，需要把","words":[{"end_time":22671,"start_time":22592,"text":"第"},{"end_time":22751,"start_time":22671,"text":"一"},{"end_time":22831,"start_time":22751,"text":"个"},{"end_time":22991,"start_time":22911,"text":"文件"},{"end_time":23311,"start_time":23231,"text":"是"},{"end_time":23391,"start_time":23311,"text":"我们"},{"end_time":23792,"start_time":23712,"text":"本期"},{"end_time":24032,"start_time":23952,"text":"的"},{"end_time":24112,"start_time":24032,"text":"一"},{"end_time":24192,"start_time":24112,"text":"个"},{"end_time":24512,"start_time":24432,"text":"事物"},{"end_time":24911,"start_time":24831,"text":"点"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":25151,"start_time":25071,"text":"property"},{"end_time":-1,"start_time":-1,"text":" "},{"end_time":25471,"start_time":25391,"text":"文件"},{"end_time":26112,"start_time":26032,"text":"需要"},{"end_time":26411,"start_time":26331,"text":"把"}]}]}
SystemASR: 处理utterances转录 {
  count: 1,
  currentText: '第一个文件是我们本期的一个事物点 property 文件，需要把...',
  historyCount: 0
}
SystemASR: 当前转录 {
  text: '第一个文件是我们本期的一个事物点 property 文件，需要把',
  isFinal: false,
  startTime: 22592,
  endTime: 26411
}
安全读取: 位置=5118976, 文件大小=5165056, 待读取=46080字节
帧处理: 总数据=46080字节, 完整帧=46080字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=46080字节, 缓存完整帧=0字节, 有效帧=46080字节, 新不完整帧=0字节
当前文件位置: 5165056字节, 总读取: 5118976字节
发送音频数据: 7680字节, 3840样本, 能量=2500.02, 静音=false
SystemASR: 发送缓冲的音频数据，大小=7680字节
安全读取并处理 46080 字节音频数据，新位置: 5165056
SystemASR: 构建消息 - 序列号=86, 载荷大小=7125, 总大小=7137
SystemASR: 发送音频数据，序列号=86, 总大小=7137字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0311, Max=0.1465, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844959180 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=35 fc fc fd 02 00 85 01
MicrophoneASR: 音频数据分析 - 非零字节: 4998/5462 (91.50%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [-971, -516, 2, 389]
MicrophoneASR: 前8字节原始数据: 35 fc fc fd 02 00 85 01
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0311, Max=0.1465, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844959181 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0074, Max=0.0410, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844959387 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=8192字节, 采样率=16000Hz, 前8字节=1c 00 b9 00 94 00 bf ff
MicrophoneASR: 音频数据分析 - 非零字节: 6573/8192 (80.24%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=8192字节
MicrophoneASR: 前4个样本值: [28, 185, 148, -65]
MicrophoneASR: 前8字节原始数据: 1c 00 b9 00 94 00 bf ff
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节, 处理后长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 8192
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0074, Max=0.0403, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844959388 处理麦克风音频数据，长度=8192字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=8192字节
MicrophoneASR: 准备发送音频数据 - 长度: 8192字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=8200字节, 原始长度=8192字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=5165056, 文件大小=5226496, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
当前文件位置: 5226496字节, 总读取: 5165056字节
发送音频数据: 10240字节, 5120样本, 能量=2176.62, 静音=false
安全读取并处理 61440 字节音频数据，新位置: 5226496
SystemASR: 构建消息 - 序列号=87, 载荷大小=9440, 总大小=9452
SystemASR: 发送音频数据，序列号=87, 总大小=9452字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0112, Max=0.0503, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844959602 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 原始音频数据 - 长度=5462字节, 采样率=16000Hz, 前8字节=4c 00 43 00 8f 00 49 01
MicrophoneASR: 音频数据分析 - 非零字节: 4620/5462 (84.58%)
MicrophoneASR: 采样率已是16000Hz，无需转换
MicrophoneASR: 音频数据验证 - 长度=5462字节
MicrophoneASR: 前4个样本值: [76, 67, 143, 329]
MicrophoneASR: 前8字节原始数据: 4c 00 43 00 8f 00 49 01
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节, 处理后长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 5462
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🔊 MicrophoneASR: 音频分析 - RMS=0.0112, Max=0.0571, 阈值=0.01, 静音=false
🎵 MicrophoneASR: 1753844959603 处理麦克风音频数据，长度=5462字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=5462字节
MicrophoneASR: 准备发送音频数据 - 长度: 5462字节, 采样率: 16000Hz, 格式: PCM16
MicrophoneASR: 发送音频数据，总大小=5470字节, 原始长度=5462字节
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🖱️ 检测到鼠标点击事件: { x: 473, y: 73, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 473, y: 73 },
  windowBounds: { x: 0, y: 0, width: 1792, height: 751 },
  toolbarBounds: { x: 101, y: 1, width: 1590, height: 749 },
  regionsCount: 5
}
使用渲染进程报告的工具条位置: { x: 101, y: 1, width: 1590, height: 749 }
鼠标位置 (473, 73) 在工具条范围内
点击在 voice-one-click-start 区域内
语音面板一键启动按钮被点击
🛑 收到停止语音识别流程请求: 系统音频=true, 麦克风=true
🔇 停止系统音频服务...
停止系统音频捕获
SystemASR: Stopping ASR session
SystemASR: Stopping session
开始终止系统音频捕获进程...
正在终止音频捕获进程 PID: 85902
✅ [2025-07-30T03:09:19.669Z] 进程操作: terminate_start (PID: 85902) - Starting termination process
第一步：尝试优雅终止进程...
✅ [2025-07-30T03:09:19.669Z] 进程操作: graceful_terminate (PID: 85902) - Sending SIGTERM
音频文件监控已停止，缓存已清理
IPC: 收到停止麦克风音频捕获请求
🛑 MicrophoneManager: 停止麦克风音频捕获
🔇 MicrophoneManager: 通知MicrophoneASRManager停止处理音频
MicrophoneASR: Stopping ASR session
MicrophoneASR: Stopping session
📡 MicrophoneManager: 发送停止录制状态到渲染进程
✅ MicrophoneManager: 已通知渲染进程录制停止
SystemAudioManager (重试): 音频捕获进程退出，代码: null, 信号: SIGTERM
✅ 进程已优雅退出
✅ [2025-07-30T03:09:19.694Z] 进程操作: graceful_exit (PID: 85902) - Process exited gracefully
✅ [2025-07-30T03:09:19.694Z] 进程操作: cleanup_complete (PID: 85902) - Process reference cleared
已删除系统音频输出文件
连接状态更新: systemAudio = false {
  systemASR: true,
  microphoneASR: true,
  systemAudio: false,
  microphone: true
}
✅ 系统音频捕获已停止
SystemASR: Stopping ASR session
SystemASR: Stopping session
连接状态更新: systemASR = false {
  systemASR: false,
  microphoneASR: true,
  systemAudio: false,
  microphone: true
}
✅ 系统音频ASR连接已关闭
🎤 停止麦克风服务...
🛑 MicrophoneManager: 停止麦克风音频捕获
⚠️ MicrophoneManager: 麦克风未在录制中
连接状态更新: microphone = false {
  systemASR: false,
  microphoneASR: true,
  systemAudio: false,
  microphone: false
}
✅ 麦克风捕获已停止
MicrophoneASR: Stopping ASR session
MicrophoneASR: Stopping session
连接状态更新: microphoneASR = false {
  systemASR: false,
  microphoneASR: false,
  systemAudio: false,
  microphone: false
}
✅ 麦克风ASR连接已关闭
📊 停止后连接状态: {
  systemASR: false,
  microphoneASR: false,
  systemAudio: false,
  microphone: false
}
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🖱️ 检测到鼠标点击事件: { x: 1352, y: 1255, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 1352, y: 1255 },
  windowBounds: { x: 0, y: 0, width: 1792, height: 751 },
  toolbarBounds: { x: 101, y: 1, width: 1590, height: 749 },
  regionsCount: 5
}
使用渲染进程报告的工具条位置: { x: 101, y: 1, width: 1590, height: 749 }
鼠标位置 (1352, 1255) 不在工具条范围内
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
App 'before-quit' event fired
正在停止鼠标监听程序...
开始停止ioHook...
ioHook事件监听器已移除
ioHook.stop()调用完成
ioHook停止完成
正在清理全局快捷键...
快捷键 Alt+C 已注销
快捷键 Alt+V 已注销
快捷键 Alt+A 已注销
快捷键 CommandOrControl+Left 已注销
快捷键 CommandOrControl+Right 已注销
快捷键 CommandOrControl+Down 已注销
快捷键 CommandOrControl+Up 已注销
快捷键 CommandOrControl+B 已注销
快捷键 Alt+Z 已注销
快捷键 Alt+2 已注销
快捷键 Alt+3 已注销
快捷键 Alt+1 已注销
快捷键 CommandOrControl+Q 已注销
所有快捷键已注销
键盘钩子已停用
正在取消处理请求...
App 'will-quit' event fired
App 'quit' event fired - 开始最终清理
清理 SystemAudioManager...
停止系统音频捕获
系统音频捕获未启动，无需停止
清理 MicrophoneManager...
🛑 MicrophoneManager: 停止麦克风音频捕获
⚠️ MicrophoneManager: 麦克风未在录制中
所有资源清理完成
