{"appId": "interview.coder.id", "productName": "SecureKernel", "mac": {"identity": "Developer ID Application: Your Name (TEAM_ID)", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist", "notarize": {"teamId": "your-team-id", "appleId": "<EMAIL>", "appleIdPassword": "your-app-specific-password"}}, "win": {"signingHashAlgorithms": ["sha256"], "sign": false}, "linux": {"sign": false, "checksums": true}}