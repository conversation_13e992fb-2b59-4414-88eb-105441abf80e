#!/usr/bin/env node

/**
 * 代码混淆构建脚本
 * 用于在构建过程中自动混淆JavaScript代码
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
// 尝试加载混淆配置，优先使用简化版本
let obfuscationConfig;
try {
  obfuscationConfig = require('../obfuscation-simple.config.js');
} catch (error) {
  try {
    obfuscationConfig = require('../obfuscation.config.js');
  } catch (error2) {
    console.error('❌ 无法加载混淆配置文件');
    process.exit(1);
  }
}

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

// 确保目录存在
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// 获取所有JavaScript文件
function getJSFiles(dir, extensions = ['.js', '.ts']) {
  const files = [];
  
  function traverse(currentDir) {
    if (!fs.existsSync(currentDir)) {
      return;
    }
    
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // 跳过node_modules和其他不需要的目录
        if (!['node_modules', '.git', 'dist', 'release'].includes(item)) {
          traverse(fullPath);
        }
      } else if (stat.isFile()) {
        const ext = path.extname(item);
        if (extensions.includes(ext)) {
          files.push(fullPath);
        }
      }
    }
  }
  
  traverse(dir);
  return files;
}

// 验证预加载脚本的关键API
function validatePreloadScript(code, filePath) {
  const criticalAPIs = [
    'contextBridge',
    'exposeInMainWorld',
    'ipcRenderer',
    'electronAPI'
  ];

  const missingAPIs = criticalAPIs.filter(api => !code.includes(api));

  if (missingAPIs.length > 0) {
    logWarning(`预加载脚本可能缺少关键API: ${missingAPIs.join(', ')} in ${filePath}`);
    return false;
  }

  return true;
}

// 验证主进程脚本的关键功能
function validateMainProcessScript(code, filePath) {
  const criticalAPIs = [
    'BrowserWindow',
    'createWindow',
    'app.whenReady',
    'ipcMain',
    'loadURL'
  ];

  const missingAPIs = criticalAPIs.filter(api => !code.includes(api));

  if (missingAPIs.length > 0) {
    logWarning(`主进程脚本可能缺少关键API: ${missingAPIs.join(', ')} in ${filePath}`);
    return false;
  }

  // 检查窗口创建逻辑
  if (!code.includes('new BrowserWindow') && !code.includes('BrowserWindow(')) {
    logWarning(`主进程脚本可能缺少窗口创建逻辑 in ${filePath}`);
    return false;
  }

  return true;
}

// 验证渲染进程脚本的关键功能
function validateRendererScript(code, filePath) {
  const criticalAPIs = [
    'React',
    'ReactDOM',
    'createRoot',
    'window.electronAPI'
  ];

  const missingAPIs = criticalAPIs.filter(api => !code.includes(api));

  if (missingAPIs.length > 0) {
    logWarning(`渲染进程脚本可能缺少关键API: ${missingAPIs.join(', ')} in ${filePath}`);
    return false;
  }

  // 检查React渲染逻辑
  if (!code.includes('createRoot') && !code.includes('render')) {
    logWarning(`渲染进程脚本可能缺少React渲染逻辑 in ${filePath}`);
    return false;
  }

  return true;
}

// 通用代码质量验证
function validateCodeQuality(originalCode, obfuscatedCode, filePath) {
  const issues = [];

  // 检查代码是否被过度压缩
  const originalLines = originalCode.split('\n').length;
  const obfuscatedLines = obfuscatedCode.split('\n').length;

  if (obfuscatedLines === 1 && originalLines > 20) {
    issues.push('代码被压缩为单行，可能影响调试');
  }

  // 检查代码大小变化
  const originalSize = Buffer.byteLength(originalCode, 'utf8');
  const obfuscatedSize = Buffer.byteLength(obfuscatedCode, 'utf8');
  const sizeRatio = obfuscatedSize / originalSize;

  if (sizeRatio > 3) {
    issues.push(`代码大小增加过多 (${(sizeRatio * 100).toFixed(1)}%)，可能影响性能`);
  }

  // 检查是否包含语法错误标识
  if (obfuscatedCode.includes('SyntaxError') || obfuscatedCode.includes('ReferenceError')) {
    issues.push('混淆后的代码可能包含语法错误');
  }

  // 检查关键字符串是否被意外混淆
  const criticalStrings = [
    'electronAPI',
    'contextBridge',
    'ipcRenderer',
    'exposeInMainWorld'
  ];

  const missingStrings = criticalStrings.filter(str =>
    originalCode.includes(str) && !obfuscatedCode.includes(str)
  );

  if (missingStrings.length > 0) {
    issues.push(`关键字符串可能被意外混淆: ${missingStrings.join(', ')}`);
  }

  if (issues.length > 0) {
    logWarning(`代码质量问题 in ${filePath}:`);
    issues.forEach(issue => logWarning(`  - ${issue}`));
    return false;
  }

  return true;
}

// 混淆单个文件
function obfuscateFile(inputPath, outputPath, config) {
  try {
    logInfo(`混淆文件: ${path.relative(process.cwd(), inputPath)}`);

    const originalCode = fs.readFileSync(inputPath, 'utf8');
    const obfuscatedCode = obfuscationConfig.obfuscate(originalCode, config);

    // 根据文件类型进行特定验证
    let validationPassed = true;

    if (inputPath.includes('preload')) {
      logInfo(`验证预加载脚本: ${path.basename(inputPath)}`);

      if (!validatePreloadScript(obfuscatedCode, inputPath)) {
        logWarning(`预加载脚本混淆后可能存在问题`);
        validationPassed = false;
      }
    } else if (inputPath.includes('main.js') && !inputPath.includes('dist/')) {
      logInfo(`验证主进程脚本: ${path.basename(inputPath)}`);

      if (!validateMainProcessScript(obfuscatedCode, inputPath)) {
        logWarning(`主进程脚本混淆后可能存在问题`);
        validationPassed = false;
      }
    } else if (inputPath.includes('dist/') && inputPath.endsWith('.js')) {
      logInfo(`验证渲染进程脚本: ${path.basename(inputPath)}`);

      if (!validateRendererScript(obfuscatedCode, inputPath)) {
        logWarning(`渲染进程脚本混淆后可能存在问题`);
        validationPassed = false;
      }
    }

    // 通用代码质量验证
    if (!validateCodeQuality(originalCode, obfuscatedCode, inputPath)) {
      validationPassed = false;
    }

    // 如果验证失败，询问是否继续
    if (!validationPassed) {
      logWarning(`文件 ${path.basename(inputPath)} 混淆验证失败，但仍将保存混淆结果`);
      logWarning(`建议检查混淆配置或考虑降低混淆强度`);
    }

    ensureDir(path.dirname(outputPath));
    fs.writeFileSync(outputPath, obfuscatedCode);

    const originalSize = Buffer.byteLength(originalCode, 'utf8');
    const obfuscatedSize = Buffer.byteLength(obfuscatedCode, 'utf8');
    const ratio = ((obfuscatedSize / originalSize) * 100).toFixed(1);

    const statusIcon = validationPassed ? '✅' : '⚠️';
    logSuccess(`${statusIcon} 已混淆: ${path.relative(process.cwd(), outputPath)} (${ratio}% 原始大小)`);

    return validationPassed;
  } catch (error) {
    logError(`混淆失败 ${inputPath}: ${error.message}`);
    return false;
  }
}

// 混淆主进程文件
function obfuscateMainProcess() {
  logInfo('开始混淆主进程文件...');
  
  const mainFiles = [
    'dist-electron/main.js',
    'dist-electron/preload.js'
  ];
  
  let successCount = 0;
  
  for (const file of mainFiles) {
    if (fs.existsSync(file)) {
      let config;
      let description;

      if (file.includes('preload')) {
        config = obfuscationConfig.preload;
        description = '预加载脚本';
        logInfo(`使用预加载脚本专用配置处理: ${file}`);
      } else {
        config = obfuscationConfig.mainProcess;
        description = '主进程文件';
      }

      const outputPath = file.replace('.js', '.obfuscated.js');

      logInfo(`正在混淆${description}: ${path.basename(file)}`);

      if (obfuscateFile(file, outputPath, config)) {
        // 替换原文件
        fs.renameSync(outputPath, file);
        successCount++;
        logSuccess(`${description}混淆成功: ${path.basename(file)}`);
      } else {
        logError(`${description}混淆失败: ${path.basename(file)}`);
      }
    } else {
      logWarning(`文件不存在: ${file}`);
    }
  }
  
  logSuccess(`主进程混淆完成: ${successCount}/${mainFiles.length} 文件`);
  return successCount;
}

// 混淆渲染进程文件
function obfuscateRendererProcess() {
  logInfo('开始混淆渲染进程文件...');
  
  const distDir = 'dist';
  if (!fs.existsSync(distDir)) {
    logWarning('渲染进程构建目录不存在，跳过混淆');
    return 0;
  }
  
  const jsFiles = getJSFiles(distDir, ['.js']).filter(file => 
    !file.includes('.map') && 
    !file.includes('node_modules')
  );
  
  let successCount = 0;
  
  for (const file of jsFiles) {
    const outputPath = file.replace('.js', '.obfuscated.js');
    
    if (obfuscateFile(file, outputPath, obfuscationConfig.rendererProcess)) {
      // 替换原文件
      fs.renameSync(outputPath, file);
      successCount++;
    }
  }
  
  logSuccess(`渲染进程混淆完成: ${successCount}/${jsFiles.length} 文件`);
  return successCount;
}

// 创建混淆报告
function createObfuscationReport(mainCount, rendererCount) {
  const report = {
    timestamp: new Date().toISOString(),
    mainProcessFiles: mainCount,
    rendererProcessFiles: rendererCount,
    totalFiles: mainCount + rendererCount,
    obfuscationConfig: {
      production: true,
      stringArrayEncoding: 'rc4',
      controlFlowFlattening: true,
      deadCodeInjection: true,
      debugProtection: true,
      selfDefending: true
    }
  };
  
  const reportPath = 'dist/obfuscation-report.json';
  ensureDir(path.dirname(reportPath));
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  logSuccess(`混淆报告已生成: ${reportPath}`);
}

// 主函数
function main() {
  log('\n🔒 开始代码混淆过程...', colors.bright);
  
  const startTime = Date.now();
  
  try {
    // 检查构建文件是否存在
    if (!fs.existsSync('dist-electron') && !fs.existsSync('dist')) {
      logError('未找到构建文件，请先运行构建命令');
      process.exit(1);
    }
    
    // 混淆主进程
    const mainCount = obfuscateMainProcess();
    
    // 混淆渲染进程
    const rendererCount = obfuscateRendererProcess();
    
    // 创建报告
    createObfuscationReport(mainCount, rendererCount);
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    log('\n🎉 代码混淆完成!', colors.bright + colors.green);
    logSuccess(`总计混淆文件: ${mainCount + rendererCount}`);
    logSuccess(`耗时: ${duration} 秒`);
    
  } catch (error) {
    logError(`混淆过程出错: ${error.message}`);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  obfuscateMainProcess,
  obfuscateRendererProcess,
  createObfuscationReport,
  main
};
