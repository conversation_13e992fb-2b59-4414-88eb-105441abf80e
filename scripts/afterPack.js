/**
 * This script runs after electron-builder packs the application.
 * It removes unnecessary files to reduce the final package size.
 */
const fs = require('fs');
const path = require('path');

/**
 * Recursively delete unnecessary files and directories
 * @param {string} dir - The directory to clean
 * @param {RegExp[]} patterns - Array of RegExp patterns to match files/dirs to delete
 * @param {RegExp[]} keepPatterns - Array of RegExp patterns for files/dirs to keep
 * @param {boolean} [isRoot=true] - Whether this is the root directory call
 */
function cleanDir(dir, patterns, keepPatterns = [], isRoot = true) {
  if (!fs.existsSync(dir)) return;

  const entries = fs.readdirSync(dir, { withFileTypes: true });
  let sizeReduction = 0;
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    
    // Skip if explicitly marked to keep
    if (keepPatterns.some(pattern => pattern.test(entry.name))) {
      if (entry.isDirectory()) {
        // Still clean inside directories we're keeping
        sizeReduction += cleanDir(fullPath, patterns, keepPatterns, false);
      }
      continue;
    }
    
    // Check if the entry matches any of the patterns to delete
    if (patterns.some(pattern => pattern.test(entry.name))) {
      let size = 0;
      
      // Calculate size before deleting
      if (entry.isDirectory()) {
        size = getFolderSize(fullPath);
        fs.rmSync(fullPath, { recursive: true, force: true });
      } else {
        size = fs.statSync(fullPath).size;
        fs.unlinkSync(fullPath);
      }
      
      sizeReduction += size;
      console.log(`Removed: ${fullPath} (${formatSize(size)})`);
      continue;
    }
    
    // Recursively process subdirectories
    if (entry.isDirectory()) {
      sizeReduction += cleanDir(fullPath, patterns, keepPatterns, false);
    }
  }
  
  if (isRoot) {
    console.log(`Total size reduction: ${formatSize(sizeReduction)}`);
  }
  
  return sizeReduction;
}

/**
 * Calculate the size of a folder recursively
 * @param {string} dirPath - Path to the directory
 * @returns {number} Total size in bytes
 */
function getFolderSize(dirPath) {
  let size = 0;
  
  if (!fs.existsSync(dirPath)) return size;
  
  const files = fs.readdirSync(dirPath);
  
  for (const file of files) {
    const filePath = path.join(dirPath, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      size += getFolderSize(filePath);
    } else {
      size += stats.size;
    }
  }
  
  return size;
}

/**
 * Format bytes into human-readable format
 * @param {number} bytes - Number of bytes
 * @returns {string} Formatted size
 */
function formatSize(bytes) {
  if (bytes < 1024) return bytes + ' B';
  else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
  else return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
}

exports.default = async function(context) {
  const { appOutDir } = context;
  console.log(`Cleaning directory: ${appOutDir}`);

  // Patterns for files and directories to remove
  const patternsToRemove = [
    /\.d\.ts$/,                   // TypeScript declaration files
    /\.map$/,                     // Source map files
    /LICENSE/i,                   // License files (case insensitive)
    /\.md$/i,                     // Markdown files (case insensitive)
    /-min\.js\.map$/,             // Minified JS source maps
    /^test$/i,                    // Test directories (case insensitive)
    /^tests$/i,                   // Test directories (case insensitive)
    /^__tests__$/,                // Jest test directories
    /^docs$/i,                    // Documentation directories (case insensitive)
    /^examples$/i,                // Example directories (case insensitive)
    /\.git/,                      // Git directories
    /\.github/,                   // GitHub directories
    /package-lock\.json$/,        // Package lock files
    /yarn\.lock$/,                // Yarn lock files
    /thumbs\.db$/i,               // Windows image cache files
    /\.DS_Store$/,                // macOS folder attribute files
    /^node_modules\/\.bin$/,      // Node.js bin directory
    /\.ts$/,                      // TypeScript source files (already compiled)
    /\.tsx$/,                     // TypeScript React files (already compiled)
    /^\.vscode$/,                 // VSCode config
    /\.eslintrc$/,                // ESLint config
    /tsconfig\.json$/,            // TypeScript config
    /jestconfig\.json$/,          // Jest config
    /\.babelrc$/,                 // Babel config
    /\.npmignore$/,               // npm ignore file
    /\.editorconfig$/,            // Editor config
    /\.travis\.yml$/,             // Travis CI config
    /^coverage$/,                 // Coverage reports
    /^\.nyc_output$/,             // NYC output
    /CONTRIBUTING\.md$/i,         // Contributing guidelines
    /CHANGELOG\.md$/i,            // Changelog
    /AUTHORS$/i,                  // Authors file
    /HISTORY$/i,                  // History file
    /^man$/,                      // Man pages
    /\.jshintrc$/,                // JSHint config
    /\.npmrc$/                    // npm config
  ];
  
  // Patterns for files and directories to keep
  const patternsToKeep = [
    /^node_modules$/,             // Keep the node_modules directory itself (but clean inside)
    /^locales$/                   // Keep the locales directory itself (but clean inside)
  ];

  cleanDir(appOutDir, patternsToRemove, patternsToKeep);

  // Remove unnecessary locales
  const localesDir = path.join(appOutDir, 'locales');
  if (fs.existsSync(localesDir)) {
    // Keep only essential locales (en-US and zh-CN)
    const localesToKeep = ['en-US', 'zh-CN'];
    let localeSize = 0;
    
    fs.readdirSync(localesDir).forEach(file => {
      const localeName = file.replace('.pak', '');
      const filePath = path.join(localesDir, file);
      
      if (!localesToKeep.includes(localeName)) {
        localeSize += fs.statSync(filePath).size;
        fs.unlinkSync(filePath);
        console.log(`Removed locale: ${localeName}`);
      }
    });
    
    console.log(`Removed ${formatSize(localeSize)} of unnecessary locales`);
  }
  console.log('Package cleaning completed');
};