#!/usr/bin/env node

/**
 * 代码签名配置脚本
 * 用于配置和管理应用的代码签名
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { execSync } = require('child_process');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

// 签名配置
const SIGNING_CONFIG = {
  // 应用标识符
  appId: 'interview.coder.id',
  // 开发者ID（需要从环境变量或配置文件获取）
  developerId: process.env.DEVELOPER_ID || 'Developer ID Application: Your Name (TEAM_ID)',
  // 证书名称
  certificateName: process.env.CERTIFICATE_NAME || 'Developer ID Application',
  // 公证配置
  notarization: {
    teamId: process.env.APPLE_TEAM_ID,
    appleId: process.env.APPLE_ID,
    appleIdPassword: process.env.APPLE_ID_PASSWORD
  }
};

/**
 * 检查签名环境
 */
function checkSigningEnvironment() {
  logInfo('检查代码签名环境...');
  
  const platform = process.platform;
  logInfo(`当前平台: ${platform}`);
  
  if (platform === 'darwin') {
    return checkMacOSSigningEnvironment();
  } else if (platform === 'win32') {
    return checkWindowsSigningEnvironment();
  } else {
    logWarning('当前平台不支持代码签名');
    return false;
  }
}

/**
 * 检查macOS签名环境
 */
function checkMacOSSigningEnvironment() {
  try {
    // 检查是否安装了Xcode命令行工具
    execSync('xcode-select --version', { stdio: 'pipe' });
    logSuccess('Xcode命令行工具已安装');
    
    // 检查可用的签名证书
    const certificates = execSync('security find-identity -v -p codesigning', { encoding: 'utf8' });
    
    if (certificates.includes('Developer ID Application')) {
      logSuccess('找到Developer ID Application证书');
      return true;
    } else {
      logWarning('未找到Developer ID Application证书');
      logInfo('可用证书:');
      console.log(certificates);
      return false;
    }
  } catch (error) {
    logError(`macOS签名环境检查失败: ${error.message}`);
    return false;
  }
}

/**
 * 检查Windows签名环境
 */
function checkWindowsSigningEnvironment() {
  try {
    // 检查是否有签名工具
    execSync('signtool', { stdio: 'pipe' });
    logSuccess('Windows SDK签名工具已安装');
    
    logInfo('Windows证书存储检查完成');
    
    return true;
  } catch (error) {
    logWarning('Windows签名工具未找到，将跳过代码签名');
    return false;
  }
}

/**
 * 生成自签名证书（用于开发和测试）
 */
function generateSelfSignedCertificate() {
  logInfo('生成自签名证书...');
  
  try {
    const certDir = path.join(process.cwd(), 'certificates');
    if (!fs.existsSync(certDir)) {
      fs.mkdirSync(certDir, { recursive: true });
    }
    
    const keyPath = path.join(certDir, 'private-key.pem');
    const certPath = path.join(certDir, 'certificate.pem');
    
    // 生成私钥
    const privateKey = crypto.generateKeyPairSync('rsa', {
      modulusLength: 2048,
      privateKeyEncoding: {
        type: 'pkcs8',
        format: 'pem'
      },
      publicKeyEncoding: {
        type: 'spki',
        format: 'pem'
      }
    });
    
    fs.writeFileSync(keyPath, privateKey.privateKey);
    
    // 创建证书信息
    const certInfo = {
      subject: {
        commonName: 'SecureKernel Development',
        organizationName: 'Development Team',
        countryName: 'US'
      },
      issuer: {
        commonName: 'SecureKernel Development CA',
        organizationName: 'Development Team',
        countryName: 'US'
      },
      serialNumber: crypto.randomBytes(16).toString('hex'),
      validFrom: new Date(),
      validTo: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1年有效期
    };
    
    // 创建简单的证书内容
    const certContent = `-----BEGIN CERTIFICATE-----
${Buffer.from(JSON.stringify(certInfo)).toString('base64')}
-----END CERTIFICATE-----`;
    
    fs.writeFileSync(certPath, certContent);
    
    logSuccess(`自签名证书已生成:`);
    logInfo(`私钥: ${keyPath}`);
    logInfo(`证书: ${certPath}`);
    
    return {
      keyPath,
      certPath,
      info: certInfo
    };
  } catch (error) {
    logError(`生成自签名证书失败: ${error.message}`);
    return null;
  }
}

/**
 * 创建签名配置文件
 */
function createSigningConfig() {
  logInfo('创建签名配置文件...');
  
  const config = {
    // 基础配置
    appId: SIGNING_CONFIG.appId,
    productName: 'SecureKernel',
    
    // macOS签名配置
    mac: {
      identity: SIGNING_CONFIG.developerId,
      hardenedRuntime: true,
      gatekeeperAssess: false,
      entitlements: 'build/entitlements.mac.plist',
      entitlementsInherit: 'build/entitlements.mac.plist',
      notarize: SIGNING_CONFIG.notarization.teamId ? {
        teamId: SIGNING_CONFIG.notarization.teamId,
        appleId: SIGNING_CONFIG.notarization.appleId,
        appleIdPassword: SIGNING_CONFIG.notarization.appleIdPassword
      } : false
    },
    
    // Windows签名配置
    win: {
      certificateFile: process.env.WINDOWS_CERTIFICATE_FILE,
      certificatePassword: process.env.WINDOWS_CERTIFICATE_PASSWORD,
      signingHashAlgorithms: ['sha256'],
      sign: !!process.env.WINDOWS_CERTIFICATE_FILE
    },
    
    // Linux配置（AppImage不需要签名，但可以添加校验和）
    linux: {
      sign: false,
      checksums: true
    }
  };
  
  const configPath = path.join(process.cwd(), 'build', 'signing-config.json');
  const buildDir = path.dirname(configPath);
  
  if (!fs.existsSync(buildDir)) {
    fs.mkdirSync(buildDir, { recursive: true });
  }
  
  fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
  
  logSuccess(`签名配置文件已创建: ${configPath}`);
  return config;
}

/**
 * 主函数
 */
function main() {
  log('\n🔏 开始代码签名配置...', colors.bright);
  
  const startTime = Date.now();
  
  try {
    // 1. 检查签名环境
    const hasSigningEnvironment = checkSigningEnvironment();
    
    // 2. 创建签名配置
    const config = createSigningConfig();
    
    // 3. 如果没有签名环境，生成自签名证书用于开发
    if (!hasSigningEnvironment) {
      logWarning('未检测到签名环境，生成开发用自签名证书');
      generateSelfSignedCertificate();
    }
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    log('\n🎉 代码签名配置完成!', colors.bright + colors.green);
    logSuccess(`耗时: ${duration} 秒`);
    
    if (hasSigningEnvironment) {
      logSuccess('代码签名环境已就绪');
    } else {
      logWarning('请配置正式的代码签名证书以用于生产环境');
    }
    
  } catch (error) {
    logError(`代码签名配置失败: ${error.message}`);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  checkSigningEnvironment,
  generateSelfSignedCertificate,
  createSigningConfig,
  main,
  SIGNING_CONFIG
};
