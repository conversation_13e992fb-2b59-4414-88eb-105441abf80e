#!/usr/bin/env node

/**
 * 安全构建总结脚本
 * 显示所有安全配置的状态和构建结果
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function logHeader(message) {
  log(`\n🔒 ${message}`, colors.bright + colors.cyan);
  log('='.repeat(50), colors.cyan);
}

/**
 * 检查安全配置文件
 */
function checkSecurityFiles() {
  logHeader('安全配置文件检查');
  
  const securityFiles = [
    { path: 'obfuscation.config.js', name: '代码混淆配置' },
    { path: 'scripts/obfuscate.js', name: '代码混淆脚本' },
    { path: 'scripts/asar-encryption.js', name: 'ASAR加密脚本' },
    { path: 'scripts/code-signing.js', name: '代码签名脚本' },
    { path: 'scripts/afterPack.js', name: '打包后处理脚本' },
    { path: 'electron/security/anti-debug.js', name: '反调试保护' },
    { path: 'src/security/anti-debug-renderer.ts', name: '渲染进程反调试' },
    { path: 'electron/security/runtime-protection.js', name: '运行时保护' },
    { path: 'build/entitlements.mac.plist', name: 'macOS权限文件' }
  ];
  
  let existingFiles = 0;
  
  for (const file of securityFiles) {
    if (fs.existsSync(file.path)) {
      logSuccess(`${file.name}: ${file.path}`);
      existingFiles++;
    } else {
      logError(`${file.name}: ${file.path} (缺失)`);
    }
  }
  
  logInfo(`安全文件完整性: ${existingFiles}/${securityFiles.length} (${((existingFiles/securityFiles.length)*100).toFixed(1)}%)`);
  
  return existingFiles === securityFiles.length;
}

/**
 * 检查package.json安全配置
 */
function checkPackageJsonSecurity() {
  logHeader('Package.json 安全配置检查');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    // 检查构建配置
    const build = packageJson.build || {};
    
    // ASAR配置
    if (build.asar && typeof build.asar === 'object') {
      logSuccess('ASAR配置: 已启用高级配置');
    } else if (build.asar === true) {
      logWarning('ASAR配置: 基础配置 (建议使用高级配置)');
    } else {
      logError('ASAR配置: 未启用');
    }
    
    // 压缩配置
    if (build.compression === 'maximum') {
      logSuccess('压缩配置: 最大压缩');
    } else {
      logWarning(`压缩配置: ${build.compression || '未设置'}`);
    }
    
    // macOS安全配置
    const mac = build.mac || {};
    if (mac.hardenedRuntime) {
      logSuccess('macOS强化运行时: 已启用');
    } else {
      logWarning('macOS强化运行时: 未启用');
    }
    
    if (mac.entitlements) {
      logSuccess(`macOS权限文件: ${mac.entitlements}`);
    } else {
      logWarning('macOS权限文件: 未配置');
    }
    
    // 安全脚本
    const scripts = packageJson.scripts || {};
    const securityScripts = ['obfuscate', 'build:secure'];
    
    for (const script of securityScripts) {
      if (scripts[script]) {
        logSuccess(`安全脚本 ${script}: 已配置`);
      } else {
        logWarning(`安全脚本 ${script}: 未配置`);
      }
    }
    
    return true;
  } catch (error) {
    logError(`Package.json检查失败: ${error.message}`);
    return false;
  }
}

/**
 * 检查构建输出
 */
function checkBuildOutput() {
  logHeader('构建输出检查');
  
  const outputDirs = [
    { path: 'dist', name: '渲染进程构建输出' },
    { path: 'dist-electron', name: '主进程构建输出' },
    { path: 'release', name: '最终打包输出' }
  ];
  
  for (const dir of outputDirs) {
    if (fs.existsSync(dir.path)) {
      const files = fs.readdirSync(dir.path);
      logSuccess(`${dir.name}: ${files.length} 个文件`);
      
      // 检查关键文件
      if (dir.path === 'dist-electron') {
        const mainJs = path.join(dir.path, 'main.js');
        const preloadJs = path.join(dir.path, 'preload.js');
        
        if (fs.existsSync(mainJs)) {
          const content = fs.readFileSync(mainJs, 'utf8');
          if (content.includes('initAntiDebug') || content.includes('initRuntimeProtection')) {
            logSuccess('主进程安全保护: 已集成');
          } else {
            logWarning('主进程安全保护: 未检测到');
          }
        }
      }
      
      if (dir.path === 'release') {
        // 检查完整性文件
        const integrityFiles = files.filter(f => f.includes('integrity') || f.includes('signature'));
        if (integrityFiles.length > 0) {
          logSuccess(`完整性文件: ${integrityFiles.length} 个`);
        } else {
          logWarning('完整性文件: 未找到');
        }
      }
    } else {
      logWarning(`${dir.name}: 目录不存在`);
    }
  }
}

/**
 * 检查依赖安全性
 */
function checkDependencySecurity() {
  logHeader('依赖安全性检查');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const securityDeps = [
      'javascript-obfuscator',
      'crypto'
    ];
    
    const devDeps = packageJson.devDependencies || {};
    const deps = packageJson.dependencies || {};
    
    for (const dep of securityDeps) {
      if (devDeps[dep] || deps[dep]) {
        logSuccess(`安全依赖 ${dep}: 已安装`);
      } else {
        logWarning(`安全依赖 ${dep}: 未安装`);
      }
    }
    
    // 检查潜在的不安全依赖
    const allDeps = { ...deps, ...devDeps };
    const unsafeDeps = Object.keys(allDeps).filter(dep => 
      dep.includes('debug') || 
      dep.includes('inspector') || 
      dep.includes('devtools')
    );
    
    if (unsafeDeps.length > 0) {
      logWarning(`潜在不安全依赖: ${unsafeDeps.join(', ')}`);
    } else {
      logSuccess('未发现潜在不安全依赖');
    }
    
  } catch (error) {
    logError(`依赖检查失败: ${error.message}`);
  }
}

/**
 * 生成安全报告
 */
function generateSecurityReport() {
  logHeader('生成安全报告');
  
  const report = {
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    platform: process.platform,
    arch: process.arch,
    nodeVersion: process.version,
    security: {
      codeObfuscation: fs.existsSync('obfuscation.config.js'),
      asarEncryption: fs.existsSync('scripts/asar-encryption.js'),
      antiDebug: fs.existsSync('electron/security/anti-debug.js'),
      runtimeProtection: fs.existsSync('electron/security/runtime-protection.js'),
      codeSigning: fs.existsSync('scripts/code-signing.js')
    },
    build: {
      distExists: fs.existsSync('dist'),
      distElectronExists: fs.existsSync('dist-electron'),
      releaseExists: fs.existsSync('release')
    }
  };
  
  const reportPath = 'security-report.json';
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  logSuccess(`安全报告已生成: ${reportPath}`);
  
  // 计算安全评分
  const securityFeatures = Object.values(report.security);
  const enabledFeatures = securityFeatures.filter(Boolean).length;
  const securityScore = (enabledFeatures / securityFeatures.length) * 100;
  
  logInfo(`安全评分: ${securityScore.toFixed(1)}% (${enabledFeatures}/${securityFeatures.length} 功能启用)`);
  
  return report;
}

/**
 * 主函数
 */
function main() {
  log('\n🛡️ Electron应用安全构建总结', colors.bright + colors.magenta);
  log('='.repeat(60), colors.magenta);
  
  const startTime = Date.now();
  
  try {
    // 1. 检查安全配置文件
    const filesOk = checkSecurityFiles();
    
    // 2. 检查package.json配置
    const packageOk = checkPackageJsonSecurity();
    
    // 3. 检查构建输出
    checkBuildOutput();
    
    // 4. 检查依赖安全性
    checkDependencySecurity();
    
    // 5. 生成安全报告
    const report = generateSecurityReport();
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    // 总结
    logHeader('安全构建总结');
    
    if (filesOk && packageOk) {
      logSuccess('🎉 安全配置完整，应用已加固');
    } else {
      logWarning('⚠️ 部分安全配置缺失，建议完善');
    }
    
    logInfo(`检查耗时: ${duration} 秒`);
    logInfo(`安全评分: ${((Object.values(report.security).filter(Boolean).length / Object.values(report.security).length) * 100).toFixed(1)}%`);
    
    log('\n📋 安全功能状态:', colors.bright);
    for (const [feature, enabled] of Object.entries(report.security)) {
      const status = enabled ? '✅ 启用' : '❌ 禁用';
      log(`  ${feature}: ${status}`);
    }
    
    log('\n🚀 推荐的构建命令:', colors.bright + colors.green);
    log('  npm run build:secure    # 安全构建', colors.green);
    log('  npm run obfuscate       # 单独混淆', colors.green);
    log('  node scripts/code-signing.js  # 配置签名', colors.green);
    
  } catch (error) {
    logError(`安全检查失败: ${error.message}`);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  checkSecurityFiles,
  checkPackageJsonSecurity,
  checkBuildOutput,
  checkDependencySecurity,
  generateSecurityReport,
  main
};
