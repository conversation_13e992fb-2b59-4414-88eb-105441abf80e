/* eslint-disable no-bitwise */

import { CONST } from "./constants";
export const parseResponse = async (res, option) => {
  const arraybuffer = await convertBlobToArrayBuffer(res);
  const dataView = new DataView(arraybuffer);
  const header_size = dataView.getUint8(0) & 0x0f;
  const message_type = dataView.getUint8(1) >> 4;
  // 服务器错误响应：8byte描述字段
  const message_description_length = message_type === CONST.SERVER_ERROR_RESPONSE ? 8 : 4;
  const sequence_length = option !== null && option !== void 0 && option.hasSequence ? 4 : 0;
  const payloadBuffer = arraybuffer.slice(header_size * 4 + message_description_length + sequence_length);
  const uint8_msg = new Uint8Array(payloadBuffer);
  const enc = new TextDecoder('utf-8');
  const text = enc.decode(uint8_msg);
  const payload = JSON.parse(text);
  return payload;
};
export const encodeFullClientRequest = requestData => {
  const full_client_request_header = generateHeader();
  const json = JSON.stringify(requestData);
  full_client_request_header.setUint32(4, json.length, false);
  return new Blob([full_client_request_header, json]);
};
export const encodeAudioOnlyRequest = requestData => {
  const audio_only_request_header = generateHeader(CONST.CLIENT_AUDIO_ONLY_REQUEST);
  audio_only_request_header.setUint32(4, requestData.size, false);
  return new Blob([audio_only_request_header, requestData]);
};
const generateHeader = (message_type = CONST.CLIENT_FULL_REQUEST, version = CONST.PROTOCOL_VERSION, message_type_specific_flags = CONST.NO_SEQUENCE, serial_method = CONST.JSON, compression_type = CONST.NO_COMPRESSION, reserved_data = 0x00, extension_header = new ArrayBuffer(0)) => {
  const buffer = new ArrayBuffer(8);
  const dataView = new DataView(buffer);
  const header_size = Math.trunc(extension_header.byteLength / 4) + 1;
  dataView.setUint8(0, version << 4 | header_size);
  dataView.setUint8(1, message_type << 4 | message_type_specific_flags);
  dataView.setUint8(2, serial_method << 4 | compression_type);
  dataView.setUint8(3, reserved_data);
  return dataView;
};
async function convertBlobToArrayBuffer(blob) {
  return new Promise(resolve => {
    const reader = new FileReader();
    reader.onload = () => {
      resolve(reader.result);
    };
    reader.readAsArrayBuffer(blob);
  });
}