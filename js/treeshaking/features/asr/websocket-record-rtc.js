import _asyncToGenerator from "@babel/runtime/helpers/esm/asyncToGenerator";
import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import get from 'lodash-es/get';
import RecordRTC, { StereoAudioRecorder } from 'recordrtc';
import { encodeAudioOnlyRequest, encodeFullClientRequest, parseResponse } from "./utils";
export function WebsocketRecordRtc(params = {}) {
  const context = _objectSpread(_objectSpread({}, params), {}, {
    webSocket: undefined,
    mediaStream: undefined,
    recorder: undefined,
    streamArr: undefined
  });
  /**
   *
   * @param url 服务端提供Socket服务的URL接口
   * @param postData Socket建立连接时发送到服务端的数据
   * @param onStart Socket连接建立成功时的回调函数
   * @param onMessage Socket包到达时的处理函数
   * @param onClose Socket连接关闭时的回调函数
   */
  function connect({
    url,
    config,
    debug = false
  }) {
    const {
      onStart,
      onMessage,
      onClose,
      onError
    } = context;
    const socket = new WebSocket(url);
    context.webSocket = socket;
    socket.onopen = () => {
      debug && console.info('socket connected');
      const build = encodeFullClientRequest(config);
      socket.send(build);
      onStart === null || onStart === void 0 || onStart();
    };
    socket.onmessage = /*#__PURE__*/function () {
      var _ref = _asyncToGenerator(function* (event) {
        try {
          const isbigasr = get(config, 'request.model_name') === 'bigmodel';
          const res_json = yield parseResponse(event.data, {
            hasSequence: isbigasr
          });
          if (!isbigasr) {
            const text = res_json.result ? res_json.result[0].text : '';
            onMessage === null || onMessage === void 0 || onMessage(text, res_json);
          } else {
            const text = res_json.result ? res_json.result.text : '';
            onMessage === null || onMessage === void 0 || onMessage(text, res_json);
          }
        } catch (error) {
          console.error(error);
        }
      });
      return function (_x) {
        return _ref.apply(this, arguments);
      };
    }();
    socket.onclose = () => {
      debug && console.info('socket onclose disconnected');
      onClose === null || onClose === void 0 || onClose();
    };
    socket.onerror = () => {
      debug && console.info('socket onerror disconnected');
      onError === null || onError === void 0 || onError();
    };
  }
  function startRecord(_x2, _x3) {
    return _startRecord.apply(this, arguments);
  }
  function _startRecord() {
    _startRecord = _asyncToGenerator(function* (options, handleRecordResult) {
      context.mediaStream = yield window.navigator.mediaDevices.getUserMedia({
        audio: true
      });
      if (!context.streamArr) {
        context.streamArr = [context.mediaStream];
      } else {
        context.streamArr.push(context.mediaStream);
      }
      context.recorder = new RecordRTC(context.mediaStream, _objectSpread(_objectSpread({
        // 后端要求单通道，16K采样率，PCM
        type: 'audio',
        recorderType: StereoAudioRecorder,
        mimeType: 'audio/wav',
        numberOfAudioChannels: 1,
        desiredSampRate: 16000,
        disableLogs: true,
        timeSlice: 200,
        bufferSize: 4096
      }, options), {}, {
        ondataavailable(recordResult) {
          const socket = context.webSocket;
          if (socket) {
            handleRecordResult === null || handleRecordResult === void 0 || handleRecordResult(recordResult);
            const pcm = recordResult.slice(44);
            const data = encodeAudioOnlyRequest(pcm);
            if (socket.readyState === socket.OPEN) {
              socket.send(data);
            }
          }
        }
      }));
      context.recorder.startRecording();
    });
    return _startRecord.apply(this, arguments);
  }
  function stopRecord() {
    if (!context.recorder) {
      return;
    }
    context.recorder.stopRecording(() => {
      var _context$streamArr;
      if (!context.webSocket) {
        return;
      }
      context.webSocket.close();
      (_context$streamArr = context.streamArr) === null || _context$streamArr === void 0 || _context$streamArr.forEach(item => item.getAudioTracks().forEach(track => track.stop()));
      context.streamArr = undefined;
    });
  }
  return Object.freeze({
    connect,
    startRecord,
    stopRecord
  });
}