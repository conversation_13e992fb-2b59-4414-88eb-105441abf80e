const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Read package.json
const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '../package.json'), 'utf8'));

console.log('Analyzing dependencies...');
console.log('-------------------------');

// Separate production and dev dependencies
const prodDeps = Object.keys(packageJson.dependencies || {});
const devDeps = Object.keys(packageJson.devDependencies || {});

console.log(`Production dependencies: ${prodDeps.length}`);
console.log(`Development dependencies: ${devDeps.length}`);

// Check for potentially movable dependencies (ones that might only be needed at build time)
const potentialDevDeps = prodDeps.filter(dep => {
  return (
    // UI components that are compiled into the bundle
    dep.includes('react-') ||
    dep.includes('radix-ui') ||
    // Utility libraries that get bundled
    dep.includes('clsx') ||
    dep.includes('class-variance') ||
    dep.includes('tailwind-merge')
  );
});

if (potentialDevDeps.length > 0) {
  console.log('\nDependencies that might be movable to devDependencies:');
  potentialDevDeps.forEach(dep => {
    console.log(` - ${dep}`);
  });
}

// Check for large dependencies
console.log('\nChecking dependency sizes...');
try {
  const output = execSync('npm list --prod --parseable', { encoding: 'utf8' });
  const deps = output.split('\n').filter(Boolean);
  
  const sizeMap = {};
  
  // Calculate size of each dependency
  deps.forEach(depPath => {
    if (depPath.includes('node_modules')) {
      const name = depPath.split('node_modules/').pop();
      if (!name) return;
      
      try {
        const stats = getDirSize(depPath);
        const rootName = name.split('/')[0];
        sizeMap[rootName] = (sizeMap[rootName] || 0) + stats.size;
      } catch (err) {
        // Ignore file access errors
      }
    }
  });
  
  // Sort dependencies by size
  const sortedDeps = Object.entries(sizeMap)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10);
  
  console.log('\nTop 10 largest dependencies:');
  sortedDeps.forEach(([name, size]) => {
    console.log(` - ${name}: ${formatSize(size)}`);
  });
  
} catch (error) {
  console.error('Error analyzing dependencies:', error.message);
}

function getDirSize(dirPath) {
  let size = 0;
  let fileCount = 0;
  
  try {
    const files = fs.readdirSync(dirPath);
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory()) {
        const subDirStats = getDirSize(filePath);
        size += subDirStats.size;
        fileCount += subDirStats.fileCount;
      } else {
        size += stats.size;
        fileCount++;
      }
    }
  } catch (err) {
    // Ignore errors
  }
  
  return { size, fileCount };
}

function formatSize(bytes) {
  if (bytes < 1024) return bytes + ' B';
  else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
  else return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
} 