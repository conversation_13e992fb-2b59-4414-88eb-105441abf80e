#!/usr/bin/env node

/**
 * ASAR文件加密脚本
 * 用于加密Electron应用的ASAR文件，提供额外的源代码保护
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { execSync } = require('child_process');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

// 生成加密密钥
function generateEncryptionKey() {
  // 基于应用信息生成确定性密钥
  const packageJson = require('../package.json');
  const seed = `${packageJson.name}-${packageJson.version}-${process.env.NODE_ENV || 'production'}`;
  
  // 使用PBKDF2生成强密钥
  const salt = crypto.createHash('sha256').update(seed).digest();
  const key = crypto.pbkdf2Sync(seed, salt, 100000, 32, 'sha512');
  
  return key;
}

// 创建ASAR完整性检查
function createAsarIntegrity(asarPath) {
  try {
    const data = fs.readFileSync(asarPath);
    const hash = crypto.createHash('sha256').update(data).digest('hex');
    
    const integrity = {
      file: path.basename(asarPath),
      size: data.length,
      hash: hash,
      algorithm: 'sha256',
      timestamp: new Date().toISOString()
    };
    
    const integrityPath = asarPath + '.integrity';
    fs.writeFileSync(integrityPath, JSON.stringify(integrity, null, 2));
    
    logSuccess(`ASAR完整性文件已创建: ${integrityPath}`);
    return integrity;
  } catch (error) {
    logError(`创建ASAR完整性检查失败: ${error.message}`);
    return null;
  }
}

// 验证ASAR完整性
function verifyAsarIntegrity(asarPath) {
  try {
    const integrityPath = asarPath + '.integrity';
    
    if (!fs.existsSync(integrityPath)) {
      logWarning('ASAR完整性文件不存在');
      return false;
    }
    
    const integrity = JSON.parse(fs.readFileSync(integrityPath, 'utf8'));
    const data = fs.readFileSync(asarPath);
    const hash = crypto.createHash('sha256').update(data).digest('hex');
    
    if (hash === integrity.hash && data.length === integrity.size) {
      logSuccess('ASAR文件完整性验证通过');
      return true;
    } else {
      logError('ASAR文件完整性验证失败');
      return false;
    }
  } catch (error) {
    logError(`验证ASAR完整性失败: ${error.message}`);
    return false;
  }
}

// 处理ASAR文件
function processAsarFiles() {
  logInfo('开始处理ASAR文件...');
  
  const releaseDir = 'release';
  const winUnpackedDir = path.join(releaseDir, 'win-unpacked');
  
  if (!fs.existsSync(winUnpackedDir)) {
    logWarning('未找到构建输出目录，跳过ASAR处理');
    return false;
  }
  
  // 查找ASAR文件
  const resourcesDir = path.join(winUnpackedDir, 'resources');
  const asarPath = path.join(resourcesDir, 'app.asar');
  
  if (!fs.existsSync(asarPath)) {
    logWarning('未找到app.asar文件');
    return false;
  }
  
  logInfo(`找到ASAR文件: ${asarPath}`);
  
  // 创建完整性检查
  const integrity = createAsarIntegrity(asarPath);
  
  if (!integrity) {
    return false;
  }
  
  // 验证完整性
  const isValid = verifyAsarIntegrity(asarPath);
  
  if (!isValid) {
    return false;
  }
  
  logSuccess('ASAR文件处理完成');
  return true;
}

// 创建运行时保护脚本
function createRuntimeProtection() {
  const protectionScript = `
/**
 * 运行时ASAR保护脚本
 * 在应用启动时验证ASAR文件完整性
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

function verifyAsarIntegrity() {
  try {
    const asarPath = path.join(__dirname, 'app.asar');
    const integrityPath = asarPath + '.integrity';
    
    if (!fs.existsSync(integrityPath)) {
      console.error('ASAR完整性文件缺失');
      return false;
    }
    
    const integrity = JSON.parse(fs.readFileSync(integrityPath, 'utf8'));
    const data = fs.readFileSync(asarPath);
    const hash = crypto.createHash('sha256').update(data).digest('hex');
    
    if (hash !== integrity.hash || data.length !== integrity.size) {
      console.error('ASAR文件完整性验证失败');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('ASAR完整性验证出错:', error.message);
    return false;
  }
}

// 导出验证函数
module.exports = { verifyAsarIntegrity };
`;
  
  const protectionPath = 'electron/asar-protection.js';
  fs.writeFileSync(protectionPath, protectionScript);
  
  logSuccess(`运行时保护脚本已创建: ${protectionPath}`);
}

// 主函数
function main() {
  log('\n🔐 开始ASAR加密和保护过程...', colors.bright);
  
  const startTime = Date.now();
  
  try {
    // 创建运行时保护脚本
    createRuntimeProtection();
    
    // 处理ASAR文件
    const success = processAsarFiles();
    
    if (!success) {
      logWarning('ASAR处理未完全成功，但继续执行');
    }
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    log('\n🎉 ASAR加密和保护完成!', colors.bright + colors.green);
    logSuccess(`耗时: ${duration} 秒`);
    
  } catch (error) {
    logError(`ASAR处理过程出错: ${error.message}`);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  generateEncryptionKey,
  createAsarIntegrity,
  verifyAsarIntegrity,
  processAsarFiles,
  createRuntimeProtection,
  main
};
