require("dotenv").config()
const { execSync } = require("child_process")
const fs = require("fs")
const path = require("path")

// 条件加载安全模块
let obfuscator = null;
let codeSigning = null;
let asarEncryption = null;

try {
  obfuscator = require("./obfuscate.js");
  codeSigning = require("./code-signing.js");
  asarEncryption = require("./asar-encryption.js");
} catch (error) {
  console.warn("⚠️ 部分安全模块加载失败:", error.message);
}

try {
  // Verify environment variables
  const requiredEnvVars = [
    "APPLE_ID",
    "APPLE_APP_SPECIFIC_PASSWORD",
    "APPLE_TEAM_ID"
  ]

  const missing = requiredEnvVars.filter((key) => !process.env[key])

  if (missing.length > 0) {
    console.error("Missing required environment variables:", missing.join(", "))
    // process.exit(1)
  }

  // Add platform check
  if (process.platform !== "darwin") {
    console.warn("Warning: Building for macOS on a non-macOS platform may fail")
  }

  // Set optimization environment variables
  process.env.VITE_ENV = 'production';
  process.env.NODE_ENV = 'production';
  
  // Disable source maps for production builds to reduce file size
  process.env.GENERATE_SOURCEMAP = 'false';
  
  console.log("⚙️ Starting optimized build process...");
  
  // Clean previous builds
  console.log("🧹 Cleaning previous builds...");
  execSync("npm run clean", { stdio: "inherit" });
  
  // Create a temporary production-optimized package.json
  console.log("📦 Optimizing dependencies...");
  createOptimizedPackageJson();

  // Setup code signing configuration
  console.log("🔏 Setting up code signing...");
  try {
    if (codeSigning) {
      codeSigning.main();
      console.log("✅ Code signing configuration completed");
    } else {
      console.warn("⚠️ Code signing module not available");
    }
  } catch (error) {
    console.warn("⚠️ Code signing setup failed:", error.message);
    console.warn("Continuing with build process...");
  }
  
  // Compile TypeScript
  console.log("🔨 Compiling TypeScript...");
  execSync("cross-env NODE_ENV=production tsc", { stdio: "inherit" });
  
  // Build with Vite
  console.log("🏗️ Building with Vite...");
  execSync("vite build --emptyOutDir --mode production", { stdio: "inherit" });

  // Code Obfuscation - 使用优化后的配置
  console.log("🔒 Applying code obfuscation...");
  try {
    if (obfuscator && process.env.ENABLE_OBFUSCATION === 'true') {
      console.log("🔧 Using optimized obfuscation configuration...");
      obfuscator.main();
      console.log("✅ Code obfuscation completed successfully");
    } else if (obfuscator) {
      console.log("ℹ️ Code obfuscation is available but disabled");
      console.log("ℹ️ Set ENABLE_OBFUSCATION=true to enable obfuscation");
    } else {
      console.warn("⚠️ Code obfuscation module not available");
    }
  } catch (error) {
    console.error("❌ Code obfuscation failed:", error.message);
    console.warn("Continuing with build process...");
    // 混淆失败不应该阻止构建过程
  }

  // ASAR Encryption (will be handled by afterPack hook)
  console.log("🔐 ASAR encryption will be applied during packaging...");
  try {
    if (asarEncryption) {
      asarEncryption.createRuntimeProtection();
      console.log("✅ Runtime protection scripts created");
    } else {
      console.warn("⚠️ ASAR encryption module not available");
    }
  } catch (error) {
    console.warn("⚠️ Runtime protection setup failed:", error.message);
  }

  // Electron Builder
  console.log("📦 Packaging with electron-builder...");
  execSync(
    "electron-builder -mw",
    {
      stdio: "inherit",
      env: {
        ...process.env,
        CSC_IDENTITY_AUTO_DISCOVERY: "true",
        APPLE_ID: process.env.APPLE_ID,
        APPLE_APP_SPECIFIC_PASSWORD: process.env.APPLE_APP_SPECIFIC_PASSWORD,
        APPLE_TEAM_ID: process.env.APPLE_TEAM_ID,
        NOTARIZE: "true"
      }
    }
  )
  
  // Restore original package.json if we created a temp one
  restoreOriginalPackageJson();
  
  console.log("✅ Build completed successfully!");
} catch (error) {
  // Restore original package.json if we created a temp one
  restoreOriginalPackageJson();
  
  console.error("❌ Build failed:", error)
  process.exit(1)
}

/**
 * Creates a production-optimized package.json by moving unnecessary
 * dependencies to devDependencies for a smaller final package.
 */
function createOptimizedPackageJson() {
  const packageJsonPath = path.join(__dirname, "..", "package.json");
  const packageJsonBackupPath = path.join(__dirname, "..", "package.json.backup");
  
  // Skip if already backed up
  if (fs.existsSync(packageJsonBackupPath)) {
    console.log("Using existing package.json backup");
    return;
  }
  
  try {
    // Read current package.json
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf8"));
    
    // Create backup
    fs.writeFileSync(packageJsonBackupPath, JSON.stringify(packageJson, null, 2));
    
    // Dependencies that can be moved to devDependencies for build
    // These are dependencies that get bundled and don't need to be in the final app
    const bundledDeps = [
      "react",
      "react-dom",
      "react-copy-to-clipboard",
      "react-syntax-highlighter",
      "react-markdown",
      "react-query",
      "clsx",
      "class-variance-authority",
      "tailwind-merge",
      "@radix-ui/react-dialog",
      "@radix-ui/react-slot",
      "@radix-ui/react-toast",
      "katex",
      "markdown-it",
      "markdown-it-link-attributes",
      "markdown-it-texmath",

      "prismjs",
      "rehype-raw",
      "remark-gfm"
    ];
    
    // Move bundled dependencies to devDependencies
    if (packageJson.dependencies) {
      const newDevDependencies = { ...packageJson.devDependencies };
      
      bundledDeps.forEach(dep => {
        if (packageJson.dependencies[dep]) {
          newDevDependencies[dep] = packageJson.dependencies[dep];
          delete packageJson.dependencies[dep];
        }
      });
      
      packageJson.devDependencies = newDevDependencies;
    }
    
    // Write optimized package.json
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log("Created optimized package.json for build");
  } catch (err) {
    console.error("Failed to optimize package.json:", err);
  }
}

/**
 * Restores the original package.json from backup if it exists
 */
function restoreOriginalPackageJson() {
  const packageJsonPath = path.join(__dirname, "..", "package.json");
  const packageJsonBackupPath = path.join(__dirname, "..", "package.json.backup");
  
  if (fs.existsSync(packageJsonBackupPath)) {
    try {
      fs.copyFileSync(packageJsonBackupPath, packageJsonPath);
      fs.unlinkSync(packageJsonBackupPath);
      console.log("Restored original package.json");
    } catch (err) {
      console.error("Failed to restore original package.json:", err);
    }
  }
}
