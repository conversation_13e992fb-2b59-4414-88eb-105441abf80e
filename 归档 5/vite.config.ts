import react from "@vitejs/plugin-react"
import { resolve } from "path"
import { defineConfig } from "vite"
import electron from "vite-plugin-electron"

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    electron([
      {
        // Main process entry file
        entry: "electron/main.ts",
        onstart(options) {
          options.startup()
        },
        vite: {
          build: {
            rollupOptions: {
              external: ["sharp", "electron", "electron-is-dev"]
            }
          }
        }
      },
      {
        entry: "electron/preload.ts",
        onstart(options) {
          options.reload()
        }
      }
    ])
  ],
  server: {
    port: 5173
  },
  resolve: {
    alias: {
      "@": resolve(__dirname, "./src")
    }
  },
  build: {
    outDir: "dist",
    emptyOutDir: true,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
        passes: 2,
        ecma: 2020
      },
      mangle: {
        safari10: false
      },
      output: {
        comments: false,
        ecma: 2020
      }
    },
    rollupOptions: {
      external: ["sharp", "electron", "electron-is-dev"],
      input: {
        main: resolve(__dirname, "./index.html")
      },
      output: {
        manualChunks: (id) => {
          // Create separate chunks for large libraries
          if (id.includes('node_modules')) {
            // Group heavy libraries that might not be used immediately
            if (id.includes('highlight.js') || id.includes('prismjs')) {
              return 'highlight-vendor';
            }
            if (id.includes('katex')) {
              return 'katex-vendor';
            }
            if (id.includes('react') || id.includes('react-dom')) {
              return 'react-vendor';
            }
            if (id.includes('react-query')) {
              return 'query-vendor';
            }
            if (id.includes('react-syntax-highlighter')) {
              return 'syntax-vendor';
            }
            if (id.includes('axios')) {
              return 'axios-vendor';
            }
            if (id.includes('@radix-ui')) {
              return 'radix-vendor';
            }
            if (id.includes('markdown-it')) {
              return 'markdown-vendor';
            }
            // All other libraries go into a shared vendor chunk
            return 'vendor';
          }
        }
      }
    },
    sourcemap: false,
    chunkSizeWarningLimit: 1000,
    // Optimize CSS
    cssCodeSplit: true,
    // Enable tree-shaking
    target: 'es2020',
    assetsInlineLimit: 4096, // 4KB - inline small assets
    // Additional optimizations
    reportCompressedSize: false // Skip compressed size reporting for faster builds
  }
})
