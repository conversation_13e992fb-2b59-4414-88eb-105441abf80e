{"name": "secure-kernel", "version": "1.1.3", "main": "./dist-electron/main.js", "scripts": {"clean": "rimraf dist dist-electron", "dev": "cross-env NODE_ENV=development vite", "build": "node scripts/build.js", "build:secure": "cross-env ENABLE_OBFUSCATION=true node scripts/build.js", "build:test": "npm run build:secure && npm run test:obfuscated", "obfuscate": "node scripts/obfuscate.js", "test:security": "node scripts/test-security-modules.js", "test:obfuscation": "node scripts/test-obfuscation.js", "test:obfuscated": "node scripts/test-obfuscated-build.js", "security:summary": "node scripts/secure-build-summary.js", "security:signing": "node scripts/code-signing.js", "preview": "vite preview", "electron:dev": "tsc -p electron/tsconfig.json && electron .", "app:dev": "concurrently \"cross-env NODE_ENV=development vite\" \"wait-on http://localhost:5173 && cross-env electron .\"", "app:build": "npm run build", "app:build:secure": "npm run build:secure", "app:build:test": "npm run build:test", "watch": "tsc -p electron/tsconfig.json --watch", "analyze-deps": "node scripts/analyze-deps.js"}, "iohook": {"targets": ["electron-122"], "platforms": ["darwin"], "arches": ["x64", "arm64", "ia32"]}, "build": {"appId": "interview.coder.id", "productName": "SecureKernel", "files": ["dist/**/*", "dist-electron/**/*", "package.json", "!dist/**/*.map", "!dist-electron/**/*.map"], "directories": {"output": "release", "buildResources": "assets"}, "asar": true, "compression": "maximum", "removePackageScripts": true, "electronCompile": false, "electronLanguages": ["en", "zh-CN"], "asarUnpack": ["node_modules/@mhgbrown/iohook/**/*"], "afterPack": "./scripts/afterPack.js", "extraResources": [{"from": "assets/icons", "to": "icons", "filter": ["**/*.png", "**/*.icns", "**/*.ico"]}, {"from": "bin", "to": "bin", "filter": ["**/*"]}], "mac": {"category": "public.app-category.developer-tools", "target": [{"target": "dmg", "arch": ["arm64", "x64"]}], "icon": "assets/icons/mac/icon.icns", "hardenedRuntime": true, "gatekeeperAssess": false, "identity": null, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist", "artifactName": "${productName}-${version}-${arch}.${ext}", "darkModeSupport": true, "minimumSystemVersion": "10.15.0"}, "dmg": {"sign": false, "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}], "window": {"width": 540, "height": 380}, "internetEnabled": false}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "assets/icons/win/icon.png"}, "linux": {"target": ["AppImage"], "icon": "assets/icons/png/icon_256x256.png"}, "publish": {"provider": "github", "owner": "<PERSON><PERSON><PERSON>", "repo": "secure-kernel"}, "npmRebuild": false, "fileAssociations": [], "protocols": [], "extraMetadata": {"main": "./dist-electron/main.js"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}, "keywords": [], "author": "", "license": "ISC", "description": "An invisible desktop application to help you pass your technical interviews.", "devDependencies": {"@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.2", "@types/color": "^4.2.0", "@types/diff": "^6.0.0", "@types/electron": "^1.4.38", "@types/electron-store": "^1.3.1", "@types/katex": "^0.16.7", "@types/markdown-it": "^14.1.2", "@types/node": "^22.9.0", "@types/prismjs": "^1.26.5", "@types/react": "^18.3.12", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.3.1", "@types/react-syntax-highlighter": "^15.5.13", "@types/screenshot-desktop": "^1.12.3", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^8.14.0", "@typescript-eslint/parser": "^8.14.0", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.1.0", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "electron": "^29.1.4", "electron-builder": "^24.13.3", "electron-is-dev": "^3.0.1", "javascript-obfuscator": "^4.1.1", "katex": "^0.16.22", "markdown-it": "^14.1.0", "markdown-it-link-attributes": "^4.0.1", "markdown-it-texmath": "^1.0.0", "postcss": "^8.4.49", "prismjs": "^1.30.0", "react": "^18.3.1", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-query": "^3.39.3", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "rimraf": "^6.0.1", "tailwind-merge": "^2.5.5", "tailwindcss": "^3.4.15", "terser": "^5.39.0", "typescript": "^5.6.3", "vite": "^5.4.11", "vite-plugin-electron": "^0.28.8", "vite-plugin-electron-renderer": "^0.14.6", "wait-on": "^8.0.1"}, "dependencies": {"@mhgbrown/iohook": "^0.12.2", "@types/ws": "^8.18.1", "axios": "^1.7.7", "bufferutil": "^4.0.9", "electron-store": "^10.0.0", "electron-updater": "^6.3.9", "eventsource": "^4.0.0", "jsonrepair": "^3.12.0", "utf-8-validate": "^6.0.5", "uuid": "^11.0.3", "ws": "^8.18.2"}}