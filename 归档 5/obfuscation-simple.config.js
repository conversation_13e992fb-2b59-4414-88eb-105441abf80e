/**
 * 简化的JavaScript代码混淆配置
 * 避免验证错误，确保稳定运行
 */

const JavaScriptObfuscator = require('javascript-obfuscator');

// 工具函数：去重数组
function uniqueArray(arr) {
  return [...new Set(arr)];
}

// 基础混淆配置 - 稳定可靠，降低混淆强度
const baseConfig = {
  compact: true,
  controlFlowFlattening: false,
  deadCodeInjection: false,
  debugProtection: false,
  disableConsoleOutput: false, // 保留console输出用于调试
  identifierNamesGenerator: 'hexadecimal',
  renameGlobals: false,
  selfDefending: false,
  stringArray: true,
  stringArrayThreshold: 0.5, // 降低字符串数组阈值
  stringArrayEncoding: [], // 移除字符串编码
  unicodeEscapeSequence: false,
  target: 'node',

  // 扩展保留名称 - 包含更多关键API
  reservedNames: [
    // Node.js 核心
    'require',
    'module',
    'exports',
    '__dirname',
    '__filename',
    'process',
    'global',
    'Buffer',
    'console',

    // Electron 核心
    'electron',
    'app',
    'BrowserWindow',
    'ipcMain',
    'ipcRenderer',
    'contextBridge',
    'webContents',
    'shell',

    // 预加载脚本关键API
    'exposeInMainWorld',
    'invoke',
    'on',
    'off',
    'removeListener',
    'send',

    // DOM 相关（渲染进程）
    'window',
    'document',
    'navigator',
    'location',
    'addEventListener',
    'removeEventListener',

    // 应用特定API
    'electronAPI',
    'PROCESSING_EVENTS'
  ],

  // 扩展保留字符串
  reservedStrings: [
    'electron',
    'app',
    'ready',
    'window-all-closed',
    'activate',
    'electronAPI',
    'contextBridge',
    'ipcRenderer',
    'invoke',
    'on',
    'off'
  ]
};

// 主进程配置 - 保护窗口创建和显示逻辑
const mainProcessConfig = {
  ...baseConfig,
  target: 'node',
  compact: true, // 可以压缩，但要保护关键功能
  stringArray: true,
  stringArrayThreshold: 0.3, // 降低字符串数组使用率
  controlFlowFlattening: false, // 禁用控制流扁平化，避免窗口逻辑被破坏
  deadCodeInjection: false, // 禁用死代码注入
  selfDefending: false, // 禁用自我防护
  transformObjectKeys: false, // 禁用对象键转换，保护配置对象

  // 主进程特殊保留 - 保护窗口和应用逻辑（去重）
  reservedNames: uniqueArray([
    ...baseConfig.reservedNames,

    // Electron 主进程特有API
    'Menu', 'MenuItem', 'dialog', 'screen', 'nativeTheme', 'powerMonitor', 'powerSaveBlocker',
    'autoUpdater', 'crashReporter', 'nativeImage', 'clipboard', 'globalShortcut', 'systemPreferences', 'Notification',

    // 窗口管理相关 - 关键保护
    'createWindow', 'showMainWindow', 'hideMainWindow', 'toggleMainWindow', 'getMainWindow', 'isVisible',
    'WindowHelper', 'AppState', 'getInstance', 'setBounds', 'setIgnoreMouseEvents', 'setVisibleOnAllWorkspaces',
    'setAlwaysOnTop', 'setContentProtection', 'setOpacity', 'showInactive', 'focus', 'loadURL', 'isDestroyed', 'getFocusedWindow',

    // 应用状态管理
    'getView', 'setView', 'getProblemInfo', 'setProblemInfo', 'getScreenshotHelper', 'getScreenshotQueue', 'getExtraScreenshotQueue',

    // 事件处理器
    'initializeApp', 'initializeIpcHandlers', 'checkAndInitializeConfig', 'registerGlobalShortcuts',

    // 类名和构造函数
    'ScreenshotHelper', 'ProcessingHelper', 'ShortcutsHelper', 'MouseTrackingHelper', 'SystemAudioManager', 'SystemASRManager',

    // 配置和存储
    'store', 'get', 'set', 'has', 'delete', 'clear',

    // 应用生命周期
    'whenReady', 'quit', 'activate', 'beforeQuit', 'windowAllClosed'
  ]),

  // 主进程保留字符串（去重）
  reservedStrings: uniqueArray([
    ...baseConfig.reservedStrings,

    // 主进程特有事件
    'before-quit', 'will-quit', 'closed', 'focus', 'blur', 'show', 'hide', 'minimize', 'maximize', 'restore',
    'resize', 'move', 'moved', 'enter-full-screen', 'leave-full-screen',

    // 配置键
    'openaiApiKey', 'currentModel', 'windowBounds', 'windowPosition', 'windowSize',

    // 文件路径
    'preload.js', 'main.js', 'index.html',

    // 应用标识
    'SecureKernel', 'interview.coder.id'
  ])
};

// 渲染进程配置 - 保护React组件和DOM操作
const rendererProcessConfig = {
  ...baseConfig,
  target: 'browser',
  stringArrayThreshold: 0.2, // 进一步降低字符串数组使用率
  compact: true, // 可以压缩
  controlFlowFlattening: false, // 禁用控制流扁平化，保护组件逻辑
  deadCodeInjection: false, // 禁用死代码注入
  selfDefending: false, // 禁用自我防护
  transformObjectKeys: false, // 禁用对象键转换，保护props和state

  reservedNames: uniqueArray([
    ...baseConfig.reservedNames,

    // 浏览器环境API - 界面渲染必需
    'window', 'document', 'navigator', 'location', 'history', 'localStorage', 'sessionStorage', 'indexedDB',
    'fetch', 'XMLHttpRequest', 'WebSocket', 'Worker', 'ServiceWorker', 'URL', 'URLSearchParams', 'FormData',
    'Blob', 'File', 'FileReader',

    // DOM 操作 - 关键保护
    'getElementById', 'getElementsByClassName', 'getElementsByTagName', 'querySelector', 'querySelectorAll',
    'createElement', 'createTextNode', 'appendChild', 'removeChild', 'insertBefore', 'replaceChild', 'cloneNode',
    'innerHTML', 'outerHTML', 'textContent', 'innerText', 'className', 'classList', 'style', 'setAttribute',
    'getAttribute', 'removeAttribute', 'hasAttribute', 'dataset',

    // 事件处理 - 必须保护
    'addEventListener', 'removeEventListener', 'dispatchEvent', 'preventDefault', 'stopPropagation',
    'stopImmediatePropagation', 'Event', 'CustomEvent', 'MouseEvent', 'KeyboardEvent', 'TouchEvent',
    'FocusEvent', 'InputEvent',

    // React 核心 - 组件渲染必需
    'React', 'ReactDOM', 'createRoot', 'render', 'unmountComponentAtNode', 'Component', 'PureComponent',
    'Fragment', 'StrictMode', 'Suspense', 'createElement', 'cloneElement', 'isValidElement',

    // React Hooks - 状态管理必需
    'useState', 'useEffect', 'useLayoutEffect', 'useCallback', 'useMemo', 'useRef', 'useContext',
    'useReducer', 'useImperativeHandle', 'useDebugValue',

    // React Context
    'createContext', 'Provider', 'Consumer',

    // React Query - 数据管理
    'QueryClient', 'QueryClientProvider', 'useQuery', 'useMutation', 'useQueryClient', 'invalidateQueries',
    'setQueryData', 'removeQueries',

    // 应用特定组件和函数
    'App', 'Queue', 'Solutions', 'Voice', 'ApiKeyAuth', 'Toast', 'ToastProvider', 'ToastContext', 'useToast', 'showToast',

    // 状态管理
    'setView', 'setIsAuthenticated', 'setToastOpen', 'setToastMessage', 'setIsVoiceAssistantVisible',

    // 常用的全局函数
    'setTimeout', 'setInterval', 'clearTimeout', 'clearInterval', 'requestAnimationFrame', 'cancelAnimationFrame',
    'Promise', 'async', 'await', 'then', 'catch', 'finally',

    // 观察者API
    'ResizeObserver', 'MutationObserver', 'IntersectionObserver', 'observe', 'unobserve', 'disconnect',

    // CSS 和样式
    'getComputedStyle', 'matchMedia', 'CSSStyleDeclaration'
  ]),

  // 渲染进程保留字符串（去重）
  reservedStrings: uniqueArray([
    ...baseConfig.reservedStrings,

    // HTML 元素和属性
    'div', 'span', 'button', 'input', 'textarea', 'select', 'option', 'form', 'label', 'img', 'svg', 'path',
    'className', 'id', 'data-', 'aria-', 'role', 'tabindex', 'disabled', 'checked', 'selected', 'value',
    'placeholder', 'type', 'src', 'alt', 'href', 'target',

    // CSS 类名和样式
    'min-h-0', 'container', 'content', 'root',

    // 事件类型
    'click', 'change', 'input', 'focus', 'blur', 'keydown', 'keyup', 'keypress', 'mousedown', 'mouseup',
    'mousemove', 'mouseenter', 'mouseleave', 'touchstart', 'touchend', 'touchmove', 'scroll', 'resize',
    'load', 'unload', 'beforeunload', 'DOMContentLoaded',

    // 应用状态
    'queue', 'solutions', 'voice', 'debug', 'authenticated', 'loading', 'error', 'success',

    // 查询键
    'screenshots', 'problem_statement', 'solution', 'new_solution'
  ])
};

// 预加载脚本配置 - 最保守的设置，确保界面正常显示
const preloadConfig = {
  ...baseConfig,
  target: 'node',
  compact: false, // 不压缩，保持可读性
  stringArray: false, // 完全禁用字符串数组，避免API调用被破坏
  stringArrayThreshold: 0, // 完全禁用字符串数组
  identifierNamesGenerator: 'mangled', // 使用更简单的标识符生成
  controlFlowFlattening: false, // 禁用控制流扁平化
  deadCodeInjection: false, // 禁用死代码注入
  selfDefending: false, // 禁用自我防护
  debugProtection: false, // 禁用调试保护
  disableConsoleOutput: false, // 保留console输出
  transformObjectKeys: false, // 禁用对象键转换，保护API对象

  // 预加载脚本特殊保留 - 扩展保护关键API和事件
  reservedNames: [
    // Node.js 核心
    'require',
    'module',
    'exports',
    '__dirname',
    '__filename',
    'process',
    'global',
    'Buffer',
    'console',

    // Electron 核心API - 关键保护
    'electron',
    'app',
    'BrowserWindow',
    'ipcMain',
    'ipcRenderer',
    'contextBridge',
    'webContents',
    'shell',

    // 预加载脚本关键API - 必须保护
    'exposeInMainWorld',
    'invoke',
    'on',
    'off',
    'removeListener',
    'send',
    'electronAPI',
    'PROCESSING_EVENTS',

    // 预加载脚本特有的API
    'webFrame',
    'remote',
    'nodeIntegration',
    'contextIsolation',
    'enableRemoteModule',
    'webSecurity',

    // DOM 相关 - 界面渲染必需
    'window',
    'document',
    'navigator',
    'location',
    'addEventListener',
    'removeEventListener',
    'dispatchEvent',
    'CustomEvent',
    'getElementById',
    'querySelector',
    'querySelectorAll',
    'createElement',
    'appendChild',
    'removeChild',
    'innerHTML',
    'textContent',
    'className',
    'classList',
    'style',
    'setAttribute',
    'getAttribute',
    'removeAttribute',

    // React 相关 - 防止组件渲染被破坏
    'React',
    'ReactDOM',
    'createRoot',
    'render',
    'useState',
    'useEffect',
    'useCallback',
    'useMemo',
    'useRef',
    'useContext',
    'createContext',

    // 应用特定的事件名称 - 必须保护
    'screenshot-taken',
    'solutions-ready',
    'reset-view',
    'config-updated',
    'model-changed',
    'window-moved',
    'toggle-voice-assistant',
    'navigate-to-voice-view',
    'start-microphone-recognition',
    'restore-focus',
    'unauthorized',
    'api-key-out-of-credits',
    'processing-no-screenshots',
    'first-point-recorded',
    'solution-start',
    'solution-error',
    'solution-success',
    'problem-extracted',
    'debug-success',
    'debug-error',
    'debug-start',
    'copy-solution-content',
    'config-init-failed',

    // IPC 通道名称 - 通信必需
    'update-content-dimensions',
    'get-api-key',
    'get-current-model',
    'take-screenshot',
    'take-full-screenshot',
    'generate-solution',
    'get-screenshots',
    'delete-screenshot',
    'update-api-key',
    'set-api-key',
    'init-config',
    'should-show-microphone-button'
  ],

  // 扩展保留字符串 - 保护所有关键字符串
  reservedStrings: [
    // Electron 相关
    'electron',
    'app',
    'ready',
    'window-all-closed',
    'activate',
    'electronAPI',
    'contextBridge',
    'ipcRenderer',
    'invoke',
    'on',
    'off',
    'exposeInMainWorld',

    // 事件名称
    'screenshot-taken',
    'solutions-ready',
    'reset-view',
    'config-updated',
    'model-changed',
    'window-moved',
    'toggle-voice-assistant',
    'navigate-to-voice-view',
    'unauthorized',
    'solution-start',
    'solution-error',
    'solution-success',
    'problem-extracted',

    // IPC 通道
    'update-content-dimensions',
    'get-api-key',
    'get-current-model',
    'take-screenshot',
    'generate-solution',
    'get-screenshots',
    'delete-screenshot',
    'set-api-key',
    'init-config',

    // DOM 相关
    'root',
    'getElementById',
    'querySelector',
    'addEventListener',
    'DOMContentLoaded',

    // React 相关
    'React',
    'ReactDOM',
    'createRoot',
    'render'
  ]
};

module.exports = {
  base: baseConfig,
  mainProcess: mainProcessConfig,
  rendererProcess: rendererProcessConfig,
  preload: preloadConfig,
  
  // 混淆函数
  obfuscate: (code, config = baseConfig) => {
    return JavaScriptObfuscator.obfuscate(code, config).getObfuscatedCode();
  },
  
  // 混淆文件
  obfuscateFile: (inputPath, outputPath, config = baseConfig) => {
    const fs = require('fs');
    const code = fs.readFileSync(inputPath, 'utf8');
    const obfuscatedCode = JavaScriptObfuscator.obfuscate(code, config).getObfuscatedCode();
    fs.writeFileSync(outputPath, obfuscatedCode);
    return obfuscatedCode;
  }
};
