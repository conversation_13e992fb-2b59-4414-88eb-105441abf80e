/**
 * SystemASRManager.ts
 * Dedicated ASR Manager for System Audio
 * Handles WebSocket connections specifically for system audio processing
 */

import { BrowserWindow } from 'electron';
import { getVoiceConfig, isVoiceEnabled } from '../store';
import { generateUUID } from '../utils';
import { unifiedHistoryManager, HistoryItem } from './UnifiedHistoryManager';

// Import WebSocket with error handling
let WebSocket: any;
try {
  WebSocket = require('ws');
  console.log('WebSocket module loaded successfully for SystemASR');
} catch (error) {
  console.error('Failed to load ws module for SystemASR:', error);
  WebSocket = class MockWebSocket {
    constructor() {
      throw new Error('WebSocket module is not available');
    }
  };
}

// Constants for WebSocket protocol
const PROTOCOL_VERSION = 0x01;
const DEFAULT_HEADER_SIZE = 0x01;
const FULL_CLIENT_REQUEST = 0x01;
const AUDIO_ONLY_REQUEST = 0x02;
const FULL_SERVER_RESPONSE = 0x09;
const SERVER_ACK = 0x0B;
const SERVER_ERROR_RESPONSE = 0x0F;
const POS_SEQUENCE = 0x01;
const JSON_FORMAT = 0x01;
const GZIP = 0x01;

export class SystemASRManager {
  private wsConnection: any = null;
  private isConnected: boolean = false;
  private isProcessing: boolean = false;
  private mainWindow: BrowserWindow | null = null;
  private static instance: SystemASRManager | null = null;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private messageQueue: Array<any> = [];
  private sessionStarted: boolean = false;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private lastActivityTimestamp: number = 0;
  private sequence: number = 1;
  private readyToSendAudio: boolean = false;
  private isConnecting: boolean = false;
  private currentConnectionId: string = '';
  private isScheduledForClosure: boolean = false;
  private lastConnectionTime: number = 0;
  private connectionAttemptBlocked: boolean = false;
  private connectionBlockTimeout: NodeJS.Timeout | null = null;
  private readonly MIN_CONNECTION_INTERVAL = 30000; // 30秒
  private autoCloseTimer: NodeJS.Timeout | null = null;
  private lastPongTime: number = 0;
  private pingInterval: NodeJS.Timeout | null = null;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private lastAudioDataTime: number = 0;
  private audioTimeoutCheckTimer: NodeJS.Timeout | null = null;
  private readonly AUDIO_TIMEOUT_MS = 8000; // 8秒无音频数据则自动关闭

  // 新增优化相关属性
  private lastLogTime: number = 0;
  private audioBuffer: Buffer[] = [];
  private audioBufferSize: number = 0;
  private readonly MAX_BUFFER_SIZE = 32000; // 最大缓冲区大小 (约1秒的16kHz 16位音频)
  private readonly MIN_BUFFER_SIZE = 6400;  // 最小缓冲区大小 (约0.2秒的16kHz 16位音频)

  private static lastSendTime: number = 0;

  private config: any = null;

  private static lastTranscriptions: {
    finalTexts: Set<string>;
    lastInterimText: string;
    lastFinalText: string;
    lastTimestamp: number;
    lastHistoryUpdateTime: number;
    history: Array<{
      text: string;
      source: 'system';
      timestamp: string;
      isFinal: boolean;
      paragraphId?: string;
    }>;
  } | null = null;

  constructor() {
    console.log('SystemASRManager instance created');
    this.startAudioTimeoutCheck();
    this.startPerformanceMonitoring();

    if (!SystemASRManager.lastTranscriptions) {
      SystemASRManager.lastTranscriptions = {
        finalTexts: new Set<string>(),
        lastInterimText: '',
        lastFinalText: '',
        lastTimestamp: 0,
        lastHistoryUpdateTime: 0,
        history: []
      };
    }
  }

  /**
   * 设置主窗口引用
   */
  public setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
    // 设置统一历史管理器的主窗口
    unifiedHistoryManager.setMainWindow(window);
  }

  /**
   * 从 store 中加载语音配置
   */
  private loadConfig(): void {
    try {
      this.config = getVoiceConfig();
      console.log('SystemASRManager: 语音配置加载成功', this.config);
    } catch (error) {
      console.error('SystemASRManager: 加载语音配置失败', error);
      this.config = null;
    }
  }

  /**
   * 检查系统音频识别功能是否启用
   */
  public isSystemASREnabled(): boolean {
    const enabled = isVoiceEnabled();
    console.log('SystemASRManager: 系统音频识别启用状态:', enabled);
    return enabled;
  }

  /**
   * 获取语音配置中的 AppId
   */
  public getVoiceAppId(): string {
    return this.config?.voiceAppId || '';
  }

  /**
   * 获取语音配置中的 AccessKeyId
   */
  public getVoiceAccessKeyId(): string {
    return this.config?.voiceAccessKeyId || '';
  }

  public static getInstance(): SystemASRManager {
    if (!SystemASRManager.instance) {
      SystemASRManager.instance = new SystemASRManager();
    }
    return SystemASRManager.instance;
  }



  public async startASRSession(): Promise<{ success: boolean, error?: string }> {
    console.log('Starting SystemASR session for system audio');

    if (!this.config) {
      this.loadConfig();
    }

    // 检查语音功能是否启用
    if (!this.isSystemASREnabled()) {
      console.log('SystemASR: 语音功能已禁用');
      return { success: false, error: '语音功能已禁用，请检查配置' };
    }

    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
      console.log('SystemASR: 自动关闭计时器已取消');
    }

    if (this.isConnecting) {
      console.log('SystemASR: Already attempting to connect');
      return { success: false, error: 'Connection already in progress' };
    }

    if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN && this.isConnected) {
      console.log('SystemASR: Active WebSocket connection already exists');
      return { success: true };
    }

    const now = Date.now();
    if (now - this.lastConnectionTime < this.MIN_CONNECTION_INTERVAL && this.connectionAttemptBlocked) {
      const remainingTime = Math.ceil((this.MIN_CONNECTION_INTERVAL - (now - this.lastConnectionTime)) / 1000);
      return { success: false, error: `Please wait ${remainingTime}s before reconnecting` };
    }

    if (this.wsConnection) {
      console.log('SystemASR: Closing existing connection');
      try {
        this.isConnected = false;
        this.sessionStarted = false;
        
        if (this.wsConnection.readyState === WebSocket.OPEN) {
          this.wsConnection.close(1000, 'Closing before new connection');
        } else {
          this.wsConnection.terminate();
        }
      } catch (err) {
        console.error('SystemASR: Error closing existing connection:', err);
      }
      
      this.wsConnection = null;
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    this.lastConnectionTime = now;
    this.messageQueue = [];
    this.reconnectAttempts = 0;
    this.sessionStarted = false;
    this.lastActivityTimestamp = Date.now();
    this.lastPongTime = Date.now();
    this.currentConnectionId = generateUUID();
    
    this.clearIntervals();
    
    try {
      await this.connectToASRService();
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown connection error' 
      };
    }
  }

  private clearIntervals(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  private async connectToASRService(): Promise<void> {
    // 检查熔断器状态
    if (!this.checkCircuitBreaker()) {
      throw new Error('熔断器开启，拒绝连接请求');
    }

    this.isConnecting = true;
    const connectionId = this.currentConnectionId;

    return new Promise((resolve, reject) => {
      try {
        console.log(`SystemASR: Starting connection attempt (ID: ${connectionId})`);

        // 记录连接尝试
        this.performanceMetrics.connectionAttempts++;
        const connectionStartTime = Date.now();

        const connectId = generateUUID();
        const targetUrl = 'wss://openspeech.bytedance.com/api/v3/sauc/bigmodel';
        
        const options = {
          headers: {
            'X-Api-App-Key': this.getVoiceAppId(),
            'X-Api-Access-Key': this.getVoiceAccessKeyId(),
            'X-Api-Resource-Id': 'volc.bigasr.sauc.duration',
            'X-Api-Connect-Id': connectId
          },
          rejectUnauthorized: false,
          protocolVersion: 13
        };
        
        this.wsConnection = new WebSocket(targetUrl, options);
        this.wsConnection.binaryType = 'arraybuffer';
        
        const connectionTimeout = setTimeout(() => {
          if (connectionId !== this.currentConnectionId) return;
          
          if (!this.isConnected) {
            console.error('SystemASR: Connection timeout');
            if (this.wsConnection) {
              this.wsConnection.terminate();
              this.wsConnection = null;
            }
            
            if (this.reconnectAttempts < this.maxReconnectAttempts) {
              this.reconnectAttempts++;
              console.log(`SystemASR: Reconnection attempt ${this.reconnectAttempts} of ${this.maxReconnectAttempts}`);
              setTimeout(() => {
                if (connectionId === this.currentConnectionId) {
                  this.connectToASRService().then(resolve).catch(reject);
                }
              }, 3000);
            } else {
              this.isConnecting = false;
              this.recordConnectionFailure(); // 记录连接失败
              this.performanceMetrics.failedConnections++; // 记录失败连接
              reject(new Error('Connection timeout after multiple attempts'));
            }
          }
        }, 15000);
        
        this.wsConnection.on('open', () => {
          if (connectionId !== this.currentConnectionId) {
            clearTimeout(connectionTimeout);
            try {
              this.wsConnection.close(1000, "Superseded by newer connection");
            } catch (e) {
              console.error("SystemASR: Error closing superseded connection:", e);
            }
            return;
          }
          
          clearTimeout(connectionTimeout);
          console.log('SystemASR: WebSocket connection established');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.lastActivityTimestamp = Date.now();
          this.isConnecting = false;

          // 记录连接成功，重置熔断器
          this.recordConnectionSuccess();

          // 记录性能指标
          this.performanceMetrics.successfulConnections++;
          this.performanceMetrics.lastConnectionTime = Date.now();
          const connectionTime = Date.now() - connectionStartTime;
          this.performanceMetrics.totalReconnectionTime += connectionTime;
          
          this.sendInitMessage();
          this.processQueuedMessages();
          this.startHeartbeat();
          
          if (this.mainWindow && !this.mainWindow.isDestroyed()) {
            this.mainWindow.webContents.send('system-asr:connection-status', { connected: true });
          }
          
          resolve();
        });
        
        this.wsConnection.on('message', (message: any) => {
          if (connectionId !== this.currentConnectionId) return;
          this.handleWebSocketMessage(message);
        });
        
        this.wsConnection.on('error', (error: any) => {
          if (connectionId !== this.currentConnectionId) {
            clearTimeout(connectionTimeout);
            return;
          }
          
          clearTimeout(connectionTimeout);
          console.error('SystemASR: WebSocket error:', error);
          
          this.isConnected = false;
          this.clearIntervals();
          
          if (this.mainWindow && !this.mainWindow.isDestroyed()) {
            this.mainWindow.webContents.send('system-asr:error', {
              error: true,
              message: `SystemASR service error: ${error.message || 'Unknown error'}`,
              code: error.code || 1011
            });
          }
          
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            setTimeout(() => {
              if (connectionId === this.currentConnectionId) {
                this.connectToASRService().then(resolve).catch(reject);
              }
            }, 5000);
          } else {
            this.isConnecting = false;
            this.recordConnectionFailure(); // 记录连接失败
            reject(error);
          }
        });
        
        this.wsConnection.on('close', (code: number, reason: string) => {
          if (connectionId !== this.currentConnectionId) {
            clearTimeout(connectionTimeout);
            return;
          }
          
          clearTimeout(connectionTimeout);
          console.log(`SystemASR: WebSocket closed with code ${code}: ${reason || 'No reason provided'}`);
          
          this.isConnected = false;
          this.sessionStarted = false;
          this.sequence = 1; // 重置序列号
          this.clearIntervals();
          
          if (this.mainWindow && !this.mainWindow.isDestroyed()) {
            this.mainWindow.webContents.send('system-asr:connection-status', { 
              connected: false,
              code,
              reason
            });
          }
          
          if (code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            setTimeout(() => {
              if (connectionId === this.currentConnectionId) {
                this.connectToASRService().then(resolve).catch(reject);
              }
            }, 5000);
          } else {
            this.isConnecting = false;
          }
        });
      } catch (error) {
        console.error('SystemASR: Failed to create WebSocket connection:', error);
        this.isConnecting = false;
        this.recordConnectionFailure(); // 记录连接失败
        reject(error);
      }
    });
  }

  private async sendInitMessage(): Promise<void> {
    if (!this.wsConnection || this.wsConnection.readyState !== WebSocket.OPEN) {
      console.error('SystemASR: Cannot send init message: WebSocket not open');
      return;
    }

    try {
      const user = { uid: "system-audio" };
      const audio = {
        format: "pcm",
        sample_rate: 16000,
        bits: 16,
        channel: 1,
        codec: "raw"
      };
      
      const request = {
        model_name: "bigmodel",
        enable_punc: true,
        mode: "2pass",
        enable_partial_result: true,
        enable_itn: true,
        latency: 2,  // 增加延迟以提高准确率
        refresh_ms: 200,  // 减少刷新间隔，提高实时性
        enable_vad: true,  // 启用语音活动检测
        vad_eos_timeout: 1000,  // VAD结束超时时间
        enable_words: true,  // 启用词级别时间戳
        enable_speaker_diarization: false,  // 关闭说话人分离以提高性能
        enable_emotion: false,  // 关闭情感识别以提高性能
        enable_language_detection: false,  // 关闭语言检测以提高性能
        language: "zh-CN",  // 明确指定中文
        domain: "general",  // 通用领域
        enable_inverse_text_normalization: true,  // 启用逆文本标准化
        enable_punctuation: false,  // 启用标点符号
        enable_capitalization: true,  // 启用大小写
        max_alternatives: 1,  // 只返回最佳结果
        enable_confidence: true,  // 启用置信度
        enable_word_confidence: false,  // 关闭词级置信度以提高性能
        enable_nbest: false,  // 关闭N-best结果
        enable_hotwords: false,  // 关闭热词
        enable_profanity_filter: false,  // 关闭敏感词过滤
        enable_disfluency_filter: true,  // 启用语音不流畅过滤
        enable_filler_words_filter: true,  // 启用填充词过滤
        audio_enhancement: {
          enable_noise_reduction: true,  // 启用降噪
          enable_echo_cancellation: true,  // 启用回声消除
          enable_automatic_gain_control: true  // 启用自动增益控制
        }
      };
      
      const payload = { user, audio, request };
      
      console.log('SystemASR: 发送初始化消息');
      
      const payloadStr = JSON.stringify(payload);
      const compressedPayload = await this.compressData(payloadStr);
      const compressedData = new Uint8Array(compressedPayload);
      
      const header = this.createHeader(FULL_CLIENT_REQUEST, POS_SEQUENCE, JSON_FORMAT, GZIP, 0);
      const payloadSize = this.intToBytes(compressedData.length);
      const sequenceBytes = this.intToBytes(this.sequence);
      
      const totalSize = header.length + sequenceBytes.length + payloadSize.length + compressedData.length;
      const message = new Uint8Array(totalSize);
      
      let offset = 0;
      message.set(header, offset);
      offset += header.length;
      message.set(sequenceBytes, offset);
      offset += sequenceBytes.length;
      message.set(payloadSize, offset);
      offset += payloadSize.length;
      message.set(compressedData, offset);
      
      this.wsConnection.send(message);
      this.readyToSendAudio = false;
      this.lastActivityTimestamp = Date.now();
    } catch (error) {
      console.error('SystemASR: 发送初始化消息时出错:', error);
    }
  }

  private processQueuedMessages(): void {
    if (this.messageQueue.length > 0) {
      console.log(`SystemASR: Processing ${this.messageQueue.length} queued audio messages`);
      const queuedMessages = [...this.messageQueue]; // 创建副本
      this.messageQueue = []; // 清空队列

      // 逐个处理队列中的音频数据
      queuedMessages.forEach(audioData => {
        if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN && this.readyToSendAudio) {
          this.processAudioData(audioData);
        }
      });
    }
  }

  private startHeartbeat(): void {
    this.clearIntervals();
    
    this.pingInterval = setInterval(() => {
      if (this.isScheduledForClosure) {
        this.clearIntervals();
        return;
      }
      
      if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
        try {
          this.wsConnection.ping();
          
          const now = Date.now();
          if (now - this.lastPongTime > 30000) {
            console.warn('SystemASR: No pong response for 30 seconds');
            this.handleConnectionFailure();
          }
        } catch (error) {
          console.error('SystemASR: Error sending ping:', error);
          this.handleConnectionFailure();
        }
      }
    }, 15000);
    
    if (this.wsConnection) {
      this.wsConnection.on('pong', () => {
        this.lastPongTime = Date.now();
      });
    }
    
    this.heartbeatInterval = setInterval(() => {
      if (this.isScheduledForClosure) {
        this.clearIntervals();
        return;
      }
      
      if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
        const currentTime = Date.now();
        if (currentTime - this.lastActivityTimestamp > 15000) {
          console.log('SystemASR: Sending heartbeat');
          
          try {
            this.wsConnection.send(JSON.stringify({ type: 'heartbeat' }));
            this.lastActivityTimestamp = currentTime;
          } catch (error) {
            console.error('SystemASR: Error sending heartbeat:', error);
            this.handleConnectionFailure();
          }
        }
      } else {
        this.clearIntervals();
      }
    }, 10000);
  }

  public async processAudioData(audioData: any): Promise<void> {
    this.lastAudioDataTime = Date.now();

    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
    }

    this.lastActivityTimestamp = Date.now();

    const audioDataLength = audioData.audio_data ? audioData.audio_data.length : 0;
    const sampleRate = audioData.sample_rate || 16000;

    // 记录音频数据处理性能指标
    this.performanceMetrics.totalAudioPacketsSent++;
    this.performanceMetrics.totalAudioDataSent += audioDataLength;

    // 优化日志输出 - 减少频繁日志，提高性能
    const now = Date.now();
    if (!this.lastLogTime || now - this.lastLogTime > 5000) { // 每5秒输出一次详细日志
      console.log(`SystemASR: 处理音频数据，长度=${audioDataLength}字节, 采样率=${sampleRate}Hz`);
      this.lastLogTime = now;
    }

    if (!this.wsConnection) {
      console.error('SystemASR: WebSocket连接未初始化');
      throw new Error('SystemASR WebSocket connection not initialized');
    }

    if (!this.sessionStarted && this.isConnected) {
      console.log(`SystemASR: 会话尚未开始，将音频数据加入队列 (队列长度: ${this.messageQueue.length})`);
      // 限制队列大小，避免内存溢出
      if (this.messageQueue.length < 50) {
        this.messageQueue.push(audioData);
      } else {
        console.warn('SystemASR: 音频数据队列已满，丢弃旧数据');
        this.messageQueue.shift(); // 移除最旧的数据
        this.messageQueue.push(audioData);
      }
      return;
    }

    // 优化发送间隔 - 根据数据大小动态调整
    const MIN_SEND_INTERVAL = this.calculateOptimalSendInterval(audioDataLength);

    if (!SystemASRManager.lastSendTime) {
      SystemASRManager.lastSendTime = 0;
    }

    if (now - SystemASRManager.lastSendTime < MIN_SEND_INTERVAL) {
      // 不再跳过，而是缓存数据进行批量处理
      this.bufferAudioData(audioData);
      return;
    }

    // 检查是否有缓冲的数据需要先发送
    if (this.audioBufferSize > 0) {
      this.flushAudioBuffer(sampleRate);
    }

    if (this.wsConnection.readyState === WebSocket.OPEN && this.readyToSendAudio) {
      try {
        SystemASRManager.lastSendTime = now;

        const audioDataBytes = new Uint8Array(audioData.audio_data);

        if (!audioDataBytes || audioDataBytes.length === 0) {
          console.warn(`SystemASR: 收到空的音频数据，跳过处理`);
          return;
        }

        // 使用新的核心发送方法
        await this.sendAudioDataToASR(audioDataBytes, sampleRate);

      } catch (error) {
        console.error(`SystemASR: 发送音频数据出错:`, error);
        throw error;
      }
    } else if (this.wsConnection.readyState === WebSocket.OPEN && !this.readyToSendAudio) {
      console.warn('SystemASR: WebSocket已连接但尚未准备好发送音频，加入队列');
      // 限制队列大小
      if (this.messageQueue.length < 50) {
        this.messageQueue.push(audioData);
      } else {
        console.warn('SystemASR: 音频数据队列已满，丢弃旧数据');
        this.messageQueue.shift();
        this.messageQueue.push(audioData);
      }
    } else {
      console.warn(`SystemASR: WebSocket未连接，音频数据加入队列`);
      // 限制队列大小
      if (this.messageQueue.length < 50) {
        this.messageQueue.push(audioData);
      } else {
        console.warn('SystemASR: 音频数据队列已满，丢弃旧数据');
        this.messageQueue.shift();
        this.messageQueue.push(audioData);
      }
    }
  }

  // Helper methods
  private convertSampleRate(audioData: Uint8Array, sourceSampleRate: number, targetSampleRate: number): Uint8Array {
    if (sourceSampleRate === targetSampleRate) {
      return audioData;
    }
    
    const dataLength = audioData.length % 2 === 0 ? audioData.length : audioData.length - 1;
    const samples = new Int16Array(dataLength / 2);
    
    for (let i = 0; i < samples.length; i++) {
      samples[i] = (audioData[i * 2] & 0xff) | ((audioData[i * 2 + 1] & 0xff) << 8);
    }
    
    let newSampleCount: number;
    
    if (sourceSampleRate > targetSampleRate) {
      const ratio = sourceSampleRate / targetSampleRate;
      newSampleCount = Math.floor(samples.length / ratio);
    } else {
      const ratio = targetSampleRate / sourceSampleRate;
      newSampleCount = Math.floor(samples.length * ratio);
    }
    
    const newSamples = new Int16Array(newSampleCount);
    
    if (sourceSampleRate > targetSampleRate) {
      const ratio = sourceSampleRate / targetSampleRate;
      for (let i = 0; i < newSampleCount; i++) {
        const srcIndex = i * ratio;
        const srcIndexInt = Math.floor(srcIndex);
        const srcIndexFrac = srcIndex - srcIndexInt;
        
        if (srcIndexInt + 1 < samples.length) {
          newSamples[i] = Math.round(samples[srcIndexInt] * (1 - srcIndexFrac) + samples[srcIndexInt + 1] * srcIndexFrac);
        } else {
          newSamples[i] = samples[srcIndexInt];
        }
      }
    } else {
      const ratio = targetSampleRate / sourceSampleRate;
      for (let i = 0; i < newSampleCount; i++) {
        const srcIndex = i / ratio;
        const srcIndexInt = Math.floor(srcIndex);
        const srcIndexFrac = srcIndex - srcIndexInt;
        
        if (srcIndexInt + 1 < samples.length) {
          newSamples[i] = Math.round(samples[srcIndexInt] * (1 - srcIndexFrac) + samples[srcIndexInt + 1] * srcIndexFrac);
        } else {
          newSamples[i] = samples[srcIndexInt < samples.length ? srcIndexInt : samples.length - 1];
        }
      }
    }
    
    const newAudioData = new Uint8Array(newSamples.length * 2);
    for (let i = 0; i < newSamples.length; i++) {
      newAudioData[i * 2] = newSamples[i] & 0xff;
      newAudioData[i * 2 + 1] = (newSamples[i] >> 8) & 0xff;
    }
    
    return newAudioData;
  }

  private async compressData(data: string | Uint8Array | ArrayBuffer): Promise<ArrayBufferLike> {
    try {
      const zlib = require('zlib');
      const { promisify } = require('util');
      const gzipAsync = promisify(zlib.gzip);
      
      let inputData: Buffer;
      
      if (typeof data === 'string') {
        inputData = Buffer.from(data);
      } else if (data instanceof Uint8Array) {
        inputData = Buffer.from(data);
      } else if (data instanceof ArrayBuffer) {
        inputData = Buffer.from(data);
      } else {
        throw new Error('Unsupported data type for compression');
      }
      
      try {
        const compressed = await gzipAsync(inputData, { level: zlib.constants.Z_BEST_SPEED });
        return compressed.buffer.slice(compressed.byteOffset, compressed.byteOffset + compressed.byteLength);
      } catch (gzipError) {
        console.error('SystemASR: Error compressing data:', gzipError);
        return inputData.buffer.slice(inputData.byteOffset, inputData.byteOffset + inputData.byteLength);
      }
    } catch (error) {
      console.error('SystemASR: Error importing zlib:', error);
      
      if (typeof data === 'string') {
        return new TextEncoder().encode(data).buffer;
      } else if (data instanceof ArrayBuffer) {
        return data;
      } else if (data instanceof Uint8Array) {
        return data.buffer;
      } else {
        throw new Error('Unsupported data type for compression');
      }
    }
  }

  private async decompressData(data: Uint8Array): Promise<Uint8Array> {
    try {
      const zlib = require('zlib');
      const { promisify } = require('util');
      const gunzipAsync = promisify(zlib.gunzip);
      
      try {
        const decompressed = await gunzipAsync(data);
        return new Uint8Array(decompressed);
      } catch (gunzipError) {
        console.warn('SystemASR: gunzip failed, data may not be compressed:', gunzipError);
        return data;
      }
    } catch (error) {
      console.error('SystemASR: Error importing zlib for decompression:', error);
      return data;
    }
  }

  private createHeader(messageType: number, messageTypeSpecificFlags: number, serializationMethod: number, compressionType: number, reservedData: number): Uint8Array {
    const header = new Uint8Array(4);
    header[0] = (PROTOCOL_VERSION << 4) | DEFAULT_HEADER_SIZE;
    header[1] = (messageType << 4) | messageTypeSpecificFlags;
    header[2] = (serializationMethod << 4) | compressionType;
    header[3] = reservedData;
    return header;
  }

  private intToBytes(value: number): Uint8Array {
    const bytes = new Uint8Array(4);
    bytes[0] = (value >> 24) & 0xFF;
    bytes[1] = (value >> 16) & 0xFF;
    bytes[2] = (value >> 8) & 0xFF;
    bytes[3] = value & 0xFF;
    return bytes;
  }

  private bytesToInt(bytes: Uint8Array): number {
    if (bytes.length !== 4) {
      throw new Error('Bytes to int conversion requires exactly 4 bytes');
    }
    return ((bytes[0] & 0xFF) << 24) |
           ((bytes[1] & 0xFF) << 16) |
           ((bytes[2] & 0xFF) << 8) |
           (bytes[3] & 0xFF);
  }

  private handleWebSocketMessage(data: any): void {
    // 记录消息接收性能指标
    this.performanceMetrics.messagesReceived++;

    if (data instanceof Blob) {
      const reader = new FileReader();
      reader.onload = () => {
        this.parseResponse(new Uint8Array(reader.result as ArrayBuffer));
      };
      reader.readAsArrayBuffer(data);
    } else if (data instanceof ArrayBuffer) {
      this.parseResponse(new Uint8Array(data));
    } else if (typeof data === 'string') {
      console.error('SystemASR: Error from server:', data);
      this.notifyError(data);
    }
  }

  private async parseResponse(data: Uint8Array): Promise<number> {
    if (!data || data.length === 0) {
      console.warn('SystemASR: Empty response data');
      return -1;
    }
    
    if (data.length < 12) {
      try {
        const textDecoder = new TextDecoder();
        const text = textDecoder.decode(data);
        console.log('SystemASR: Small response (possibly error):', text);
        this.notifyError(text);
        return -1;
      } catch (e) {
        console.error('SystemASR: Error decoding small response:', e);
      }
    }
    
    const protocolVersion = (data[0] >> 4) & 0x0F;
    const headerSize = data[0] & 0x0F;
    const messageType = (data[1] >> 4) & 0x0F;
    const messageTypeSpecificFlags = data[1] & 0x0F;
    const serializationMethod = (data[2] >> 4) & 0x0F;
    const compressionType = data[2] & 0x0F;
    const reserved = data[3];
    
    const sequenceBytes = data.slice(4, 8);
    const sequence = this.bytesToInt(sequenceBytes);
    
    const payloadSizeBytes = data.slice(8, 12);
    const payloadSize = this.bytesToInt(payloadSizeBytes);
    
    const payload = data.slice(12);
    
    let payloadStr = null;
    
    if (messageType === FULL_SERVER_RESPONSE) {
      console.log('SystemASR: Received FULL_SERVER_RESPONSE');
      
      if (compressionType === GZIP) {
        const decompressedPayload = await this.decompressData(payload);
        payloadStr = new TextDecoder().decode(decompressedPayload);
        await this.handleTranscriptResponse(payloadStr);
      } else {
        payloadStr = new TextDecoder().decode(payload);
        await this.handleTranscriptResponse(payloadStr);
      }
      
      this.readyToSendAudio = true;
    } else if (messageType === SERVER_ACK) {
      console.log('SystemASR: Received SERVER_ACK');
      
      if (payload.length > 0) {
        payloadStr = new TextDecoder().decode(payload);
        console.log('SystemASR: Server ACK payload:', payloadStr);
      }
      
      this.readyToSendAudio = true;
    } else if (messageType === SERVER_ERROR_RESPONSE) {
      console.log('SystemASR: Received SERVER_ERROR_RESPONSE');
      
      const errorCode = sequence;
      payloadStr = new TextDecoder().decode(payload);
      console.error(`SystemASR: Server error (code ${errorCode}):`, payloadStr);
      // this.notifyError(`Server error (${errorCode}): ${payloadStr}`);
      
      // this.readyToSendAudio = false;
    } else {
      console.warn('SystemASR: Unknown message type:', messageType);
    }
    
    return sequence;
  }

  private async handleTranscriptResponse(payloadStr: string): Promise<void> {
    try {
      if (this.isScheduledForClosure) {
        console.log('SystemASR: Connection scheduled for closure, ignoring transcript response');
        return;
      }
      
      console.log('SystemASR: Processing transcript response:', payloadStr);
      
      const response = JSON.parse(payloadStr);
      
      if (response.error) {
        console.error('SystemASR: Error in response:', response.error);
        this.notifyError(`SystemASR Error: ${response.error.message || 'Unknown error'}`);
        return;
      }
      
      const logId = response?.result?.additions?.log_id;
      if (logId && !this.sessionStarted) {
        console.log('SystemASR: New session started with ID:', logId);
        this.sessionStarted = true;
      }
      
      if (!SystemASRManager.lastTranscriptions) {
        SystemASRManager.lastTranscriptions = {
          finalTexts: new Set<string>(),
          lastInterimText: '',
          lastFinalText: '',
          lastTimestamp: 0,
          lastHistoryUpdateTime: 0,
          history: []
        };
      }
      
      const now = Date.now();
      // 调整重置时间阈值，避免长句被过早清空
      if (now - SystemASRManager.lastTranscriptions.lastTimestamp > 30000) {
        console.log('SystemASR: 距离上次转写超过30秒，清空临时记录');
        SystemASRManager.lastTranscriptions.lastInterimText = '';
        SystemASRManager.lastTranscriptions.lastFinalText = '';
        SystemASRManager.lastTranscriptions.finalTexts.clear();
      }
      SystemASRManager.lastTranscriptions.lastTimestamp = now;
      
      const paragraphId = `system-${Date.now()}`;
      let shouldUpdateHistory = false;
      
      if (response?.result?.utterances?.length > 0) {
        console.log(`SystemASR: Processing ${response.result.utterances.length} utterances`);
        
        for (const utterance of response.result.utterances) {
          if (!utterance.text) continue;
          
          const text = utterance.text.trim();
          if (text.length === 0) continue;
          
          const isFinal = utterance.definite === true;
          const startTimeMs = utterance.start_time || 0;
          const endTimeMs = utterance.end_time || 0;
          
          if (isFinal) {
            // 检查是否与最近的历史记录重复或包含
            let isRedundant = false;
            const recentHistory = SystemASRManager.lastTranscriptions.history.slice(-5);
            
            for (const historyItem of recentHistory) {
              // 检查完全相同
              if (historyItem.text === text) {
                console.log(`SystemASR: 跳过完全相同的文本: "${text}"`);
                isRedundant = true;
                break;
              }
              
              // 优化包含关系检查，降低去重阈值
              if (historyItem.text.includes(text) || text.includes(historyItem.text)) {
                const shorterText = historyItem.text.length < text.length ? historyItem.text : text;
                const longerText = historyItem.text.length >= text.length ? historyItem.text : text;
                
                // 降低阈值到50%，减少有效文本被误判为重复的情况
                if (longerText.includes(shorterText) && shorterText.length > longerText.length * 0.5) {
                  console.log(`SystemASR: 检测到文本重叠: "${text}" 与 "${historyItem.text}"`);
                  
                  // 如果新文本更长，更新现有记录
                  if (text.length > historyItem.text.length) {
                    historyItem.text = text;
                    historyItem.timestamp = new Date().toISOString();
                    shouldUpdateHistory = true;
                  }
                  
                  isRedundant = true;
                  break;
                }
              }
            }
            
            if (!isRedundant) {
              SystemASRManager.lastTranscriptions.lastFinalText = text;

              // 添加到finalTexts集合以供后续去重
              SystemASRManager.lastTranscriptions.finalTexts.add(text);
              // 限制finalTexts大小，防止内存泄漏
              if (SystemASRManager.lastTranscriptions.finalTexts.size > 20) {
                const oldestTexts = Array.from(SystemASRManager.lastTranscriptions.finalTexts).slice(0, 5);
                oldestTexts.forEach(t => SystemASRManager.lastTranscriptions!.finalTexts.delete(t));
              }

              // 创建历史项并添加到统一历史管理器
              const historyItem: HistoryItem = {
                text,
                source: 'system',
                timestamp: new Date().toISOString(),
                isFinal: true,
                paragraphId
              };

              // 使用统一历史管理器
              unifiedHistoryManager.addHistoryItem(historyItem);

              // 保持原有的本地历史记录（用于去重）
              SystemASRManager.lastTranscriptions.history.push(historyItem);

              // 限制历史记录大小
              if (SystemASRManager.lastTranscriptions.history.length > 50) {
                SystemASRManager.lastTranscriptions.history = SystemASRManager.lastTranscriptions.history.slice(-50);
              }

              shouldUpdateHistory = true;
            }
          } else {
            // 处理临时结果
            if (text === SystemASRManager.lastTranscriptions.lastInterimText) {
              console.log(`SystemASR: 跳过重复的临时结果: "${text}"`);
              continue;
            }
            
            // 减少临时结果的过滤，允许更多临时结果通过
            let isRedundant = false;
            
            // 只对太短的临时结果进行过滤
            if (text.length < 5) {
              for (const finalText of SystemASRManager.lastTranscriptions.finalTexts) {
                if (finalText.includes(text)) {
                  console.log(`SystemASR: 短临时结果被最终结果包含: "${text}"`);
                  isRedundant = true;
                  break;
                }
              }
            }
            
            if (!isRedundant) {
              SystemASRManager.lastTranscriptions.lastInterimText = text;
              
              // 发送临时结果到渲染进程
              if (this.mainWindow && !this.mainWindow.isDestroyed()) {
                const transcriptionData = {
                  text,
                  isFinal: false,
                  timestamp: new Date().toISOString(),
                  startTime: startTimeMs,
                  endTime: endTimeMs,
                  source: 'system',
                  paragraphId
                };
                
                this.mainWindow.webContents.send('asr:transcription', transcriptionData);
              }
            }
          }
        }
      }
      
      // 注意：历史记录更新现在由统一历史管理器处理
      // 这里不再需要单独发送历史更新事件
      
      this.readyToSendAudio = true;
      
      // 处理消息队列
      if (this.messageQueue.length > 0 && this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
        const nextAudioData = this.messageQueue.shift();
        if (nextAudioData) {
          this.processAudioData(nextAudioData).catch(err => {
            console.error(`SystemASR: Error processing queued audio data:`, err);
          });
        }
      }
    } catch (error) {
      console.error('SystemASR: Error parsing transcript response:', error);
      this.readyToSendAudio = true;
    }
  }

  private notifyError(message: string): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('system-asr:connection-status', { 
        connected: false,
        error: message
      });
      
      this.mainWindow.webContents.send('system-asr:error', { 
        message
      });
    }
  }

  public isASRConnected(): boolean {
    return this.isConnected && this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN;
  }

  public isASRProcessing(): boolean {
    return this.isProcessing;
  }

  public isConnectionBlocked(): boolean {
    return this.connectionAttemptBlocked;
  }

  public getCooldownTimeRemaining(): number {
    if (!this.connectionAttemptBlocked) return 0;
    
    const now = Date.now();
    const timeSinceLastConnection = now - this.lastConnectionTime;
    const remainingTime = Math.max(0, this.MIN_CONNECTION_INTERVAL - timeSinceLastConnection);
    
    return remainingTime;
  }

  private handleConnectionFailure(): void {
    console.log('SystemASR: Handling connection failure...');

    // 清理当前连接
    this.cleanupConnection();

    // 重置状态
    this.isConnected = false;
    this.sessionStarted = false;
    this.readyToSendAudio = false;
    this.clearIntervals();

    // 通知UI连接状态
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('system-asr:connection-status', {
        connected: false,
        reconnecting: this.reconnectAttempts < this.maxReconnectAttempts
      });
    }

    // 智能重连策略
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.scheduleReconnection();
    } else {
      console.error('SystemASR: 达到最大重连次数，停止重连尝试');
      this.notifyError('ASR服务连接失败，已达到最大重试次数');

      // 重置重连计数，允许用户手动重新启动
      setTimeout(() => {
        this.reconnectAttempts = 0;
        console.log('SystemASR: 重连计数已重置，可以重新尝试连接');
      }, 60000); // 1分钟后重置
    }
  }

  /**
   * 清理WebSocket连接和相关资源
   */
  private cleanupConnection(): void {
    if (this.wsConnection) {
      try {
        // 移除所有事件监听器，避免重复触发
        this.wsConnection.removeAllListeners();

        // 尝试优雅关闭
        if (this.wsConnection.readyState === WebSocket.OPEN) {
          this.wsConnection.close(1000, 'Client initiated cleanup');
        } else {
          this.wsConnection.terminate();
        }
      } catch (e) {
        console.error('SystemASR: Error during connection cleanup:', e);
        try {
          this.wsConnection.terminate();
        } catch (terminateError) {
          console.error('SystemASR: Error terminating connection:', terminateError);
        }
      } finally {
        this.wsConnection = null;
        this.sequence = 1; // 重置序列号
      }
    }
  }

  /**
   * 智能重连调度
   */
  private scheduleReconnection(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    this.reconnectAttempts++;

    // 指数退避算法，但有最大延迟限制
    const baseDelay = 2000;
    const maxDelay = 30000;
    const jitter = Math.random() * 1000; // 添加随机抖动，避免雷群效应
    const delay = Math.min(baseDelay * Math.pow(1.5, this.reconnectAttempts - 1) + jitter, maxDelay);

    console.log(`SystemASR: 调度重连尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts}，延迟 ${Math.round(delay)}ms`);

    this.reconnectTimer = setTimeout(async () => {
      console.log(`SystemASR: 执行重连尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);

      try {
        // 检查网络连接状态
        if (!await this.checkNetworkConnectivity()) {
          console.warn('SystemASR: 网络连接不可用，延迟重连');
          this.scheduleReconnection(); // 递归调度下一次重连
          return;
        }

        await this.connectToASRService();
        console.log('SystemASR: 重连成功');

        // 重连成功后处理队列中的消息
        this.processQueuedMessages();

      } catch (error) {
        console.error(`SystemASR: 重连尝试 ${this.reconnectAttempts} 失败:`, error);

        // 如果还有重连机会，继续尝试
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnection();
        } else {
          this.handleConnectionFailure(); // 达到最大重连次数
        }
      }
    }, delay);
  }

  /**
   * 检查网络连接状态
   */
  private async checkNetworkConnectivity(): Promise<boolean> {
    try {
      // 简单的网络连接检查
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch('https://www.baidu.com', {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache'
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      console.warn('SystemASR: 网络连接检查失败:', error);
      return false;
    }
  }



  /**
   * 熔断器模式实现
   */
  private circuitBreakerState: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  private failureCount: number = 0;
  private lastFailureTime: number = 0;
  private readonly FAILURE_THRESHOLD = 5;
  private readonly RECOVERY_TIMEOUT = 60000; // 1分钟

  /**
   * 检查熔断器状态
   */
  private checkCircuitBreaker(): boolean {
    const now = Date.now();

    switch (this.circuitBreakerState) {
      case 'CLOSED':
        return true; // 正常状态，允许请求

      case 'OPEN':
        if (now - this.lastFailureTime > this.RECOVERY_TIMEOUT) {
          console.log('SystemASR: 熔断器进入半开状态');
          this.circuitBreakerState = 'HALF_OPEN';
          return true;
        }
        console.log('SystemASR: 熔断器开启，拒绝连接请求');
        return false;

      case 'HALF_OPEN':
        return true; // 半开状态，允许一个请求测试

      default:
        return true;
    }
  }

  /**
   * 记录连接成功
   */
  private recordConnectionSuccess(): void {
    this.failureCount = 0;
    if (this.circuitBreakerState !== 'CLOSED') {
      console.log('SystemASR: 熔断器恢复到关闭状态');
      this.circuitBreakerState = 'CLOSED';
    }
  }

  /**
   * 记录连接失败
   */
  private recordConnectionFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.failureCount >= this.FAILURE_THRESHOLD) {
      console.log(`SystemASR: 连续失败${this.failureCount}次，熔断器开启`);
      this.circuitBreakerState = 'OPEN';
    }
  }

  /**
   * 性能监控系统
   */
  private performanceMetrics = {
    connectionAttempts: 0,
    successfulConnections: 0,
    failedConnections: 0,
    totalAudioDataSent: 0,
    totalAudioPacketsSent: 0,
    averagePacketSize: 0,
    lastResetTime: Date.now(),
    connectionUptime: 0,
    lastConnectionTime: 0,
    reconnectionCount: 0,
    averageReconnectionTime: 0,
    totalReconnectionTime: 0,
    messagesSent: 0,
    messagesReceived: 0,
    lastActivityTime: Date.now(),
    memoryUsage: {
      heapUsed: 0,
      heapTotal: 0,
      external: 0,
      rss: 0
    }
  };

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    // 每30秒记录一次性能指标
    setInterval(() => {
      this.recordPerformanceMetrics();
    }, 30000);

    // 每5分钟输出详细的性能报告
    setInterval(() => {
      this.generatePerformanceReport();
    }, 300000);
  }

  /**
   * 记录性能指标
   */
  private recordPerformanceMetrics(): void {
    const now = Date.now();

    // 更新连接正常运行时间
    if (this.isConnected && this.performanceMetrics.lastConnectionTime > 0) {
      this.performanceMetrics.connectionUptime = now - this.performanceMetrics.lastConnectionTime;
    }

    // 记录内存使用情况
    const memUsage = process.memoryUsage();
    this.performanceMetrics.memoryUsage = {
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
      external: Math.round(memUsage.external / 1024 / 1024), // MB
      rss: Math.round(memUsage.rss / 1024 / 1024) // MB
    };

    // 计算平均包大小
    if (this.performanceMetrics.totalAudioPacketsSent > 0) {
      this.performanceMetrics.averagePacketSize = Math.round(
        this.performanceMetrics.totalAudioDataSent / this.performanceMetrics.totalAudioPacketsSent
      );
    }

    // 计算平均重连时间
    if (this.performanceMetrics.reconnectionCount > 0) {
      this.performanceMetrics.averageReconnectionTime = Math.round(
        this.performanceMetrics.totalReconnectionTime / this.performanceMetrics.reconnectionCount
      );
    }

    this.performanceMetrics.lastActivityTime = now;
  }

  /**
   * 生成性能报告
   */
  private generatePerformanceReport(): void {
    const now = Date.now();
    const runningTime = (now - this.performanceMetrics.lastResetTime) / 1000 / 60; // 分钟

    console.log('=== SystemASR 性能报告 ===');
    console.log(`运行时间: ${runningTime.toFixed(1)} 分钟`);
    console.log(`连接尝试: ${this.performanceMetrics.connectionAttempts}`);
    console.log(`成功连接: ${this.performanceMetrics.successfulConnections}`);
    console.log(`失败连接: ${this.performanceMetrics.failedConnections}`);

    if (this.performanceMetrics.connectionAttempts > 0) {
      const successRate = (this.performanceMetrics.successfulConnections / this.performanceMetrics.connectionAttempts * 100).toFixed(1);
      console.log(`连接成功率: ${successRate}%`);
    }

    console.log(`当前连接状态: ${this.isConnected ? '已连接' : '未连接'}`);

    if (this.performanceMetrics.connectionUptime > 0) {
      console.log(`连接正常运行时间: ${(this.performanceMetrics.connectionUptime / 1000 / 60).toFixed(1)} 分钟`);
    }

    console.log(`重连次数: ${this.performanceMetrics.reconnectionCount}`);

    if (this.performanceMetrics.averageReconnectionTime > 0) {
      console.log(`平均重连时间: ${this.performanceMetrics.averageReconnectionTime} 毫秒`);
    }

    console.log(`音频数据发送: ${this.performanceMetrics.totalAudioPacketsSent} 包`);
    console.log(`音频数据总量: ${(this.performanceMetrics.totalAudioDataSent / 1024 / 1024).toFixed(2)} MB`);

    if (this.performanceMetrics.averagePacketSize > 0) {
      console.log(`平均包大小: ${this.performanceMetrics.averagePacketSize} 字节`);
    }

    console.log(`消息发送: ${this.performanceMetrics.messagesSent}`);
    console.log(`消息接收: ${this.performanceMetrics.messagesReceived}`);

    console.log(`内存使用 - 堆已用: ${this.performanceMetrics.memoryUsage.heapUsed}MB, 堆总计: ${this.performanceMetrics.memoryUsage.heapTotal}MB`);
    console.log(`内存使用 - 外部: ${this.performanceMetrics.memoryUsage.external}MB, RSS: ${this.performanceMetrics.memoryUsage.rss}MB`);

    console.log(`熔断器状态: ${this.circuitBreakerState}`);
    console.log(`失败计数: ${this.failureCount}`);

    console.log('========================');

    // 发送性能报告到UI
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('system-asr:performance-report', {
        runningTime: runningTime.toFixed(1),
        connectionAttempts: this.performanceMetrics.connectionAttempts,
        successfulConnections: this.performanceMetrics.successfulConnections,
        failedConnections: this.performanceMetrics.failedConnections,
        successRate: this.performanceMetrics.connectionAttempts > 0 ?
          (this.performanceMetrics.successfulConnections / this.performanceMetrics.connectionAttempts * 100).toFixed(1) : '0',
        isConnected: this.isConnected,
        connectionUptime: this.performanceMetrics.connectionUptime > 0 ?
          (this.performanceMetrics.connectionUptime / 1000 / 60).toFixed(1) : '0',
        reconnectionCount: this.performanceMetrics.reconnectionCount,
        averageReconnectionTime: this.performanceMetrics.averageReconnectionTime,
        audioPacketsSent: this.performanceMetrics.totalAudioPacketsSent,
        audioDataSent: (this.performanceMetrics.totalAudioDataSent / 1024 / 1024).toFixed(2),
        averagePacketSize: this.performanceMetrics.averagePacketSize,
        messagesSent: this.performanceMetrics.messagesSent,
        messagesReceived: this.performanceMetrics.messagesReceived,
        memoryUsage: this.performanceMetrics.memoryUsage,
        circuitBreakerState: this.circuitBreakerState,
        failureCount: this.failureCount,
        timestamp: new Date().toISOString()
      });
    }
  }

  public stopASRSession(): void {
    console.log('SystemASR: Stopping ASR session');
    
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
    }
    
    this.isScheduledForClosure = true;
    this.currentConnectionId = generateUUID();
    
    this.clearIntervals();
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.wsConnection) {
      console.log('SystemASR: Stopping session');
      
      try {
        if (this.wsConnection.readyState === WebSocket.OPEN) {
          const endMessage = { type: 'end' };
          this.wsConnection.send(JSON.stringify(endMessage));
          
          setTimeout(() => {
            if (this.wsConnection) {
              this.wsConnection.close(1000, 'Session ended by client');
              this.wsConnection = null;
              this.isScheduledForClosure = false;
            }
          }, 500);
        } else {
          this.wsConnection.close(1000, 'Session ended by client');
          this.wsConnection = null;
          this.isScheduledForClosure = false;
        }
      } catch (error) {
        console.error('SystemASR: Error stopping session:', error);
        
        if (this.wsConnection) {
          try {
            this.wsConnection.close();
          } catch (e) {
            console.error('SystemASR: Error closing WebSocket:', e);
          }
          this.wsConnection = null;
        }
        this.isScheduledForClosure = false;
      }
    } else {
      this.isScheduledForClosure = false;
    }
    
    this.isConnected = false;
    this.isProcessing = false;
    this.sessionStarted = false;
    this.messageQueue = [];
    this.isConnecting = false;
    this.sequence = 1; // 重置序列号
  }

  public dispose(): void {
    console.log('SystemASR: Disposing manager');
    
    this.lastAudioDataTime = 0;
    
    if (this.audioTimeoutCheckTimer) {
      clearInterval(this.audioTimeoutCheckTimer);
      this.audioTimeoutCheckTimer = null;
    }
    
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
    }
    
    this.clearIntervals();
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.connectionBlockTimeout) {
      clearTimeout(this.connectionBlockTimeout);
      this.connectionBlockTimeout = null;
    }
    
    if (this.wsConnection) {
      try {
        this.wsConnection.terminate();
      } catch (e) {
        console.error('SystemASR: Error terminating WebSocket during disposal:', e);
      }
      this.wsConnection = null;
    }
    
    this.isConnected = false;
    this.isProcessing = false;
    this.sessionStarted = false;
    this.isConnecting = false;
    this.isScheduledForClosure = false;
    this.sequence = 1; // 重置序列号
  }

  private startAudioTimeoutCheck(): void {
    if (this.audioTimeoutCheckTimer) {
      clearInterval(this.audioTimeoutCheckTimer);
    }

    this.audioTimeoutCheckTimer = setInterval(() => {
      if (this.isConnected && this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
        const now = Date.now();
        if (now - this.lastAudioDataTime > this.AUDIO_TIMEOUT_MS && this.lastAudioDataTime > 0) {
          console.log(`SystemASR: 超过${this.AUDIO_TIMEOUT_MS/1000}秒未接收到音频数据，准备关闭服务`);
          
          if (this.autoCloseTimer) {
            clearTimeout(this.autoCloseTimer);
          }
          
          this.autoCloseTimer = setTimeout(() => {
            console.log('SystemASR: 由于音频数据超时，自动关闭会话');
            this.stopASRSession();
          }, 2000);
        }
      }
    }, 2000);
  }

  /**
   * 去除历史记录中的重复项
   * 通过文本内容和来源进行去重，保留最新的记录
   */
  private deduplicateHistory<T extends { text: string; source: string; timestamp: string }>(history: T[]): T[] {
    // 使用Map按文本内容和来源分组，保留最新的记录
    const seen = new Map<string, T>();
    
    for (const item of history) {
      // 确保系统数据源始终为"system"
      if (item.source !== 'system') {
        (item as any).source = 'system';
      }
      
      // 使用文本和来源组合作为键，确保不同来源的相同文本被视为不同条目
      const key = `${item.source}:${item.text}`;
      
      // 如果是新记录或比已存在的记录更新，则保留
      if (!seen.has(key) || new Date(item.timestamp) > new Date(seen.get(key)!.timestamp)) {
        seen.set(key, item);
      }
    }
    
    // 根据时间戳排序，返回去重后的数组
    return Array.from(seen.values()).sort((a, b) =>
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
  }

  /**
   * 计算最优发送间隔
   * 根据音频数据大小动态调整发送间隔，提高识别准确度
   */
  private calculateOptimalSendInterval(audioDataLength: number): number {
    // 基础间隔：200ms（与音频文件读取间隔匹配）
    const BASE_INTERVAL = 200;

    // 根据数据大小调整间隔
    if (audioDataLength > 64000) {
      // 大数据块：减少间隔，快速处理
      return Math.max(100, BASE_INTERVAL * 0.5);
    } else if (audioDataLength > 32000) {
      // 中等数据块：标准间隔
      return BASE_INTERVAL * 0.75;
    } else if (audioDataLength > 16000) {
      // 小数据块：标准间隔
      return BASE_INTERVAL;
    } else {
      // 很小的数据块：增加间隔，避免频繁发送
      return BASE_INTERVAL * 1.5;
    }
  }

  /**
   * 缓存音频数据进行批量处理
   * 当发送间隔未到时，将数据缓存起来，达到一定大小后批量发送
   */
  private bufferAudioData(audioData: any): void {
    if (!audioData.audio_data || audioData.audio_data.length === 0) {
      return;
    }

    const audioBuffer = Buffer.from(audioData.audio_data);
    this.audioBuffer.push(audioBuffer);
    this.audioBufferSize += audioBuffer.length;

    // 当缓冲区达到最小大小或超过最大大小时，立即处理
    if (this.audioBufferSize >= this.MIN_BUFFER_SIZE || this.audioBufferSize >= this.MAX_BUFFER_SIZE) {
      this.flushAudioBuffer(audioData.sample_rate || 16000);
    }
  }

  /**
   * 清空音频缓冲区并发送数据
   */
  private flushAudioBuffer(sampleRate: number = 16000): void {
    if (this.audioBuffer.length === 0 || this.audioBufferSize === 0) {
      return;
    }

    // 合并所有缓冲的音频数据
    const combinedBuffer = Buffer.concat(this.audioBuffer, this.audioBufferSize);

    // 创建合并后的音频数据对象
    const combinedAudioData = {
      audio_data: new Uint8Array(combinedBuffer),
      sample_rate: sampleRate,
      audio_format: 'pcm'
    };

    // 重置缓冲区
    this.audioBuffer = [];
    this.audioBufferSize = 0;

    // 发送合并后的数据
    console.log(`SystemASR: 发送缓冲的音频数据，大小=${combinedBuffer.length}字节`);
    this.processCombinedAudioData(combinedAudioData).catch(error => {
      console.error('SystemASR: 发送缓冲音频数据失败:', error);
    });
  }

  /**
   * 处理合并后的音频数据（绕过间隔检查）
   */
  private async processCombinedAudioData(audioData: any): Promise<void> {
    const now = Date.now();
    SystemASRManager.lastSendTime = now;

    if (this.wsConnection.readyState === WebSocket.OPEN && this.readyToSendAudio) {
      try {
        const audioDataBytes = new Uint8Array(audioData.audio_data);

        if (!audioDataBytes || audioDataBytes.length === 0) {
          console.warn(`SystemASR: 收到空的合并音频数据，跳过处理`);
          return;
        }

        // 继续使用原有的处理逻辑...
        await this.sendAudioDataToASR(audioDataBytes, audioData.sample_rate || 16000);

      } catch (error) {
        console.error(`SystemASR: 发送合并音频数据出错:`, error);
        throw error;
      }
    } else {
      console.warn(`SystemASR: WebSocket未准备好，合并音频数据加入队列`);
      this.messageQueue.push(audioData);
    }
  }

  /**
   * 发送音频数据到ASR服务的核心方法
   */
  private async sendAudioDataToASR(audioDataBytes: Uint8Array, sampleRate: number): Promise<void> {
    const nonZeroSamples = Array.from(audioDataBytes).filter(value => value !== 0).length;
    const percentNonZero = (nonZeroSamples / audioDataBytes.length) * 100;

    // 调整静音阈值，允许更多低音量数据通过
    if (percentNonZero < 0.05) {
      console.log('SystemASR: 检测到几乎完全静音数据，跳过发送');
      return;
    }

    let processedAudioData = audioDataBytes;

    if (sampleRate !== 16000) {
      processedAudioData = this.convertSampleRate(audioDataBytes, sampleRate, 16000);
    }

    if (processedAudioData.length % 2 !== 0) {
      processedAudioData = processedAudioData.slice(0, processedAudioData.length - 1);
    }

    const compressedAudio = await this.compressData(processedAudioData);
    const compressedData = new Uint8Array(compressedAudio);

    if (!compressedData || compressedData.length === 0) {
      console.error(`SystemASR: 音频数据压缩失败，跳过发送`);
      return;
    }

    // 递增序列号
    this.sequence++;

    const header = this.createHeader(AUDIO_ONLY_REQUEST, POS_SEQUENCE, JSON_FORMAT, GZIP, 0);
    const sequenceBytes = this.intToBytes(this.sequence);
    const payloadSize = this.intToBytes(compressedData.length);

    const totalSize = header.length + sequenceBytes.length + payloadSize.length + compressedData.length;
    const message = new Uint8Array(totalSize);

    let offset = 0;
    message.set(header, offset);
    offset += header.length;
    message.set(sequenceBytes, offset);
    offset += sequenceBytes.length;
    message.set(payloadSize, offset);
    offset += payloadSize.length;
    message.set(compressedData, offset);

    console.log(`SystemASR: 发送音频数据，序列号=${this.sequence}, 总大小=${totalSize}字节`);
    this.wsConnection.send(message);

    // 记录消息发送性能指标
    this.performanceMetrics.messagesSent++;

    this.lastActivityTimestamp = Date.now();
    this.isProcessing = true;
  }
}

export const systemASRManager = SystemASRManager.getInstance();
export default systemASRManager;