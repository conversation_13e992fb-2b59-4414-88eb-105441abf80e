/**
 * NativeMicrophoneManager.ts
 * 统一音频管理器 - 基于asar项目的nativeMacRecorder实现
 *
 * 复用asar项目中的native recorder功能，实现麦克风音频和系统音频的实时捕获
 * v2版本的捕获程序能够一起处理麦克风音频和系统音频，通过source字段判断类型
 */

import { ChildProcess, spawn } from 'child_process';
import { app, BrowserWindow } from 'electron';
import { EventEmitter } from 'events';
import fs from 'fs';
import path from 'path';
import { microphoneASRManager } from '../MicrophoneASRManager';
import { systemASRManager } from '../SystemASRManager';

export class NativeMicrophoneManager extends EventEmitter {
  private recordingProcess: ChildProcess | null = null;
  private mainWindow: BrowserWindow | null = null;
  private isRecording: boolean = false;
  private static instance: NativeMicrophoneManager | null = null;

  // 音频源配置
  private audioSources = {
    microphone: false,
    system: false
  };
  
  // 音频配置
  private audioConfig = {
    sampleRate: 16000,
    channels: 1,
    bitsPerSample: 16,
    format: 'pcm'
  };
  
  // 性能监控
  private performanceMetrics = {
    recordingStartTime: 0,
    totalAudioChunksCaptured: 0,
    totalAudioDataCaptured: 0,
    averageChunkSize: 0,
    lastChunkTime: 0,
    droppedChunks: 0,
    base64ChunksReceived: 0
  };
  
  // 音频处理配置
  private processingConfig = {
    chunkSize: 6400, // 0.2秒的16kHz 16位音频
    maxChunkSize: 32000, // 最大块大小
    silenceThreshold: 0.005, // 静音阈值
    bufferTimeout: 200 // 缓冲超时时间(ms)
  };
  
  // 音频缓冲区
  private audioBuffer: Buffer[] = [];
  private audioBufferSize: number = 0;
  private bufferTimer: NodeJS.Timeout | null = null;
  
  // Native recorder路径
  private nativeRecorderPath: string = '';
  private nativeRecorderV2Path: string = '';

  constructor() {
    super();
    this.setupNativeRecorderPaths();
    this.startPerformanceMonitoring();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): NativeMicrophoneManager {
    if (!NativeMicrophoneManager.instance) {
      NativeMicrophoneManager.instance = new NativeMicrophoneManager();
    }
    return NativeMicrophoneManager.instance;
  }

  /**
   * 设置主窗口引用
   */
  public setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
    microphoneASRManager.setMainWindow(window);
    systemASRManager.setMainWindow(window);
    console.log('NativeMicrophoneManager: 主窗口已设置');
  }

  /**
   * 设置native recorder路径
   */
  private setupNativeRecorderPaths(): void {
    const macExtraResources = app.isPackaged 
      ? path.join(process.resourcesPath, 'macExtraResources')
      : path.join(app.getAppPath(), 'macExtraResources');
    
    this.nativeRecorderPath = path.join(macExtraResources, 'nativeMacRecorder');
    this.nativeRecorderV2Path = path.join(macExtraResources, 'nativeMacRecorder_v2');
    
    console.log('NativeMicrophoneManager: Native recorder路径设置完成');
    console.log('  - V1路径:', this.nativeRecorderPath);
    console.log('  - V2路径:', this.nativeRecorderV2Path);
  }

  /**
   * 启动音频捕获（支持麦克风和系统音频）
   */
  public async startCapturing(options: {
    useV2?: boolean;
    enableMicrophone?: boolean;
    enableSystemAudio?: boolean;
  } = {}): Promise<{ success: boolean, error?: string }> {
    const { useV2 = true, enableMicrophone = true, enableSystemAudio = false } = options;

    console.log('NativeMicrophoneManager: 开始启动音频捕获', {
      useV2,
      enableMicrophone,
      enableSystemAudio
    });

    if (this.isRecording) {
      console.log('NativeMicrophoneManager: 音频已在录制中');
      return { success: true };
    }

    // 检查平台支持
    if (process.platform !== 'darwin') {
      return { success: false, error: '当前仅支持macOS平台' };
    }

    // 记录录音开始时间
    this.performanceMetrics.recordingStartTime = Date.now();

    // 更新音频源配置
    this.audioSources.microphone = enableMicrophone;
    this.audioSources.system = enableSystemAudio;

    try {
      // 第一步：启动ASR服务
      console.log('NativeMicrophoneManager: 第一步 - 启动ASR服务');

      const asrResults: Array<{ type: string; success: boolean; error?: string }> = [];

      if (enableMicrophone) {
        const micResult = await microphoneASRManager.startASRSession();
        asrResults.push({ type: 'microphone', ...micResult });
        if (!micResult.success) {
          console.error('NativeMicrophoneManager: MicrophoneASR服务启动失败:', micResult.error);
        } else {
          console.log('NativeMicrophoneManager: MicrophoneASR服务启动成功');
        }
      }

      if (enableSystemAudio) {
        const sysResult = await systemASRManager.startASRSession();
        asrResults.push({ type: 'system', ...sysResult });
        if (!sysResult.success) {
          console.error('NativeMicrophoneManager: SystemASR服务启动失败:', sysResult.error);
        } else {
          console.log('NativeMicrophoneManager: SystemASR服务启动成功');
        }
      }

      // 检查是否至少有一个ASR服务启动成功
      const successfulServices = asrResults.filter(r => r.success);
      if (successfulServices.length === 0) {
        return { success: false, error: 'ASR服务启动失败' };
      }

      // 第二步：启动native recorder
      console.log('NativeMicrophoneManager: 第二步 - 启动native recorder');

      // 确保之前的进程已停止
      if (this.recordingProcess) {
        console.log('NativeMicrophoneManager: 停止现有的录制进程');
        try {
          this.recordingProcess.kill('SIGINT');
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (error) {
          console.warn('NativeMicrophoneManager: 停止现有进程时出错:', error);
        }
        this.recordingProcess = null;
      }

      // 选择recorder版本（v2版本支持同时捕获麦克风和系统音频）
      const recorderPath = useV2 ? this.nativeRecorderV2Path : this.nativeRecorderPath;

      // 检查recorder文件是否存在
      if (!fs.existsSync(recorderPath)) {
        console.error(`NativeMicrophoneManager: Native recorder不存在: ${recorderPath}`);
        return { success: false, error: `Native recorder不存在: ${recorderPath}` };
      }

      // 启动native recorder进程
      const args = [this.audioConfig.sampleRate.toString()];
      console.log('NativeMicrophoneManager: 启动native recorder，参数:', args);

      this.recordingProcess = spawn(recorderPath, args, {
        stdio: ['ignore', 'pipe', 'pipe']
      });

      // 设置进程事件监听器
      this.setupProcessEventListeners(useV2);

      this.isRecording = true;
      console.log('NativeMicrophoneManager: 音频捕获启动成功');

      // 通知UI状态变化
      this.notifyStatusChange('started');

      return { success: true };

    } catch (error) {
      console.error('NativeMicrophoneManager: 启动失败:', error);
      this.isRecording = false;
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '启动失败' 
      };
    }
  }

  /**
   * 停止音频捕获
   */
  public async stopCapturing(): Promise<{ success: boolean, error?: string }> {
    console.log('NativeMicrophoneManager: 开始停止音频捕获');

    if (!this.isRecording) {
      console.log('NativeMicrophoneManager: 音频未在录制中');
      return { success: true };
    }

    try {
      this.isRecording = false;

      // 停止录音进程
      if (this.recordingProcess) {
        console.log('NativeMicrophoneManager: 停止录音进程');
        this.recordingProcess.kill('SIGINT');

        // 等待进程结束
        await new Promise((resolve) => {
          if (this.recordingProcess) {
            this.recordingProcess.on('exit', resolve);
            setTimeout(resolve, 3000); // 3秒超时
          } else {
            resolve(undefined);
          }
        });

        this.recordingProcess = null;
      }

      // 清理缓冲区
      this.clearAudioBuffer();

      // 停止ASR服务
      if (this.audioSources.microphone) {
        try {
          microphoneASRManager.stopASRSession();
          console.log('NativeMicrophoneManager: MicrophoneASR服务已停止');
        } catch (error) {
          console.warn('NativeMicrophoneManager: 停止MicrophoneASR服务时出错:', error);
        }
      }

      if (this.audioSources.system) {
        try {
          systemASRManager.stopASRSession();
          console.log('NativeMicrophoneManager: SystemASR服务已停止');
        } catch (error) {
          console.warn('NativeMicrophoneManager: 停止SystemASR服务时出错:', error);
        }
      }

      console.log('NativeMicrophoneManager: 音频捕获已停止');

      // 通知UI状态变化
      this.notifyStatusChange('stopped');

      return { success: true };

    } catch (error) {
      console.error('NativeMicrophoneManager: 停止失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '停止失败'
      };
    }
  }

  /**
   * 设置进程事件监听器
   */
  private setupProcessEventListeners(useV2: boolean): void {
    if (!this.recordingProcess) return;

    let lineBuffer = '';

    this.recordingProcess.stdout?.on('data', (data) => {
      const dataStr = data.toString();
      const lines = (lineBuffer + dataStr).split('\n');
      lineBuffer = lines.pop() ?? '';

      if (useV2) {
        // V2版本：支持mic和system分离
        const audioData: { [key: string]: string[] } = {};

        for (const line of lines) {
          const colonIndex = line.indexOf(':');
          if (colonIndex === -1) continue;

          const source = line.slice(0, colonIndex);
          const base64Data = line.slice(colonIndex + 1);

          audioData[source] ??= [];
          audioData[source].push(base64Data);
        }

        // 处理麦克风数据
        if (audioData['mic'] && this.audioSources.microphone) {
          this.handleBase64AudioData(audioData['mic'].join(''), 'mic');
        }

        // 处理系统音频数据
        if (audioData['system'] && this.audioSources.system) {
          this.handleBase64AudioData(audioData['system'].join(''), 'system');
        }
      } else {
        // V1版本：默认处理为麦克风音频
        for (const line of lines) {
          if (line.trim() && this.audioSources.microphone) {
            this.handleBase64AudioData(line, 'mic');
          }
        }
      }
    });

    this.recordingProcess.stderr?.on('data', (data) => {
      const errorMsg = data.toString();
      console.error('NativeMicrophoneManager stderr:', errorMsg);
      
      // 检查是否是致命错误
      if (errorMsg.includes('FAIL') || errorMsg.includes('cannot open')) {
        console.error('NativeMicrophoneManager: 检测到致命错误，停止录制');
        this.stopCapturing();
      }
    });

    this.recordingProcess.on('exit', (code, signal) => {
      console.log(`NativeMicrophoneManager: 录音进程退出，代码: ${code}, 信号: ${signal}`);
      
      if (this.isRecording && code !== 0) {
        console.error('NativeMicrophoneManager: 录音进程异常退出');
        this.isRecording = false;
        this.notifyStatusChange('error');
      }
      
      this.recordingProcess = null;
    });

    this.recordingProcess.on('error', (error) => {
      console.error('NativeMicrophoneManager: 录音进程错误:', error);
      this.isRecording = false;
      this.notifyStatusChange('error');
    });
  }

  /**
   * 处理Base64音频数据
   */
  private async handleBase64AudioData(base64Data: string, source: string): Promise<void> {
    if (!this.isRecording || !base64Data.trim()) {
      return;
    }

    try {
      // 解码Base64数据
      const audioBuffer = Buffer.from(base64Data, 'base64');

      // 更新性能指标
      this.performanceMetrics.base64ChunksReceived++;
      this.performanceMetrics.totalAudioDataCaptured += audioBuffer.length;
      this.performanceMetrics.lastChunkTime = Date.now();

      // 音频质量分析
      const quality = this.analyzeAudioQuality(audioBuffer);

      // 静音检测
      if (quality.isSilent) {
        console.log(`NativeMicrophoneManager: 检测到静音 (${source})，跳过处理`);
        return;
      }

      // 根据音频源类型处理数据
      await this.processAudioBySource(audioBuffer, source);

    } catch (error) {
      console.error('NativeMicrophoneManager: 处理Base64音频数据失败:', error);
      this.performanceMetrics.droppedChunks++;
    }
  }

  /**
   * 根据音频源类型处理音频数据
   */
  private async processAudioBySource(audioBuffer: Buffer, source: string): Promise<void> {
    // 添加到缓冲区
    this.addToAudioBuffer(audioBuffer);

    // 检查是否需要立即发送
    if (this.audioBufferSize >= this.processingConfig.chunkSize) {
      await this.flushAudioBuffer(source);
    } else {
      // 设置缓冲超时
      this.setBufferTimeout(source);
    }
  }

  /**
   * 音频质量分析
   */
  private analyzeAudioQuality(audioData: Buffer): { isSilent: boolean; rms: number; energy: number } {
    // 转换为16位有符号样本
    const samples = new Int16Array(audioData.length / 2);
    for (let i = 0; i < samples.length; i++) {
      let sample = (audioData[i * 2] & 0xff) | ((audioData[i * 2 + 1] & 0xff) << 8);
      if (sample > 32767) sample -= 65536;
      samples[i] = sample;
    }

    // 计算RMS和能量
    let sumSquares = 0;
    let nonZeroCount = 0;

    for (let i = 0; i < samples.length; i++) {
      const sample = samples[i];
      sumSquares += sample * sample;
      if (sample !== 0) nonZeroCount++;
    }

    const rms = Math.sqrt(sumSquares / samples.length) / 32768;
    const energy = sumSquares / samples.length;
    const nonZeroRatio = nonZeroCount / samples.length;

    // 静音判断
    const isSilent = rms < this.processingConfig.silenceThreshold || nonZeroRatio < 0.01;

    return { isSilent, rms, energy };
  }

  /**
   * 添加到音频缓冲区
   */
  private addToAudioBuffer(audioData: Buffer): void {
    this.audioBuffer.push(audioData);
    this.audioBufferSize += audioData.length;
  }

  /**
   * 设置缓冲超时
   */
  private setBufferTimeout(source: string): void {
    if (this.bufferTimer) {
      clearTimeout(this.bufferTimer);
    }

    this.bufferTimer = setTimeout(async () => {
      if (this.audioBufferSize > 0) {
        await this.flushAudioBuffer(source);
      }
    }, this.processingConfig.bufferTimeout);
  }

  /**
   * 清空音频缓冲区并发送
   */
  private async flushAudioBuffer(source: string): Promise<void> {
    if (this.audioBuffer.length === 0 || this.audioBufferSize === 0) {
      return;
    }

    // 清除缓冲超时
    if (this.bufferTimer) {
      clearTimeout(this.bufferTimer);
      this.bufferTimer = null;
    }

    // 合并所有缓冲的音频数据
    const combinedBuffer = Buffer.concat(this.audioBuffer, this.audioBufferSize);

    // 重置缓冲区
    this.audioBuffer = [];
    this.audioBufferSize = 0;

    // 根据音频源发送到对应的ASR服务
    try {
      const audioData = {
        audio_data: new Uint8Array(combinedBuffer),
        sample_rate: this.audioConfig.sampleRate,
        audio_format: 'pcm' as const
      };

      console.log(`NativeMicrophoneManager: 发送音频数据 (${source}): ${combinedBuffer.length}字节`);

      if (source === 'mic' && microphoneASRManager.isASRConnected()) {
        await microphoneASRManager.processAudioData(audioData);
        console.log('NativeMicrophoneManager: 麦克风音频数据已发送到MicrophoneASR');
      } else if (source === 'system' && systemASRManager.isASRConnected()) {
        await systemASRManager.processAudioData(audioData);
        console.log('NativeMicrophoneManager: 系统音频数据已发送到SystemASR');
      } else {
        console.warn(`NativeMicrophoneManager: ${source} ASR服务未连接，丢弃音频数据`);
        this.performanceMetrics.droppedChunks++;
        return;
      }

      // 更新性能指标
      this.performanceMetrics.totalAudioChunksCaptured++;
      this.performanceMetrics.averageChunkSize =
        this.performanceMetrics.totalAudioDataCaptured / this.performanceMetrics.totalAudioChunksCaptured;

    } catch (error) {
      console.error(`NativeMicrophoneManager: 发送音频数据失败 (${source}):`, error);
      this.performanceMetrics.droppedChunks++;
    }
  }

  /**
   * 清空音频缓冲区
   */
  private clearAudioBuffer(): void {
    this.audioBuffer = [];
    this.audioBufferSize = 0;

    if (this.bufferTimer) {
      clearTimeout(this.bufferTimer);
      this.bufferTimer = null;
    }

    console.log('NativeMicrophoneManager: 音频缓冲区已清空');
  }

  /**
   * 通知UI状态变化
   */
  private notifyStatusChange(status: 'started' | 'stopped' | 'error'): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('native-microphone:status', {
        status,
        isRecording: this.isRecording,
        timestamp: new Date().toISOString(),
        performanceMetrics: this.performanceMetrics
      });
    }

    // 发出事件
    this.emit('statusChange', { status, isRecording: this.isRecording });
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    // 每30秒输出一次性能报告
    setInterval(() => {
      this.generatePerformanceReport();
    }, 30000);
  }

  /**
   * 生成性能报告
   */
  private generatePerformanceReport(): void {
    if (!this.isRecording) return;

    const now = Date.now();
    const runningTime = this.performanceMetrics.recordingStartTime > 0
      ? (now - this.performanceMetrics.recordingStartTime) / 1000
      : 0;

    console.log('=== NativeMicrophoneManager 统一音频管理器性能报告 ===');
    console.log(`录制时间: ${runningTime.toFixed(1)} 秒`);
    console.log(`音频源配置: 麦克风=${this.audioSources.microphone}, 系统音频=${this.audioSources.system}`);
    console.log(`ASR连接状态: 麦克风=${microphoneASRManager.isASRConnected()}, 系统音频=${systemASRManager.isASRConnected()}`);
    console.log(`接收Base64块数: ${this.performanceMetrics.base64ChunksReceived}`);
    console.log(`发送音频块数: ${this.performanceMetrics.totalAudioChunksCaptured}`);
    console.log(`捕获音频总量: ${(this.performanceMetrics.totalAudioDataCaptured / 1024 / 1024).toFixed(2)} MB`);
    console.log(`平均块大小: ${this.performanceMetrics.averageChunkSize.toFixed(0)} 字节`);
    console.log(`丢弃块数: ${this.performanceMetrics.droppedChunks}`);
    console.log(`当前缓冲区大小: ${this.audioBufferSize} 字节`);
    console.log(`录制状态: ${this.isRecording ? '活跃' : '停止'}`);
    console.log('=======================================================');

    // 发送性能报告到UI
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('native-microphone:performance', {
        runningTime: runningTime.toFixed(1),
        base64ChunksReceived: this.performanceMetrics.base64ChunksReceived,
        totalChunksCaptured: this.performanceMetrics.totalAudioChunksCaptured,
        totalDataCaptured: (this.performanceMetrics.totalAudioDataCaptured / 1024 / 1024).toFixed(2),
        averageChunkSize: this.performanceMetrics.averageChunkSize.toFixed(0),
        droppedChunks: this.performanceMetrics.droppedChunks,
        currentBufferSize: this.audioBufferSize,
        isRecording: this.isRecording,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 获取当前状态
   */
  public getStatus(): {
    isRecording: boolean;
    performanceMetrics: typeof this.performanceMetrics;
    audioConfig: typeof this.audioConfig;
    processingConfig: typeof this.processingConfig;
    currentBufferSize: number;
  } {
    return {
      isRecording: this.isRecording,
      performanceMetrics: { ...this.performanceMetrics },
      audioConfig: { ...this.audioConfig },
      processingConfig: { ...this.processingConfig },
      currentBufferSize: this.audioBufferSize
    };
  }

  /**
   * 检查是否正在录制
   */
  public isCapturing(): boolean {
    return this.isRecording;
  }

  /**
   * 检查native recorder是否可用
   */
  public isNativeRecorderAvailable(useV2: boolean = false): boolean {
    const recorderPath = useV2 ? this.nativeRecorderV2Path : this.nativeRecorderPath;
    return fs.existsSync(recorderPath);
  }

  /**
   * 获取native recorder信息
   */
  public getNativeRecorderInfo(): {
    v1Available: boolean;
    v2Available: boolean;
    v1Path: string;
    v2Path: string;
    platform: string;
  } {
    return {
      v1Available: this.isNativeRecorderAvailable(false),
      v2Available: this.isNativeRecorderAvailable(true),
      v1Path: this.nativeRecorderPath,
      v2Path: this.nativeRecorderV2Path,
      platform: process.platform
    };
  }

  /**
   * 获取当前音频源配置
   */
  public getAudioSourcesConfig(): {
    microphone: boolean;
    system: boolean;
  } {
    return { ...this.audioSources };
  }

  /**
   * 检查指定音频源是否启用
   */
  public isAudioSourceEnabled(source: 'microphone' | 'system'): boolean {
    return this.audioSources[source];
  }

  /**
   * 获取ASR连接状态
   */
  public getASRConnectionStatus(): {
    microphone: boolean;
    system: boolean;
  } {
    return {
      microphone: microphoneASRManager.isASRConnected(),
      system: systemASRManager.isASRConnected()
    };
  }

  /**
   * 重启录制
   */
  public async restartCapturing(options: {
    useV2?: boolean;
    enableMicrophone?: boolean;
    enableSystemAudio?: boolean;
  } = {}): Promise<{ success: boolean, error?: string }> {
    console.log('NativeMicrophoneManager: 重启录制');

    try {
      // 先停止
      const stopResult = await this.stopCapturing();
      if (!stopResult.success) {
        console.warn('NativeMicrophoneManager: 停止时出现警告:', stopResult.error);
      }

      // 等待一段时间
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 重新启动
      const startResult = await this.startCapturing(options);

      console.log('NativeMicrophoneManager: 重启完成');
      return startResult;
    } catch (error) {
      console.error('NativeMicrophoneManager: 重启失败', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '重启失败'
      };
    }
  }

  /**
   * 清理资源
   */
  public async cleanup(): Promise<void> {
    console.log('NativeMicrophoneManager: 开始清理资源');

    // 停止录制
    if (this.isRecording) {
      await this.stopCapturing();
    }

    // 清理缓冲区
    this.clearAudioBuffer();

    // 移除所有事件监听器
    this.removeAllListeners();

    console.log('NativeMicrophoneManager: 资源清理完成');
  }
}

// 导出单例实例
export const nativeMicrophoneManager = NativeMicrophoneManager.getInstance();

// 为了向后兼容，也导出为统一音频管理器
export const nativeUnifiedAudioManager = nativeMicrophoneManager;
