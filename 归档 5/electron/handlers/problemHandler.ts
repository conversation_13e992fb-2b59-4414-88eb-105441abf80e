// Import necessary modules
import axios from "axios";
import { store } from "../store";
const fs = require('fs');
const path = require('path');
const crypto = require('crypto'); // Import crypto module for decryption
const { jsonrepair } = require('jsonrepair');

// Define interfaces for ProblemInfo and related structures

interface VoiceConfig {
  voiceEnabled: boolean;
  voiceAppId: string;
  voiceAccessKeyId: string;
}

interface ConfigResponse {
  success: boolean;
  message: string;
  data?: {
    models: string[];
    codeLanguages: string[];
    defaultLanguage: string;
    voiceConfig: VoiceConfig;
  };
  error?: string;
}

interface DebugSolutionResponse {
  thoughts: string[]
  old_code: string
  new_code: string
  time_complexity: string
  space_complexity: string
}

interface ProblemInfo {
  problem_statement?: string
  input_format?: {
    description?: string
    parameters?: Array<{
      name: string
      type: string
      subtype?: string
    }>
  }
  output_format?: {
    description?: string
    type?: string
    subtype?: string
  }
  constraints?: Array<{
    description: string
    parameter?: string
    range?: {
      min?: number
      max?: number
    }
  }>
  test_cases?: any // Adjust the type as needed
}

export async function getApiKey(): Promise<string | null> {
  return store.get("openaiApiKey")
}

/**
 * 初始化配置，调用 CodeMossController 的 initConfig 方法
 * @param apiKey API密钥
 * @returns Promise<ConfigResponse> 配置响应
 */
export async function initConfig(apiKey: string): Promise<ConfigResponse> {
  try {
    console.log('开始初始化配置...', apiKey);

    const response = await axios.get('http://*************:9000/api/codeMoss/initConfig', {
      headers: {
        'Authorization': apiKey,
        'Content-Type': 'application/json'
      },
      timeout: 10000 // 10秒超时
    });

    const configResponse = response.data;
    
    if (configResponse) {
      // 将配置存储到 store 中
      const res : ConfigResponse = {
        success: true,
        message: 'init success',
        data: configResponse
      };
      store.set('config', res);
      return res;
    } else {
      const res : ConfigResponse = {
        success: false,
        message: 'init failed',
        data: configResponse
      };
      return res;
    }
  } catch (error) {
    const res : ConfigResponse = {
        success: false,
        message: 'init failed'
      };
      return res;
  }
}

/**
 * 获取存储的配置
 * @returns ConfigResponse | null
 */
export function getStoredConfig(): ConfigResponse | null {
  return store.get('config');
}

/**
 * 处理来自语音识别的文本，使用极速模式（如Gemini）
 * @param text 语音识别文本
 * @param onChunk 流式响应的回调函数，接收每个响应片段
 * @returns 返回Promise，处理完成时resolve
 */
export async function processFastVoiceText(
  text: string, 
  onChunk?: (chunk: string, done: boolean) => void
): Promise<string> {
  try {
    // 获取API密钥和当前使用的模型
    const apiKey = store.get("openaiApiKey") as string;
    
    if (!apiKey) {
      throw new Error("API密钥未设置");
    }
    console.log("处理极速模式文本:", text);
    
    // 如果提供了onChunk回调，则使用服务端事件流(SSE)
    if (onChunk) {
      // 使用SSE流式API
      const controller = new AbortController();
      const fullResponse: string[] = [];
      
      try {
        console.log("开始发送极速模式POST请求...");
        const response = await fetch("http://*************:9000/api/codeMoss/processAudio", {
          method: 'POST',
          headers: {
            'Authorization': apiKey,
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'text/event-stream'
          },
          body: new URLSearchParams({
            'problem': text, // 这里可以替换为实际的text参数
            'isAccurate': 'false' // 极速模式
          }),
          signal: controller.signal
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        console.log("极速模式请求成功，开始处理响应流...");
        
        // 创建响应流读取器
        const reader = response.body?.getReader();
        const decoder = new TextDecoder();
        
        if (!reader) {
          throw new Error('无法获取响应流');
        }
        
        let buffer = '';
        let done = false;
        
        while (!done) {
          const { value, done: doneReading } = await reader.read();
          done = doneReading;
          
          if (value) {
            // 解码字节流为文本
            const chunk = decoder.decode(value, { stream: !done });
            // console.log("收到数据块:", chunk.substring(0, 50) + (chunk.length > 50 ? "..." : ""));
            buffer += chunk;
            
            // 处理SSE格式的数据 - 参考 audio-test.html 的实现
            const eventChunks = buffer.split('\n\n');
            buffer = eventChunks.pop() || '';

            eventChunks.forEach(chunk => {
              if (!chunk.trim()) return;

              // console.log('接收到数据块:', chunk);

              // 解析SSE格式 - 参考 audio-test.html
              const lines = chunk.split("\n");
              let data = "";

              lines.forEach(line => {
                if (line.startsWith("data:")) {
                  data = line.substring(5).trim();
                  if (data) {
                    try {
                      const item = JSON.parse(data); // 尝试解析JSON，确保数据格式正确
                      if (item.choices && item.choices.length > 0) {
                        data = item.choices[0].delta.content; // 获取第一个选择的文本
                      } else {
                        data = ''; // 如果没有有效数据，清空
                      }
                      if (data) {
                        console.log("处理数据:", data.substring(0, 50) + (data.length > 50 ? "..." : ""));
                        fullResponse.push(data);
                        onChunk(data, false);
                      }
                    } catch (e) {
                      // 如果不是JSON格式，直接使用原始数据
                      console.log("非JSON数据，直接使用:", data.substring(0, 50) + (data.length > 50 ? "..." : ""));
                      fullResponse.push(data);
                      onChunk(data, false);
                    }
                  }
                }
              });
            });
          }
          
          if (done) {
            // 处理缓冲区中剩余的数据 - 参考 audio-test.html 的实现
            if (buffer.trim() !== '') {
              const lines = buffer.split("\n");
              let data = "";

              lines.forEach(line => {
                if (line.startsWith("data:")) {
                  data = line.substring(5).trim();
                  if (data) {
                    try {
                      const item = JSON.parse(data); // 尝试解析JSON，确保数据格式正确
                      if (item.choices && item.choices.length > 0) {
                        data = item.choices[0].delta.content; // 获取第一个选择的文本
                      } else {
                        data = ''; // 如果没有有效数据，清空
                      }
                      if (data) {
                        console.log("处理最终数据块:", data.substring(0, 50) + (data.length > 50 ? "..." : ""));
                        fullResponse.push(data);
                        onChunk(data, false);
                      }
                    } catch (e) {
                      // 如果不是JSON格式，直接使用原始数据
                      console.log("最终非JSON数据，直接使用:", data.substring(0, 50) + (data.length > 50 ? "..." : ""));
                      fullResponse.push(data);
                      onChunk(data, false);
                    }
                  }
                }
              });
            }
            console.log("极速模式流处理完成");
            onChunk("", true); // 发送完成信号
            break;
          }
        }
        
        console.log("极速模式请求完成");
        return fullResponse.join('');
      } catch (error) {
        controller.abort(); // 出错时中止请求
        console.error("极速模式流式请求出错:", error);
        throw error;
      }
    } else {
      // 非流式API，使用GET请求
      const response = await axios.get(
        "http://*************:9000/api/codeMoss/processAudio",
        {
          params: {
            problem: text,
            isAccurate: false, // 极速模式
            apiKey: apiKey // 添加API密钥作为URL参数，以防Header无法使用
          },
          headers: {
            'Authorization': apiKey
          }
        }
      );
      
      // 处理完整响应
      if (response.data && typeof response.data === 'string') {
        return response.data;
      } else {
        return "未能获取有效回复";
      }
    }
  } catch (error: any) {
    console.error("极速模式请求错误:", error);
    if (error.response?.status === 401) {
      return "认证失败，请检查API密钥";
    }
    if (error.response?.status === 429) {
      return "API调用次数已达上限，请稍后再试";
    }
    return `处理请求时出错: ${error.message}`;
  }
}

/**
 * 处理来自语音识别的文本，使用精确模式（如GPT-4o）
 * @param text 语音识别文本
 * @param onChunk 流式响应的回调函数，接收每个响应片段
 * @returns 返回Promise，处理完成时resolve
 */
export async function processAccurateVoiceText(
  text: string, 
  onChunk?: (chunk: string, done: boolean) => void
): Promise<string> {
  try {
    // 获取API密钥
    const apiKey = store.get("openaiApiKey") as string;
    
    if (!apiKey) {
      throw new Error("API密钥未设置");
    }
    console.log("处理精确模式文本:", text);

    // 如果提供了onChunk回调，则使用服务端事件流(SSE)
    if (onChunk) {
      // 使用SSE流式API
      const controller = new AbortController();
      const fullResponse: string[] = [];
      
      try {
        console.log("开始发送精确模式POST请求...");
        const response = await fetch("http://*************:9000/api/codeMoss/processAudio", {
          method: 'POST',
          headers: {
            'Authorization': apiKey,
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'text/event-stream'
          },
          body: new URLSearchParams({
            'problem': text,
            'isAccurate': 'true' // 精确模式
          }),
          signal: controller.signal
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        console.log("精确模式请求成功，开始处理响应流...");
        
        // 创建响应流读取器
        const reader = response.body?.getReader();
        const decoder = new TextDecoder();
        
        if (!reader) {
          throw new Error('无法获取响应流');
        }
        
        let buffer = '';
        let done = false;
        
        while (!done) {
          const { value, done: doneReading } = await reader.read();
          done = doneReading;
          
          if (value) {
            // 解码字节流为文本
            const chunk = decoder.decode(value, { stream: !done });
            buffer += chunk;
            
            // 处理SSE格式的数据 - 参考 audio-test.html 的实现
            const eventChunks = buffer.split('\n\n');
            buffer = eventChunks.pop() || '';

            eventChunks.forEach(chunk => {
              if (!chunk.trim()) return;

              console.log('接收到数据块:', chunk);

              // 解析SSE格式 - 参考 audio-test.html
              const lines = chunk.split("\n");
              let data = "";

              lines.forEach(line => {
                if (line.startsWith("data:")) {
                  data = line.substring(5).trim();
                  if (data) {
                    try {
                      const item = JSON.parse(data); // 尝试解析JSON，确保数据格式正确
                      if (item.choices && item.choices.length > 0) {
                        data = item.choices[0].delta.content; // 获取第一个选择的文本
                      } else {
                        data = ''; // 如果没有有效数据，清空
                      }
                      if (data) {
                        console.log("处理数据:", data.substring(0, 50) + (data.length > 50 ? "..." : ""));
                        fullResponse.push(data);
                        onChunk(data, false);
                      }
                    } catch (e) {
                      fullResponse.push(data);
                      onChunk(data, false);
                    }
                  }
                }
              });
            });
          }
          
          if (done) {
            // 处理缓冲区中剩余的数据 - 参考 audio-test.html 的实现
            if (buffer.trim() !== '') {
              const lines = buffer.split("\n");
              let data = "";

              lines.forEach(line => {
                if (line.startsWith("data:")) {
                  data = line.substring(5).trim();
                  if (data) {
                    try {
                      const item = JSON.parse(data); // 尝试解析JSON，确保数据格式正确
                      if (item.choices && item.choices.length > 0) {
                        data = item.choices[0].delta.content; // 获取第一个选择的文本
                      } else {
                        data = ''; // 如果没有有效数据，清空
                      }
                      if (data) {
                        console.log("处理最终数据块:", data.substring(0, 50) + (data.length > 50 ? "..." : ""));
                        fullResponse.push(data);
                        onChunk(data, false);
                      }
                    } catch (e) {
                      // 如果不是JSON格式，直接使用原始数据
                      console.log("最终非JSON数据，直接使用:", data.substring(0, 50) + (data.length > 50 ? "..." : ""));
                      fullResponse.push(data);
                      onChunk(data, false);
                    }
                  }
                }
              });
            }
            console.log("精确模式流处理完成");
            onChunk("", true); // 发送完成信号
            break;
          }
        }
        
        console.log("精确模式请求完成");
        return fullResponse.join('');
      } catch (error) {
        controller.abort(); // 出错时中止请求
        console.error("精确模式流式请求出错:", error);
        throw error;
      }
    } else {
      // 非流式API，使用GET请求
      const response = await axios.get(
        "http://*************:9000/api/codeMoss/processAudio",
        {
          params: {
            problem: text,
            isAccurate: true, // 精确模式
            apiKey: apiKey // 添加API密钥作为URL参数，以防Header无法使用
          },
          headers: {
            'Authorization': apiKey
          }
        }
      );
      
      // 处理完整响应
      if (response.data && typeof response.data === 'string') {
        return response.data;
      } else {
        return "未能获取有效回复";
      }
    }
  } catch (error: any) {
    console.error("精确模式请求错误:", error);
    if (error.response?.status === 401) {
      return "认证失败，请检查API密钥";
    }
    if (error.response?.status === 429) {
      return "API调用次数已达上限，请稍后再试";
    }
    return `处理请求时出错: ${error.message}`;
  }
}

/**
 * 解密来自后端的 AES 加密数据
 * @param encryptedData Base64 encoded encrypted string
 * @returns Decrypted string
 */
export function decryptData(encryptedData: any): any {
  try {
    // 使用与 Java 端相同的密钥
    const secretKey = "3257871232342431"; // 与后端使用相同的密钥
    
    // 将 Base64 字符串解码为二进制数据
    const encryptedBuffer = Buffer.from(encryptedData, 'base64');
    
    // 创建解密器，使用 ECB 模式和 PKCS7 填充（Java 默认填充）
    const decipher = crypto.createDecipheriv('aes-128-ecb', Buffer.from(secretKey.substring(0, 16)), null);
    
    // 解密数据
    let decrypted = decipher.update(encryptedBuffer);
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    
    // 将二进制数据转换为字符串
    return decrypted.toString('utf8');
  } catch (error: any) {
    console.error("Error decrypting data:", error);
    throw new Error(`Failed to decrypt data: ${error.message}`);
  }
}

/**
 * 使用 deepseek-chat 模型生成解决方案
 */
export async function generateSolutionResponsesByDpChat(
  problem: string
): Promise<any> {
  try {
    const storedApiKey = store.get("openaiApiKey") as string
    if (!storedApiKey) {
      throw new Error("OpenAI API key not set")
    }

    const response = await axios.get(
      "http://*************:9000/api/codeMoss/generateSolutionByDeepSeekChat",
      {
        params: {
          problem: encodeURIComponent(problem)
        },
        headers: {
          'Authorization': storedApiKey
        }
      }
    );
    const content = response.data.choices[0].message.content
    try {
      return JSON.parse(content)
    } catch (error) {
      console.error("Failed to parse JSON, attempting repair:", error);
      const repairedJson = jsonrepair(content);
      return JSON.parse(repairedJson);
    }
  } catch (error: any) {
    console.error("Error details:", error);
    if (error.response?.status === 401) {
      throw new Error("Please close this window and re-enter a valid API key.");
    }
    if (error.response?.status === 429) {
      throw new Error("API Key out of credits. Please refill your API credits and try again.");
    }
    throw new Error(`Error generating solutions: ${error.message}`);
  }
}


export async function generateSolutionByModel(
  problem: string
): Promise<any> {
  try {
    const storedApiKey = store.get("openaiApiKey") as string
    let model = store.get("curModel") as string
    let codeLanguage = store.get("curCodeLanguage") as string
    if (!storedApiKey) {
      throw new Error("OpenAI API key not set")
    }


    if (!model) {
      model = "deepseek-v3"
    }

    if (!codeLanguage) {
      codeLanguage = "Java"
    }

    const response = await axios.get(
      "http://*************:9000/api/codeMoss/generateSolutionByModelV6",
      {
        params: {
          problem: encodeURIComponent(problem),
          model: model,
          codeLanguage: codeLanguage
        },
        headers: {
          'Authorization': storedApiKey
        }
      }
    );
    const decodeResponse = decryptData(response.data)

    const parsedResponse = JSON.parse(decodeResponse);

    const content = parsedResponse.choices[0].message.content
    
    try {
      return JSON.parse(content)
    } catch (error) {
      console.error("Failed to parse JSON, attempting repair:", error);
      const repairedJson = jsonrepair(content);
      return JSON.parse(repairedJson);
    }
  } catch (error: any) {
    console.error("Error details:", error);
    if (error.response?.status === 401) {
      throw new Error("Please close this window and re-enter a valid API key.");
    }
    if (error.response?.status === 429) {
      throw new Error("API Key out of credits. Please refill your API credits and try again.");
    }
    throw new Error(`Error generating solutions: ${error.message}`);
  }
}

export async function generateScreenshotSolutionResponseNew(
  imagePaths: Array<string>,
): Promise<any> {
  try {
    const storedApiKey = store.get("openaiApiKey") as string
    let model = store.get("curModel") as string
    let codeLanguage = store.get("curCodeLanguage") as string
    if (!storedApiKey) {
      throw new Error("OpenAI API key not set")
    }

    if (!model) {
      model = "deepseek-v3"
    }

    if (!codeLanguage) {
      codeLanguage = "Java"
    }

    // 创建 FormData 对象
    const formData = new FormData();
    
    // 处理所有图片
    for (const imagePath of imagePaths) {
      // 检查文件是否存在
      if (!fs.existsSync(imagePath)) {
        throw new Error(`File not found: ${imagePath}`);
      }

      // 读取文件
      const fileBuffer = fs.readFileSync(imagePath);
      
      // 添加文件到 FormData，使用文件的原始名称
      const fileName = path.basename(imagePath);
      formData.append('files', new Blob([fileBuffer]), fileName);
    }
    
    formData.append('model', model);
    formData.append('codeLanguage', codeLanguage);

    const url = "http://*************:9000/api/codeMoss/recognizeAndGenerateSolutionByModelV6";

    const response = await axios.post(
      url,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
          Authorization: `${storedApiKey}`
        }
      }
    )
    
    const decodeResponse = decryptData(response.data)
    try {
      const parsedResponse = JSON.parse(decodeResponse);
      const content = parsedResponse.choices[0].message.content
      try {
        return JSON.parse(content)
      } catch (error) {
        console.error("Failed to parse JSON content, attempting repair:", error);
        const repairedJson = jsonrepair(content);
        return JSON.parse(repairedJson);
      }
    } catch (error) {
      console.error("Failed to parse decoded response, attempting repair:", error);
      const repairedJson = jsonrepair(decodeResponse);
      const parsedResponse = JSON.parse(repairedJson);
      const content = parsedResponse.choices[0].message.content
      try {
        return JSON.parse(content)
      } catch (innerError) {
        console.error("Failed to parse JSON content, attempting repair:", innerError);
        const repairedContent = jsonrepair(content);
        return JSON.parse(repairedContent);
      }
    }
  } catch (error: any) {
    if (error.response?.status === 401) {
      throw new Error(
        "Please close this window and re-enter a valid API key."
      )
    }
    if (error.response?.status === 429) {
      throw new Error(
        "API Key out of credits. Please refill your API credits and try again."
      )
    }
    console.error("Error details:", error)
    throw new Error(`Error generating solutions: ${error.message}`)
  }
}

export async function generateScreenshotSolutionResponse(
  imagePath: String,
  isR1: Boolean
): Promise<any> {
  try {
    const storedApiKey = store.get("openaiApiKey") as string
    if (!storedApiKey) {
      throw new Error("OpenAI API key not set")
    }

    // 检查文件是否存在
    if (!fs.existsSync(imagePath)) {
      throw new Error(`File not found: ${imagePath}`);
    }

    // 读取文件
    const fileBuffer = fs.readFileSync(imagePath);
    
    // 创建 FormData 对象
    const formData = new FormData();
    
    // 添加文件到 FormData，使用文件的原始名称
    const fileName = path.basename(imagePath);
    formData.append('file', new Blob([fileBuffer]), fileName);

     // 验证 FormData 内容
     for (let pair of formData.entries()) {
      console.log(pair[0] + ': ' + pair[1]);
    }

    const url = isR1 ? "http://*************:9000/api/codeMoss/recognizeAndGenerateSolutionByDeepSeekR1" : 
    "http://*************:9000/api/codeMoss/recognizeAndGenerateSolutionByDeepSeekChat";

    const response = await axios.post(
      url,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
          Authorization: `${storedApiKey}`
        }
      }
    )
    console.log("respons", response)
    console.log(response.data.choices[0])
    const content = response.data.choices[0].message.content
    
    try {
      return JSON.parse(content)
    } catch (error) {
      console.error("Failed to parse JSON, attempting repair:", error);
      const repairedJson = jsonrepair(content);
      return JSON.parse(repairedJson);
    }
  } catch (error: any) {
    if (error.response?.status === 401) {
      throw new Error(
        "Please close this window and re-enter a valid API key."
      )
    }
    if (error.response?.status === 429) {
      throw new Error(
        "API Key out of credits. Please refill your API credits and try again."
      )
    }
    console.error("Error details:", error)
    throw new Error(`Error generating solutions: ${error.message}`)
  }
}

export async function debugSolutionResponses(
  imageDataList: string[],
  problemInfo: ProblemInfo
): Promise<DebugSolutionResponse> {
  // Process images for inclusion in prompt
  const imageContents = imageDataList.map((imageData) => ({
    type: "image_url",
    image_url: {
      url: `data:image/jpeg;base64,${imageData}`
    }
  }))

  // Build the prompt with error handling
  const problemStatement =
    problemInfo.problem_statement ?? "Problem statement not available"

  const inputFormatDescription =
    problemInfo.input_format?.description ??
    "Input format description not available"

  const inputParameters = problemInfo.input_format?.parameters
    ? problemInfo.input_format.parameters
        .map(
          (p) => `- ${p.name}: ${p.type}${p.subtype ? ` of ${p.subtype}` : ""}`
        )
        .join(" ")
    : "Input parameters not available"

  const outputFormatDescription =
    problemInfo.output_format?.description ??
    "Output format description not available"

  const returns = problemInfo.output_format?.type
    ? `Returns: ${problemInfo.output_format.type}${
        problemInfo.output_format.subtype
          ? ` of ${problemInfo.output_format.subtype}`
          : ""
      }`
    : "Returns: Output type not available"

  const constraints = problemInfo.constraints
    ? problemInfo.constraints
        .map((c) => {
          let constraintStr = `- ${c.description}`
          if (c.range) {
            constraintStr += ` (${c.parameter}: ${c.range.min} to ${c.range.max})`
          }
          return constraintStr
        })
        .join(" ")
    : "Constraints not available"

  let exampleTestCases = "Test cases not available"
  if (problemInfo.test_cases) {
    try {
      exampleTestCases = JSON.stringify(problemInfo.test_cases, null, 2)
    } catch {
      exampleTestCases = "Test cases not available"
    }
  }

  // Construct the debug prompt
  const debugPrompt = `
Given the following coding problem and its visual representation:

Problem Statement:
${problemStatement}

Input Format:
${inputFormatDescription}
Parameters:
${inputParameters}

Output Format:
${outputFormatDescription}
${returns}

Constraints:
${constraints}

Example Test Cases:
${exampleTestCases}

First extract and analyze the code shown in the image. Then create an improved version while maintaining the same general approach and structure. The old code you save should ONLY be the exact code that you see on the screen, regardless of any optimizations or changes you make. Make all your changes in the new_code field. You should use the image that has the most recent, longest version of the code, making sure to combine multiple images if necessary.
Focus on keeping the solution syntactically similar but with optimizations and INLINE comments ONLY ON lines of code that were changed. Make sure there are no extra line breaks and all the code that is unchanged is in the same line as it was in the original code.

IMPORTANT FORMATTING NOTES:
1. Use actual line breaks (press enter for new lines) in both old_code and new_code
2. Maintain proper indentation with spaces in both code blocks
3. Add inline comments ONLY on changed lines in new_code
4. The entire response must be valid JSON that can be parsed`

  // Construct the messages array
  const messages = [
    {
      role: "user",
      content: [
        {
          type: "text",
          text: debugPrompt
        },
        ...imageContents
      ]
    }
  ]

  // Define the function schema
  const functions = [
    {
      name: "provide_solution",
      description:
        "Debug based on the problem and provide a solution to the coding problem",
      parameters: {
        type: "object",
        properties: {
          thoughts: {
            type: "array",
            items: { type: "string" },
            description:
              "Share up to 3 key thoughts as you work through solving this problem for the first time. Write in the voice of someone actively reasoning through their approach, using natural pauses, uncertainty, and casual language that shows real-time problem solving. Each thought must be max 100 characters and be full sentences that don't sound choppy when read aloud.",
            maxItems: 3,
            thoughtGuidelines: [
              "First thought should capture that initial moment of recognition - connecting it to something familiar or identifying the core challenge. Include verbal cues like 'hmm' or 'this reminds me of' that show active thinking.",
              "Second thought must explore your emerging strategy and MUST explicitly name the algorithm or data structure being considered. Show both knowledge and uncertainty - like 'I could probably use a heap here, but I'm worried about...'",
              "Third thought should show satisfaction at having a direction while acknowledging you still need to work out specifics - like 'Okay, I think I see how this could work...'"
            ]
          },
          old_code: {
            type: "string",
            description:
              "The exact code implementation found in the image. There should be no additional lines of code added, this should only contain the code that is visible from the images, regardless of correctness or any fixes you can make. Include every line of code that are visible in the image.  You should use the image that has the most recent, longest version of the code, making sure to combine multiple images if necessary."
          },
          new_code: {
            type: "string",
            description:
              "The improved code implementation with in-line comments only on lines of code that were changed"
          },
          time_complexity: {
            type: "string",
            description:
              "Time complexity with explanation, format as 'O(_) because _.' Importantly, if there were slight optimizations in the complexity that don't affect the overall complexity, MENTION THEM."
          },
          space_complexity: {
            type: "string",
            description:
              "Space complexity with explanation, format as 'O(_) because _' Importantly, if there were slight optimizations in the complexity that don't affect the overall complexity, MENTION THEM."
          }
        },
        required: [
          "thoughts",
          "old_code",
          "new_code",
          "time_complexity",
          "space_complexity"
        ]
      }
    }
  ]

  // Prepare the payload for the API call
  const payload = {
    model: "gpt-4o",
    messages: messages,
    max_tokens: 4000,
    temperature: 0,
    functions: functions,
    function_call: { name: "provide_solution" }
  }

  try {
    // Send the request to the OpenAI API
    const storedApiKey = store.get("openaiApiKey") as string
    if (!storedApiKey) {
      throw new Error("OpenAI API key not set")
    }

    const response = await axios.post(
      "http://api.aigogo.top/v1/chat/completions",
      payload,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${storedApiKey}`
        }
      }
    )

    // Extract the function call arguments from the response
    const functionCallArguments =
      response.data.choices[0].message.function_call.arguments

    // Parse and return the response
    try {
      return JSON.parse(functionCallArguments) as DebugSolutionResponse
    } catch (error) {
      console.error("Failed to parse function call arguments, attempting repair:", error);
      const repairedJson = jsonrepair(functionCallArguments);
      return JSON.parse(repairedJson) as DebugSolutionResponse;
    }
  } catch (error: any) {
    if (error.response?.status === 404) {
      throw new Error(
        "API endpoint not found. Please check the model name and URL."
      )
    } else if (error.response?.status === 401) {
      throw new Error(
        "Please close this window and re-enter a valid Open AI API key."
      )
    } else if (error.response?.status === 429) {
      throw new Error(
        "API Key out of credits. Please refill your OpenAI API credits and try again."
      )
    } else {
      throw new Error(
        `OpenAI API error: ${
          error.response?.data?.error?.message || error.message
        }`
      )
    }
  }
}

/**
 * 检查麦克风按钮是否应该显示
 * @returns Promise<boolean> 是否显示麦克风按钮
 */
export async function shouldShowMicrophoneButton(): Promise<boolean> {
  try {
    const config = store.get('config');
    console.log("shouldShowMicrophoneButton:", config);
    if (config && config.success && config.data && config.data.voiceConfig) {
      const voiceConfig = config.data.voiceConfig;
      const shouldShow = voiceConfig.voiceEnabled;
      console.log('config status:', shouldShow, voiceConfig);
      return shouldShow;
    }

    console.log('未找到语音配置，麦克风按钮不显示');
    return false;
  } catch (error) {
    console.error('检查麦克风按钮显示状态失败:', error);
    return false;
  }
}
