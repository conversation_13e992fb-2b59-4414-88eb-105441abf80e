/**
 * SystemAudioManager.ts
 * 系统音频管理器 - 负责捕获系统音频并发送到ASR服务
 */

import { ChildProcess, exec, spawn } from 'child_process';
import { app, BrowserWindow } from 'electron';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { systemASRManager } from './SystemASRManager';

const execAsync = promisify(exec);

export class SystemAudioManager {
  private recordingProcess: ChildProcess | null = null;
  private mainWindow: BrowserWindow | null = null;
  private isRecording: boolean = false;
  private outputPath: string = '';
  private static instance: SystemAudioManager | null = null;
  private audioConfig = {
    sampleRate: 16000,
    channels: 1,
    bitsPerSample: 16
  };
  private streamClosed: boolean = false;

  constructor() {
    this.setupOutputPath();
    this.startHealthCheck();
    this.startPerformanceMonitoring();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): SystemAudioManager {
    if (!SystemAudioManager.instance) {
      SystemAudioManager.instance = new SystemAudioManager();
    }
    return SystemAudioManager.instance;
  }

  /**
   * 设置主窗口引用
   */
  public setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
    systemASRManager.setMainWindow(window);
    console.log('SystemAudioManager: 主窗口已设置');
  }

  /**
   * 设置输出文件路径
   */
  private setupOutputPath(): void {
    const userDataPath = app.getPath('userData');
    const tempDir = path.join(userDataPath, 'temp');
    
    // 确保临时目录存在
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    this.outputPath = path.join(tempDir, 'system-audio.pcm');
  }

  /**
   * 启动系统音频捕获
   */
  public async startCapturing(): Promise<{ success: boolean, error?: string }> {
    console.log('SystemAudioManager: 开始启动系统音频捕获');

    if (this.isRecording) {
      console.log('SystemAudioManager: 系统音频已在录制中');
      return { success: true };
    }

    // 记录录音开始时间
    this.performanceMetrics.recordingStartTime = Date.now();

    try {
      // 第一步：启动SystemASR服务
      console.log('SystemAudioManager: 第一步 - 启动SystemASR服务');
      
      const asrResult = await systemASRManager.startASRSession();
      if (!asrResult.success) {
        console.error('SystemAudioManager: SystemASR服务启动失败:', asrResult.error);
        return { success: false, error: `SystemASR服务启动失败: ${asrResult.error}` };
      }
      
      console.log('SystemAudioManager: SystemASR服务启动成功');
      
      // 第二步：启动系统音频捕获
      console.log('SystemAudioManager: 第二步 - 启动系统音频捕获');
      
      // 确保之前的进程已停止
      if (this.recordingProcess) {
        console.log('SystemAudioManager: 停止现有的录制进程');
        try {
          this.recordingProcess.kill('SIGTERM');
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (e) {
          console.warn('SystemAudioManager: 停止现有进程时出错:', e);
        }
        this.recordingProcess = null;
      }

      // 清理现有的输出文件
      if (fs.existsSync(this.outputPath)) {
        try {
          fs.unlinkSync(this.outputPath);
          console.log('SystemAudioManager: 已清理现有的输出文件');
        } catch (e) {
          console.warn('SystemAudioManager: 清理输出文件时出错:', e);
        }
      }

      // 确保输出目录存在
      const outputDir = path.dirname(this.outputPath);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
        console.log(`SystemAudioManager: 创建输出目录: ${outputDir}`);
      }

      // 根据平台启动相应的音频捕获
      let command: string;
      let args: string[];

      if (process.platform === 'darwin') {
        // macOS
        const baseDir = app.isPackaged ? process.resourcesPath : app.getAppPath();
        const binaryPath = path.join(baseDir, 'bin', 'macos', 'system-audio-capture');
        command = binaryPath;
        args = ['--output', this.outputPath];
        
        console.log(`SystemAudioManager: macOS音频捕获命令: ${command} ${args.join(' ')}`);
      } else if (process.platform === 'win32') {
        // Windows
        const baseDir = app.isPackaged ? process.resourcesPath : app.getAppPath();
        const binaryPath = path.join(baseDir, 'bin', 'windows', 'audio_capture_cli.exe');
        command = binaryPath;
        args = ['--output', this.outputPath];
        
        console.log(`SystemAudioManager: Windows音频捕获命令: ${command} ${args.join(' ')}`);
      } else {
        return { success: false, error: '不支持的操作系统平台' };
      }

      // 检查二进制文件是否存在
      if (!fs.existsSync(command)) {
        console.error(`SystemAudioManager: 音频捕获二进制文件不存在: ${command}`);
        return { success: false, error: `音频捕获工具不存在: ${command}` };
      }

      // 启动音频捕获进程
      return new Promise((resolve) => {
        let timeoutId: NodeJS.Timeout | null = null;
        let recordingStarted = false;
        let retryCount = 0;
        const maxRetries = 3;
        const retryDelay = 3000; // 3秒后重试
        
        const startCapturingProcess = () => {
          console.log(`SystemAudioManager: 启动音频捕获进程... (尝试 ${retryCount + 1}/${maxRetries + 1})`);
          
          // 如果存在上一个进程，确保先终止
          if (this.recordingProcess) {
            try {
              this.recordingProcess.kill("SIGTERM");
              this.recordingProcess = null;
            } catch (err) {
              console.warn("SystemAudioManager: 终止上一个进程时出错:", err);
            }
          }
          
          this.recordingProcess = spawn(command, args, {
            stdio: ['pipe', 'pipe', 'pipe'],
            detached: false
          });

          const onRecordingStart = () => {
            if (timeoutId) clearTimeout(timeoutId);
            recordingStarted = true;
            this.isRecording = true;
            this.streamClosed = false;
            
            // 确保输出文件已经创建并且位置信息已正确重置
            try {
              // 确保输出文件存在后再开始监控
              if (fs.existsSync(this.outputPath)) {
                const stats = fs.statSync(this.outputPath);
                console.log(`SystemAudioManager: 输出文件大小: ${stats.size}字节`);
              } else {
                console.log(`SystemAudioManager: 输出文件还未创建`);
              }
            } catch (error) {
              console.warn(`SystemAudioManager: 检查输出文件时出错:`, error);
            }
            
            // 通知渲染进程
            if (this.mainWindow && !this.mainWindow.isDestroyed()) {
              this.mainWindow.webContents.send('system-audio:status', { recording: true });
            }
            
            // 启动音频文件监控
            this.startAudioFileMonitoring();
            
            resolve({ success: true });
          };

          // 设置超时处理
          timeoutId = setTimeout(() => {
            if (!recordingStarted) {
              console.error(`SystemAudioManager: 启动录音超时 (尝试 ${retryCount + 1}/${maxRetries + 1})`);
              
              if (this.recordingProcess) {
                console.log("SystemAudioManager: 尝试终止超时的录音进程...");
                this.recordingProcess.kill("SIGINT"); // 尝试优雅地停止
                
                // 添加额外延时和 SIGKILL，如果 SIGINT 不起作用
                setTimeout(() => {
                  if (this.recordingProcess && !this.recordingProcess.killed) {
                    this.recordingProcess.kill("SIGKILL");
                    this.recordingProcess = null;
                  }
                  
                  // 是否需要再次尝试
                  if (retryCount < maxRetries) {
                    retryCount++;
                    console.log(`SystemAudioManager: 将在 ${retryDelay/1000}秒后重试启动录音...`);
                    setTimeout(startCapturingProcess, retryDelay);
                  } else {
                    console.error(`SystemAudioManager: 达到最大重试次数 (${maxRetries + 1})，录音启动失败`);
                    resolve({ success: false, error: `启动录音失败，已尝试 ${maxRetries + 1} 次` });
                  }
                }, 1000);
              } else {
                // 如果进程已不存在，直接判断是否需要重试
                if (retryCount < maxRetries) {
                  retryCount++;
                  console.log(`SystemAudioManager: 将在 ${retryDelay/1000}秒后重试启动录音...`);
                  setTimeout(startCapturingProcess, retryDelay);
                } else {
                  console.error(`SystemAudioManager: 达到最大重试次数 (${maxRetries + 1})，录音启动失败`);
                  resolve({ success: false, error: `启动录音失败，已尝试 ${maxRetries + 1} 次` });
                }
              }
            }
          }, 10000); // 10秒超时

          // 设置进程事件监听器
          this.recordingProcess.on('spawn', () => {
            console.log('SystemAudioManager: 音频捕获进程已启动');
          });

          this.recordingProcess.stdout.on("data", (data) => {
            const output = data.toString();
            console.log('SystemAudioManager: stdout:', output);
            
            if (process.platform === 'darwin') {
              this.handleMacOSOutput(output, onRecordingStart);
            } else if (process.platform === 'win32') {
              this.handleWindowsOutput(output, onRecordingStart);
            }
          });

          this.recordingProcess.stderr.on("data", (data) => {
            const output = data.toString();
            console.log('SystemAudioManager: stderr:', output);
            
            // 某些错误可能不是致命的，只记录但不停止
            if (output.includes('ERROR') || output.includes('FATAL')) {
              console.error('SystemAudioManager: 音频捕获严重错误:', output);
            }
          });

          this.recordingProcess.on('error', (error) => {
            if (timeoutId) clearTimeout(timeoutId);
            console.error('SystemAudioManager: 音频捕获进程错误:', error);
            this.isRecording = false;
            this.streamClosed = true;
            
            if (this.mainWindow && !this.mainWindow.isDestroyed()) {
              this.mainWindow.webContents.send('system-audio:error', {
                error: `音频捕获进程错误: ${error.message}`
              });
            }
            
            resolve({ success: false, error: `音频捕获进程错误: ${error.message}` });
          });

          this.recordingProcess.on('exit', (code, signal) => {
            if (timeoutId) clearTimeout(timeoutId);
            console.log(`SystemAudioManager: 音频捕获进程退出，代码: ${code}, 信号: ${signal}`);
            this.isRecording = false;
            this.streamClosed = true;
            this.recordingProcess = null;
            
            if (this.mainWindow && !this.mainWindow.isDestroyed()) {
              this.mainWindow.webContents.send('system-audio:status', { recording: false });
            }
            
            if (code !== 0 && code !== null && !recordingStarted) {
              resolve({ success: false, error: `音频捕获进程异常退出，代码: ${code}` });
            }
          });
        };
        
        // 启动捕获进程
        startCapturingProcess();
      });
    } catch (error) {
      console.error('SystemAudioManager: 启动系统音频捕获时出错:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '启动系统音频捕获失败'
      };
    }
  }

  private handleMacOSOutput(output: string, onRecordingStart: Function | null): void {
    // 处理 JSON 响应行
    const responseLines = output.split("\n").filter(line => line !== "");
    if (responseLines.length === 0) return;

    try {
      // 尝试解析第一行为 JSON
      const response = JSON.parse(responseLines[0]);

      if (response.code === "RECORDING_STARTED") {
        console.log(`SystemAudioManager: 录音已开始，文件保存在: ${this.outputPath}`);
        
        // 提取音频参数
        this.audioConfig.sampleRate = Number(response.sampleRate) || 16000;
        this.audioConfig.channels = Number(response.channels) || 1;
        this.audioConfig.bitsPerSample = 16;
        
        console.log(`macOS音频参数: 采样率=${this.audioConfig.sampleRate}, 通道数=${this.audioConfig.channels}, 位数=${this.audioConfig.bitsPerSample}`);
        
        if (onRecordingStart) onRecordingStart();
      } else if (response.code === "RECORDING_STOPPED") {
        console.log("SystemAudioManager: 录音已停止。");
      } else if (response.code?.includes("FAILED") || response.code?.includes("ERROR")) {
        let errorMessage = `SystemAudioManager: 录音错误: ${response.code}`;
        if (response.error) {
          errorMessage += ` - ${response.error}`;
        }
        console.error(errorMessage);
        
        // 添加重试逻辑
        this.retryStartRecording(onRecordingStart);
      } else {
        // 对于任何非RECORDING_STARTED的响应，也启动重试逻辑
        console.log(`SystemAudioManager: 收到未预期的响应代码: ${response.code}`);
        this.retryStartRecording(onRecordingStart);
      }
    } catch (e) {
      // 非 JSON 输出，降级处理
      if (output.includes('Audio capture started') || output.includes('Recording')) {
        console.log('SystemAudioManager: macOS音频捕获已开始');
        
        // 如果无法从JSON中提取参数，使用默认值
        this.audioConfig.sampleRate = 16000;
        this.audioConfig.channels = 1;
        this.audioConfig.bitsPerSample = 16;
        
        if (onRecordingStart) onRecordingStart();
      } else if (output.includes('Error') || output.includes('Failed')) {
        console.error('SystemAudioManager: macOS音频捕获错误:', output);
        // 解析错误时也启动重试逻辑
        this.retryStartRecording(onRecordingStart);
      }
    }
  }

  // 添加重试启动方法
  private retryAttempts = 0;
  private maxRetryAttempts = 5;
  private retryDelayMs = 200;
  private retryTimeoutId: NodeJS.Timeout | null = null;

  private retryStartRecording(onRecordingStart: Function | null): void {
    // 如果已经达到最大重试次数，则不再重试
    if (this.retryAttempts >= this.maxRetryAttempts) {
      console.error(`SystemAudioManager: 已达到最大重试次数 (${this.maxRetryAttempts})，放弃启动录音`);
      this.retryAttempts = 0; // 重置重试计数器，以便下次可以重试
      return;
    }

    // 清除可能存在的重试计时器
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }

    // 增加重试计数
    this.retryAttempts++;

    console.log(`SystemAudioManager: 将在 ${this.retryDelayMs}ms 后尝试重新启动录音 (尝试 ${this.retryAttempts}/${this.maxRetryAttempts})`);

    // 设置重试计时器
    this.retryTimeoutId = setTimeout(async () => {
      console.log(`SystemAudioManager: 正在重新启动录音 (尝试 ${this.retryAttempts}/${this.maxRetryAttempts})...`);
      
      try {
        // 先尝试停止现有的录音进程
        if (this.recordingProcess) {
          try {
            this.recordingProcess.kill('SIGTERM');
            this.recordingProcess = null;
            await new Promise(resolve => setTimeout(resolve, 500)); // 等待进程彻底终止
          } catch (e) {
            console.warn('SystemAudioManager: 重试时停止现有进程出错:', e);
          }
        }

        // 根据平台选择不同的重启方式
        const baseDir = app.isPackaged ? process.resourcesPath : app.getAppPath();
        let binaryPath: string;
        let args: string[];
        
        if (process.platform === 'darwin') {
          // macOS
          binaryPath = path.join(baseDir, 'bin', 'macos', 'system-audio-capture');
          args = ['--output', this.outputPath];
        } else if (process.platform === 'win32') {
          // Windows
          binaryPath = path.join(baseDir, 'bin', 'windows', 'audio_capture_cli.exe');
          args = ['--output', this.outputPath];
        } else {
          // 不支持的平台
          console.error(`SystemAudioManager: 不支持的平台 ${process.platform}，无法重启录音`);
          return;
        }
        
        // 检查二进制文件是否存在
        if (!fs.existsSync(binaryPath)) {
          console.error(`SystemAudioManager: 音频捕获二进制文件不存在: ${binaryPath}`);
          return;
        }
        
        // 启动录音进程
        this.recordingProcess = spawn(binaryPath, args, {
          stdio: ['pipe', 'pipe', 'pipe'],
          detached: false
        });

        // 设置新进程的事件处理
        this.recordingProcess.stdout.on("data", (data) => {
          const output = data.toString();
          console.log(`SystemAudioManager (重试): stdout: ${output}`);
          
          // 根据平台处理输出
          if (process.platform === 'darwin') {
            this.handleMacOSOutput(output, onRecordingStart);
          } else if (process.platform === 'win32') {
            this.handleWindowsOutput(output, onRecordingStart);
          }
        });

        this.recordingProcess.stderr.on("data", (data) => {
          const output = data.toString();
          console.log(`SystemAudioManager (重试): stderr: ${output}`);
        });

        this.recordingProcess.on('error', (error) => {
          console.error(`SystemAudioManager (重试): 音频捕获进程错误: ${error}`);
        });

        this.recordingProcess.on('exit', (code, signal) => {
          console.log(`SystemAudioManager (重试): 音频捕获进程退出，代码: ${code}, 信号: ${signal}`);
        });

      } catch (error) {
        console.error('SystemAudioManager: 重试启动录音时出错:', error);
      }
    }, this.retryDelayMs);
  }

  /**
   * 处理Windows平台输出
   */
  private handleWindowsOutput(output: string, onRecordingStart: Function | null): void {
    // 尝试从输出中解析音频参数
    const audioParamsMatch = output.match(/Audio parameters: \{(.+)\}/);
    if (audioParamsMatch?.[1]) {
      try {
        // 在解析时手动添加大括号
        const params = JSON.parse(`{${audioParamsMatch[1]}}`);

        // 提取需要的音频参数并存储，确保类型为 number
        this.audioConfig.sampleRate = Number(params.samplesPerSec);
        this.audioConfig.channels = Number(params.channels);
        this.audioConfig.bitsPerSample = Number(params.bitsPerSample);
        
        console.log(`Windows音频参数: 采样率=${this.audioConfig.sampleRate}, 通道数=${this.audioConfig.channels}, 位数=${this.audioConfig.bitsPerSample}`);
        
        console.log("SystemAudioManager: 检测到音频参数:", JSON.stringify(params));
        console.log("SystemAudioManager: Windows录音已开始");
        if (onRecordingStart) onRecordingStart();
      } catch (e) {
        console.error("SystemAudioManager: 解析音频参数失败:", e);
        // 解析音频参数失败，启动重试逻辑
        this.retryStartRecording(onRecordingStart);
      }
    }
    
    // 降级处理
    if (output.includes('Recording started') || output.includes('Capturing')) {
      console.log('SystemAudioManager: Windows音频捕获已开始');
      
      // 如果无法从输出中提取参数，使用默认值
      if (!audioParamsMatch) {
        this.audioConfig.sampleRate = 16000;
        this.audioConfig.channels = 1;
        this.audioConfig.bitsPerSample = 16;
        console.log(`使用默认音频参数: 采样率=${this.audioConfig.sampleRate}, 通道数=${this.audioConfig.channels}, 位数=${this.audioConfig.bitsPerSample}`);
      }
      
      if (onRecordingStart) onRecordingStart();
    } else if (output.includes('Error') || output.includes('Failed')) {
      console.error('SystemAudioManager: Windows音频捕获错误:', output);
      // 遇到错误，启动重试逻辑
      this.retryStartRecording(onRecordingStart);
    } else if (!output.includes('Recording') && !output.includes('Capturing') && !audioParamsMatch) {
      // 没有检测到正确的启动信息，也启动重试逻辑
      console.log('SystemAudioManager: Windows音频捕获没有正确启动:', output);
      this.retryStartRecording(onRecordingStart);
    }
  }

  /**
   * 开始监控PCM文件并发送数据到ASR
   * 优化版本：安全的文件读取，避免读写竞争
   */
  private startAudioFileMonitoring(): void {
    console.log(`开始监控PCM文件 (安全版本): ${this.outputPath}`);
    console.log('采用安全读取机制，避免读写竞争');

    // 重置流关闭标志
    this.streamClosed = false;

    // 重置文件位置指针和状态管理
    this.positionManager.reset(); // 使用位置管理器
    let isReading = false;
    let readingTimer: NodeJS.Timeout | null = null;
    let incompleteFrameBuffer = Buffer.alloc(0); // 不完整帧缓存

    // 性能监控统计 - 增强版本
    const stats = {
      totalBytesRead: 0,
      totalReads: 0,
      successfulReads: 0,
      emptyReads: 0,
      errorReads: 0,
      lastLogTime: Date.now(),
      startTime: Date.now(),
      maxSingleRead: 0,
      avgReadSize: 0
    };

    // 添加音频特征跟踪，用于防止重复发送相似音频
    const audioFeatures = {
      lastChunksHashes: new Set<string>(), // 存储最近的音频哈希值
      maxStoredHashes: 20, // 最多存储20个哈希值
      silenceCounter: 0, // 静音计数器
      maxSilenceBeforeReset: 10, // 连续静音次数阈值
      isCurrentlySilent: false, // 当前是否为静音
    };
    
    // 确保ASR服务已连接
    if (!systemASRManager.isASRConnected()) {
      systemASRManager.startASRSession().catch(error => {
        console.error('启动ASR会话时出错:', error);
      });
    }

    // 安全的文件读取函数：避免读写竞争，支持不完整帧处理
    const readFileToEnd = async () => {
      // 如果流已关闭，清理定时器并停止监控
      if (this.streamClosed) {
        if (readingTimer) {
          clearTimeout(readingTimer);
          readingTimer = null;
        }

        // 清理不完整帧缓存
        incompleteFrameManager.clear();
        console.log('音频文件监控已停止，缓存已清理');
        return;
      }

      // 避免并发读取
      if (isReading) {
        console.log('跳过本次读取 - 上次读取仍在进行中');
        scheduleNextRead();
        return;
      }

      isReading = true;
      stats.totalReads++;

      try {
        // 安全读取音频数据
        const readResult = await this.safeReadAudioData(
          this.positionManager.currentPosition,
          this.positionManager.lastValidPosition,
          incompleteFrameBuffer
        );

        if (readResult.success && readResult.data && readResult.data.length > 0) {
          // 更新位置指针
          this.positionManager.updatePosition(readResult.newPosition, true);
          incompleteFrameBuffer = readResult.incompleteFrameBuffer;

          // 处理读取到的音频数据
          processAndSendAudio(readResult.data);

          // 更新统计信息
          stats.totalBytesRead += readResult.data.length;
          stats.successfulReads++;
          stats.avgReadSize = stats.totalBytesRead / stats.successfulReads;

          console.log(`安全读取并处理 ${readResult.data.length} 字节音频数据，新位置: ${this.positionManager.currentPosition}`);
        } else if (readResult.isEmpty) {
          stats.emptyReads++;
        } else if (readResult.error) {
          console.error('安全读取失败:', readResult.error);
          stats.errorReads++;

          // 错误恢复：使用位置管理器恢复
          const shouldReset = this.positionManager.recordError();
          if (shouldReset) {
            this.positionManager.recoverToLastValid();
          }
        }

        // 定期输出性能统计
        const now = Date.now();
        if (now - stats.lastLogTime >= 10000) { // 每10秒输出一次统计
          const duration = (now - stats.startTime) / 1000;
          const avgBytesPerSec = stats.totalBytesRead / duration;
          console.log(`音频监控统计 - 运行时间: ${duration.toFixed(1)}s, 总读取: ${stats.totalReads}, 成功: ${stats.successfulReads}, 空读: ${stats.emptyReads}, 错误: ${stats.errorReads}`);
          console.log(`数据统计 - 总字节: ${stats.totalBytesRead}, 平均速率: ${avgBytesPerSec.toFixed(0)} bytes/s, 不完整帧缓存: ${incompleteFrameBuffer.length}字节`);
          stats.lastLogTime = now;
        }

        isReading = false;
        scheduleNextRead();

      } catch (error) {
        console.error('监控音频文件时出错:', error);
        stats.errorReads++;
        isReading = false;

        // 错误恢复：使用位置管理器
        this.positionManager.recoverToLastValid();

        scheduleNextRead();
      }
    };

    // 调度下一次读取（固定200ms间隔）
    const scheduleNextRead = () => {
      if (!this.streamClosed) {
        readingTimer = setTimeout(readFileToEnd, 200); // 固定200ms间隔
      }
    };


    // 不完整帧管理器
    const incompleteFrameManager = {
      buffer: Buffer.alloc(0),
      maxBufferSize: 8192, // 最大缓存8KB不完整帧

      // 添加不完整帧数据
      addIncompleteFrame(data: Buffer): void {
        if (data.length === 0) return;

        this.buffer = Buffer.concat([this.buffer, data]);

        // 防止缓存过大
        if (this.buffer.length > this.maxBufferSize) {
          console.warn(`不完整帧缓存过大(${this.buffer.length}字节)，清理旧数据`);
          const keepSize = Math.floor(this.maxBufferSize / 2);
          this.buffer = this.buffer.subarray(-keepSize);
        }
      },

      // 尝试从缓存中提取完整帧
      extractCompleteFrames(frameSize: number): { completeFrames: Buffer; remainingIncomplete: Buffer } {
        if (this.buffer.length < frameSize) {
          return {
            completeFrames: Buffer.alloc(0),
            remainingIncomplete: this.buffer
          };
        }

        const completeFrameCount = Math.floor(this.buffer.length / frameSize);
        const completeFramesSize = completeFrameCount * frameSize;

        const completeFrames = this.buffer.subarray(0, completeFramesSize);
        const remainingIncomplete = this.buffer.subarray(completeFramesSize);

        this.buffer = remainingIncomplete;

        console.log(`从缓存提取: ${completeFrames.length}字节完整帧, 剩余${remainingIncomplete.length}字节不完整帧`);

        return {
          completeFrames,
          remainingIncomplete
        };
      },

      // 清理缓存
      clear(): void {
        this.buffer = Buffer.alloc(0);
      },

      // 获取缓存状态
      getStatus: (audioConfig: any) => {
        const frameSize = (audioConfig?.bitsPerSample || 16) / 8 * (audioConfig?.channels || 1);
        return {
          size: incompleteFrameManager.buffer.length,
          canExtractFrames: incompleteFrameManager.buffer.length >= frameSize
        };
      }
    };

    // 新增：处理并发送音频数据的函数（支持不完整帧处理）
    const processAndSendAudio = (buffer: Buffer) => {
      try {
        const srcSampleRate = this.audioConfig.sampleRate;
        const srcChannels = this.audioConfig.channels;
        const srcBitsPerSample = this.audioConfig.bitsPerSample;
        const targetSampleRate = 16000; // ASR服务要求16kHz采样率

        const bytesPerSample = srcBitsPerSample / 8;
        const frameSize = bytesPerSample * srcChannels;

        // 处理不完整帧：先尝试从缓存中提取完整帧
        const cachedFrames = incompleteFrameManager.extractCompleteFrames(frameSize);

        // 合并缓存的完整帧和新数据
        const combinedBuffer = cachedFrames.completeFrames.length > 0
          ? Buffer.concat([cachedFrames.completeFrames, buffer])
          : buffer;

        // 分离新的完整帧和不完整帧
        const validFrames = Math.floor(combinedBuffer.length / frameSize);
        const validBytes = validFrames * frameSize;

        if (validBytes === 0) {
          // 没有完整帧，将所有数据加入不完整帧缓存
          incompleteFrameManager.addIncompleteFrame(combinedBuffer);
          console.log(`无完整帧，数据已缓存: ${combinedBuffer.length}字节`);
          return;
        }

        // 提取完整帧和新的不完整帧
        const validBuffer = combinedBuffer.subarray(0, validBytes);
        const newIncompleteFrame = combinedBuffer.subarray(validBytes);

        // 将新的不完整帧加入缓存
        if (newIncompleteFrame.length > 0) {
          incompleteFrameManager.addIncompleteFrame(newIncompleteFrame);
          console.log(`缓存新的不完整帧: ${newIncompleteFrame.length}字节`);
        }

        console.log(`处理音频帧: 输入=${buffer.length}字节, 缓存完整帧=${cachedFrames.completeFrames.length}字节, 有效帧=${validBuffer.length}字节, 新不完整帧=${newIncompleteFrame.length}字节`);
        
        // 检查当前音频块是否与最近发送的块重复
        const samplingStep = Math.max(1, Math.floor(validFrames / 100));
        let simpleHash = '';
        for (let i = 0; i < validFrames; i += samplingStep) {
          const frameOffset = i * frameSize;
          if (frameOffset >= validBuffer.length) break;
          
          // 根据位数读取样本
          let sample;
          if (srcBitsPerSample === 32) {
            // 32位整数或浮点数
            if (srcChannels === 1) {
              sample = validBuffer.readInt32LE(frameOffset);
            } else {
              // 取第一个通道
              sample = validBuffer.readInt32LE(frameOffset);
            }
            // 量化32位值
            const quantizedValue = Math.floor(sample / 1000) * 1000;
            simpleHash += quantizedValue.toString(16);
          } else if (srcBitsPerSample === 16) {
            if (srcChannels === 1) {
              sample = validBuffer.readInt16LE(frameOffset);
            } else {
              sample = validBuffer.readInt16LE(frameOffset);
            }
            const quantizedValue = Math.floor(sample / 10) * 10;
            simpleHash += quantizedValue.toString(16);
          }
        }
        
        // 检查是否为重复音频块
        if (audioFeatures.lastChunksHashes.has(simpleHash)) {
          console.log('检测到重复音频块，跳过处理');
          return;
        }
        
        // 添加到最近音频哈希集
        audioFeatures.lastChunksHashes.add(simpleHash);
        if (audioFeatures.lastChunksHashes.size > audioFeatures.maxStoredHashes) {
          const oldestHash = Array.from(audioFeatures.lastChunksHashes)[0];
          audioFeatures.lastChunksHashes.delete(oldestHash);
        }
        
        // 第一步：位深度转换和声道转换
        let processedBuffer;
        
        if (srcBitsPerSample === 32 && srcChannels === 2) {
          // 32位立体声 -> 16位单声道
          const numFrames = validFrames;
          processedBuffer = Buffer.alloc(numFrames * 2); // 16位单声道
          
          for (let i = 0; i < numFrames; i++) {
            const frameOffset = i * 8; // 32位 * 2通道 = 8字节
            
            // 读取左右声道的32位样本
            const leftSample32 = validBuffer.readInt32LE(frameOffset);
            const rightSample32 = validBuffer.readInt32LE(frameOffset + 4);
            
            // 转换为16位（除以65536，从32位范围转到16位范围）
            const leftSample16 = Math.max(-32768, Math.min(32767, Math.round(leftSample32 / 65536)));
            const rightSample16 = Math.max(-32768, Math.min(32767, Math.round(rightSample32 / 65536)));
            
            // 混合为单声道
            const monoSample = Math.round((leftSample16 + rightSample16) / 2);
            
            // 写入16位单声道样本
            processedBuffer.writeInt16LE(monoSample, i * 2);
          }
        } else if (srcBitsPerSample === 32 && srcChannels === 1) {
          // 32位单声道 -> 16位单声道
          const numFrames = validFrames;
          processedBuffer = Buffer.alloc(numFrames * 2); // 16位单声道
          
          for (let i = 0; i < numFrames; i++) {
            const frameOffset = i * 4; // 32位 = 4字节
            const sample32 = validBuffer.readInt32LE(frameOffset);
            
            // 转换为16位
            const sample16 = Math.max(-32768, Math.min(32767, Math.round(sample32 / 65536)));
            processedBuffer.writeInt16LE(sample16, i * 2);
          }
        } else if (srcBitsPerSample === 16 && srcChannels === 2) {
          // 16位立体声 -> 16位单声道
          const numFrames = validFrames;
          processedBuffer = Buffer.alloc(numFrames * 2); // 16位单声道
          
          for (let i = 0; i < numFrames; i++) {
            const frameOffset = i * 4; // 16位 * 2通道 = 4字节
            const leftSample = validBuffer.readInt16LE(frameOffset);
            const rightSample = validBuffer.readInt16LE(frameOffset + 2);
            const avgSample = Math.round((leftSample + rightSample) / 2);
            processedBuffer.writeInt16LE(avgSample, i * 2);
          }
        } else if (srcBitsPerSample === 16 && srcChannels === 1) {
          // 16位单声道 -> 16位单声道（无需转换）
          processedBuffer = validBuffer;
        } else {
          console.error(`不支持的音频格式: ${srcBitsPerSample}位 ${srcChannels}声道`);
          return;
        }
        
        // 第二步：采样率转换
        let finalBuffer;
        if (srcSampleRate !== targetSampleRate) {
          const ratio = srcSampleRate / targetSampleRate;
          const inputSamples = Math.floor(processedBuffer.length / 2);
          const outputSamples = Math.floor(inputSamples / ratio);
          finalBuffer = Buffer.alloc(outputSamples * 2);
        
          if (srcSampleRate % targetSampleRate === 0) {
            // 整数倍下采样
            const step = Math.floor(ratio);
            for (let i = 0; i < outputSamples; i++) {
              const srcIdx = i * step * 2;
              if (srcIdx < processedBuffer.length) {
                const sample = processedBuffer.readInt16LE(srcIdx);
                finalBuffer.writeInt16LE(sample, i * 2);
              }
            }
          } else {
            // 非整数比例重采样，使用线性插值
            for (let i = 0; i < outputSamples; i++) {
              const exactSrcIdx = i * ratio;
              const srcIdx1 = Math.floor(exactSrcIdx);
              const srcIdx2 = Math.min(srcIdx1 + 1, inputSamples - 1);
              const fraction = exactSrcIdx - srcIdx1;
              
              const sample1 = processedBuffer.readInt16LE(srcIdx1 * 2);
              const sample2 = processedBuffer.readInt16LE(srcIdx2 * 2);
              
              const interpolatedSample = Math.round((1 - fraction) * sample1 + fraction * sample2);
              finalBuffer.writeInt16LE(interpolatedSample, i * 2);
            }
          }
        } else {
          // 采样率已符合要求
          finalBuffer = processedBuffer;
          console.log(`采样率已符合要求: ${srcSampleRate}Hz = ${targetSampleRate}Hz, 无需转换`);
        }
        
        // 第三步：静音检测
        const samples = Math.floor(finalBuffer.length / 2);
        const samplingStepSilence = Math.max(1, Math.floor(samples / 50));
        let totalEnergy = 0;
        
        for (let i = 0; i < samples; i += samplingStepSilence) {
          if (i * 2 >= finalBuffer.length) break;
          const sample = finalBuffer.readInt16LE(i * 2);
          totalEnergy += Math.abs(sample);
        }
        
        const avgEnergy = totalEnergy / (samples / samplingStepSilence);
        const SILENCE_THRESHOLD = 50;
        const isSilent = avgEnergy < SILENCE_THRESHOLD;
        
        // 更新静音状态
        if (isSilent) {
          audioFeatures.silenceCounter++;
          if (!audioFeatures.isCurrentlySilent && audioFeatures.silenceCounter >= 2) {
            audioFeatures.isCurrentlySilent = true;
            console.log('检测到静音开始');
          }
        } else {
          if (audioFeatures.isCurrentlySilent) {
            audioFeatures.lastChunksHashes.clear();
            console.log('检测到静音结束，重置音频特征');
          }
          audioFeatures.silenceCounter = 0;
          audioFeatures.isCurrentlySilent = false;
        }
        
        // 静音持续处理
        if (audioFeatures.silenceCounter >= audioFeatures.maxSilenceBeforeReset) {
          audioFeatures.lastChunksHashes.clear();
          audioFeatures.silenceCounter = 0;
          console.log('持续静音，重置音频特征');
        }
        
        // 减少静音期间的发送频率
        if (audioFeatures.isCurrentlySilent && audioFeatures.silenceCounter > 5) {
          if (audioFeatures.silenceCounter % 3 !== 0) {
            return;
          }
        }
        
        // 发送音频数据到ASR
        if (systemASRManager.isASRConnected()) {
          const audioData = {
            audio_data: new Uint8Array(finalBuffer),
            sample_rate: targetSampleRate,
            audio_format: 'pcm'
          };

          // 增加当前的文件位置日志输入
          console.log(`当前文件位置: ${this.positionManager.currentPosition}字节, 总读取: ${stats.totalBytesRead}字节`);
          console.log(`发送音频数据: ${finalBuffer.length}字节, ${Math.floor(finalBuffer.length/2)}样本, 能量=${avgEnergy.toFixed(2)}, 静音=${isSilent}`);

          // 记录音频数据处理性能指标
          this.performanceMetrics.totalAudioChunksSent++;
          this.performanceMetrics.totalAudioDataProcessed += finalBuffer.length;

          systemASRManager.processAudioData(audioData).catch(error => {
            console.error('发送音频数据到ASR失败:', error);
          });
        } else if (!this.streamClosed) {
          systemASRManager.startASRSession().catch(() => {});
        }
      } catch (error) {
        console.error('处理并发送音频数据时出错:', error);
      }
    };

    // 开始监控文件 - 启动第一次读取
    console.log('启动音频文件监控，首次读取将在200ms后开始');
    scheduleNextRead();
  }

  /**
   * 停止系统音频捕获
   */
  public async stopCapturing(): Promise<void> {
    console.log('停止系统音频捕获');

    if (!this.isRecording) {
      console.log('系统音频捕获未启动，无需停止');
      return;
    }

    // 立即标记为已停止录制，防止继续处理音频数据
    this.isRecording = false;

    // 立即关闭文件监控，确保不再读取和处理音频数据
    this.streamClosed = true;

    // 重置重试状态
    this.retryAttempts = 0;
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
      this.retryTimeoutId = null;
    }

    // 通知ASRManager停止系统音频源处理
    systemASRManager.stopASRSession();

    // 如果有子进程，使用改进的终止机制
    if (this.recordingProcess) {
      console.log('开始终止系统音频捕获进程...');
      await this.terminateRecordingProcess();
    }

    // 清理输出文件
    try {
      if (fs.existsSync(this.outputPath)) {
        fs.unlinkSync(this.outputPath);
        console.log('已删除系统音频输出文件');
      }
    } catch (error) {
      console.error('删除系统音频输出文件时出错:', error);
    }

    // 通知渲染进程
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('system-audio:status', { recording: false });
    }
  }

  /**
   * 改进的进程终止方法 - 确保进程被正确终止
   */
  private async terminateRecordingProcess(): Promise<void> {
    if (!this.recordingProcess) {
      this.logProcessAction('terminate_attempt', 0, false, 'No process to terminate');
      return;
    }

    const pid = this.recordingProcess.pid;
    console.log(`正在终止音频捕获进程 PID: ${pid}`);
    this.logProcessAction('terminate_start', pid, true, 'Starting termination process');

    try {
      // 第一步：尝试优雅终止
      console.log('第一步：尝试优雅终止进程...');
      this.logProcessAction('graceful_terminate', pid, true, 'Sending SIGTERM');
      this.recordingProcess.kill('SIGTERM');

      // 等待进程优雅退出
      const gracefulExit = await this.waitForProcessExit(2000); // 等待2秒
      if (gracefulExit) {
        console.log('✅ 进程已优雅退出');
        this.logProcessAction('graceful_exit', pid, true, 'Process exited gracefully');
        this.recordingProcess = null;
        return;
      }

      // 第二步：强制终止
      console.log('第二步：进程未优雅退出，开始强制终止...');
      this.logProcessAction('force_terminate_start', pid, true, 'Starting force termination');
      await this.forceTerminateProcess(pid);

      // 第三步：验证进程是否真正被终止
      const isTerminated = await this.verifyProcessTerminated(pid);
      if (isTerminated) {
        console.log('✅ 系统音频捕获进程已成功终止');
        this.logProcessAction('terminate_success', pid, true, 'Process successfully terminated');
      } else {
        console.error('❌ 进程终止失败，可能仍在运行');
        this.logProcessAction('terminate_failed', pid, false, 'Process may still be running');
      }

    } catch (error) {
      console.error('终止系统音频捕获进程时出错:', error);
      this.logProcessAction('terminate_error', pid, false, `Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      // 无论如何都清空进程引用
      this.recordingProcess = null;
      this.logProcessAction('cleanup_complete', pid, true, 'Process reference cleared');
    }
  }

  /**
   * 等待进程退出
   */
  private async waitForProcessExit(timeoutMs: number): Promise<boolean> {
    if (!this.recordingProcess) {
      return true;
    }

    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        resolve(false);
      }, timeoutMs);

      this.recordingProcess!.on('exit', () => {
        clearTimeout(timeout);
        resolve(true);
      });

      this.recordingProcess!.on('error', () => {
        clearTimeout(timeout);
        resolve(true); // 进程错误也算作退出
      });
    });
  }

  /**
   * 强制终止进程
   */
  private async forceTerminateProcess(pid: number): Promise<void> {
    if (process.platform === 'win32') {
      // Windows: 使用taskkill强制终止
      await this.executeCommand(`taskkill /pid ${pid} /f /t`);
    } else if (process.platform === 'darwin') {
      // macOS: 使用SIGKILL + pkill双重保险
      try {
        if (this.recordingProcess) {
          this.recordingProcess.kill('SIGKILL');
        }
      } catch (e) {
        console.warn('SIGKILL失败:', e);
      }

      // 额外使用pkill确保进程被终止
      await this.executeCommand('pkill -9 -f system-audio-capture');
    } else {
      // Linux等其他平台
      try {
        if (this.recordingProcess) {
          this.recordingProcess.kill('SIGKILL');
        }
      } catch (e) {
        console.warn('SIGKILL失败:', e);
      }
    }
  }

  /**
   * 执行系统命令并等待完成
   */
  private async executeCommand(command: string): Promise<void> {
    return new Promise((resolve) => {
      const { exec } = require('child_process');
      exec(command, (error: any) => {
        if (error) {
          console.warn(`命令执行失败: ${command}`, error);
          // 不抛出错误，因为进程可能已经不存在
          resolve();
        } else {
          console.log(`命令执行成功: ${command}`);
          resolve();
        }
      });
    });
  }

  /**
   * 验证进程是否已被终止
   */
  private async verifyProcessTerminated(pid: number): Promise<boolean> {
    try {
      if (process.platform === 'win32') {
        // Windows: 使用tasklist检查进程是否存在
        const { stdout } = await execAsync(`tasklist /fi "PID eq ${pid}"`);
        return !stdout.includes(pid.toString());
      } else {
        // Unix-like系统: 使用ps检查进程是否存在
        try {
          await execAsync(`ps -p ${pid}`);
          return false; // 如果命令成功，说明进程还存在
        } catch (e) {
          return true; // 如果命令失败，说明进程不存在
        }
      }
    } catch (error) {
      console.warn('验证进程状态时出错:', error);
      return false; // 无法确认，假设进程仍存在
    }
  }

  /**
   * 获取当前录音状态
   */
  public isCapturing(): boolean {
    return this.isRecording;
  }

  /**
   * 安全读取音频数据，避免读写竞争
   */
  private async safeReadAudioData(
    position: number,
    lastValidPosition: number,
    incompleteFrameBuffer: Buffer
  ): Promise<{
    success: boolean;
    data?: Buffer;
    newPosition: number;
    lastValidPosition: number;
    incompleteFrameBuffer: Buffer;
    isEmpty?: boolean;
    error?: string;
  }> {
    try {
      // 检查文件是否存在
      if (!fs.existsSync(this.outputPath)) {
        return {
          success: false,
          isEmpty: true,
          newPosition: position,
          lastValidPosition,
          incompleteFrameBuffer
        };
      }

      // 等待文件稳定（避免读写竞争）
      await this.waitForFileStable();

      // 获取文件状态
      const fileStats = fs.statSync(this.outputPath);
      const fileSize = fileStats.size;

      // 如果文件大小没有变化或没有新数据，返回空结果
      if (fileSize <= position) {
        return {
          success: false,
          isEmpty: true,
          newPosition: position,
          lastValidPosition,
          incompleteFrameBuffer
        };
      }

      // 计算需要读取的数据量
      const dataToRead = fileSize - position;
      console.log(`安全读取: 位置=${position}, 文件大小=${fileSize}, 待读取=${dataToRead}字节`);

      // 读取数据
      const rawData = await this.readFileChunk(position, fileSize - 1);

      // 合并不完整帧缓存和新数据
      const combinedData = incompleteFrameBuffer.length > 0
        ? Buffer.concat([incompleteFrameBuffer, rawData])
        : rawData;

      // 验证和处理音频帧
      const frameResult = this.processAudioFrames(combinedData);

      return {
        success: true,
        data: frameResult.completeFrames,
        newPosition: position + rawData.length,
        lastValidPosition: position + rawData.length, // 更新最后有效位置
        incompleteFrameBuffer: frameResult.incompleteFrame
      };

    } catch (error) {
      console.error('安全读取音频数据失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        newPosition: position,
        lastValidPosition,
        incompleteFrameBuffer
      };
    }
  }

  /**
   * 等待文件稳定，避免读写竞争
   */
  private async waitForFileStable(maxWaitMs: number = 100): Promise<void> {
    const startTime = Date.now();
    let lastSize = -1;
    let stableCount = 0;
    const requiredStableCount = 2; // 需要连续2次大小相同才认为稳定

    while (Date.now() - startTime < maxWaitMs) {
      try {
        if (!fs.existsSync(this.outputPath)) {
          await new Promise(resolve => setTimeout(resolve, 10));
          continue;
        }

        const stats = fs.statSync(this.outputPath);
        const currentSize = stats.size;

        if (currentSize === lastSize) {
          stableCount++;
          if (stableCount >= requiredStableCount) {
            return; // 文件大小稳定
          }
        } else {
          stableCount = 0;
          lastSize = currentSize;
        }

        await new Promise(resolve => setTimeout(resolve, 10));
      } catch (error) {
        // 文件可能正在被写入，继续等待
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }
  }

  /**
   * 读取文件块
   */
  private async readFileChunk(start: number, end: number): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      const chunks: Buffer[] = [];
      const stream = fs.createReadStream(this.outputPath, { start, end });

      stream.on('data', (chunk: Buffer) => {
        chunks.push(chunk);
      });

      stream.on('end', () => {
        resolve(Buffer.concat(chunks));
      });

      stream.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * 处理音频帧，分离完整帧和不完整帧，并进行完整性检查
   */
  private processAudioFrames(data: Buffer): {
    completeFrames: Buffer;
    incompleteFrame: Buffer;
  } {
    if (data.length === 0) {
      return {
        completeFrames: Buffer.alloc(0),
        incompleteFrame: Buffer.alloc(0)
      };
    }

    const bytesPerSample = this.audioConfig.bitsPerSample / 8;
    const frameSize = bytesPerSample * this.audioConfig.channels;

    // 计算完整帧数量
    const completeFrameCount = Math.floor(data.length / frameSize);
    const completeFramesSize = completeFrameCount * frameSize;

    // 分离完整帧和不完整帧
    const completeFrames = completeFramesSize > 0
      ? data.subarray(0, completeFramesSize)
      : Buffer.alloc(0);

    const incompleteFrame = completeFramesSize < data.length
      ? data.subarray(completeFramesSize)
      : Buffer.alloc(0);

    // 数据完整性检查
    const integrityResult = this.validateAudioDataIntegrity(completeFrames, incompleteFrame);

    console.log(`帧处理: 总数据=${data.length}字节, 完整帧=${completeFrames.length}字节, 不完整帧=${incompleteFrame.length}字节`);
    console.log(`完整性检查: ${integrityResult.isValid ? '通过' : '失败'} - ${integrityResult.message}`);

    return {
      completeFrames: integrityResult.isValid ? completeFrames : Buffer.alloc(0),
      incompleteFrame
    };
  }

  /**
   * 音频数据完整性验证
   */
  private validateAudioDataIntegrity(completeFrames: Buffer, incompleteFrame: Buffer): {
    isValid: boolean;
    message: string;
    issues: string[];
  } {
    const issues: string[] = [];
    let isValid = true;

    // 1. 检查数据长度合理性
    if (completeFrames.length === 0 && incompleteFrame.length === 0) {
      issues.push('无音频数据');
      isValid = false;
    }

    // 2. 检查帧对齐
    const bytesPerSample = this.audioConfig.bitsPerSample / 8;
    const frameSize = bytesPerSample * this.audioConfig.channels;

    if (completeFrames.length % frameSize !== 0) {
      issues.push(`完整帧数据未对齐: ${completeFrames.length} % ${frameSize} = ${completeFrames.length % frameSize}`);
      isValid = false;
    }

    // 3. 检查数据范围合理性
    if (completeFrames.length > 0) {
      const sampleCheck = this.validateSampleRange(completeFrames);
      if (!sampleCheck.isValid) {
        issues.push(...sampleCheck.issues);
        // 样本范围问题不一定是致命的，只记录警告
      }
    }

    // 4. 检查不完整帧大小
    if (incompleteFrame.length >= frameSize) {
      issues.push(`不完整帧过大: ${incompleteFrame.length} >= ${frameSize}`);
      // 这通常表示处理逻辑有问题，但不是致命错误
    }

    // 5. 检查数据连续性（如果有历史数据）
    const continuityCheck = this.validateDataContinuity(completeFrames);
    if (!continuityCheck.isValid) {
      issues.push(...continuityCheck.issues);
      // 连续性问题可能是正常的（如静音段），不标记为致命错误
    }

    return {
      isValid,
      message: isValid ? '数据完整性检查通过' : `发现${issues.length}个问题`,
      issues
    };
  }

  /**
   * 验证音频样本范围
   */
  private validateSampleRange(data: Buffer): {
    isValid: boolean;
    issues: string[];
  } {
    const issues: string[] = [];
    const bytesPerSample = this.audioConfig.bitsPerSample / 8;
    const frameSize = bytesPerSample * this.audioConfig.channels;
    const frameCount = data.length / frameSize;

    let invalidSamples = 0;
    let maxSample = 0;
    let minSample = 0;

    // 采样检查（不检查所有样本，避免性能问题）
    const checkStep = Math.max(1, Math.floor(frameCount / 100)); // 检查1%的样本

    for (let i = 0; i < frameCount; i += checkStep) {
      const frameOffset = i * frameSize;

      for (let ch = 0; ch < this.audioConfig.channels; ch++) {
        const sampleOffset = frameOffset + ch * bytesPerSample;
        let sample: number;

        if (bytesPerSample === 2) {
          // 16位样本
          sample = data.readInt16LE(sampleOffset);
          if (sample < -32768 || sample > 32767) {
            invalidSamples++;
          }
        } else if (bytesPerSample === 4) {
          // 32位样本
          sample = data.readInt32LE(sampleOffset);
          if (sample < -2147483648 || sample > 2147483647) {
            invalidSamples++;
          }
        } else {
          continue; // 不支持的格式
        }

        maxSample = Math.max(maxSample, sample);
        minSample = Math.min(minSample, sample);
      }
    }

    // 检查无效样本比例
    const checkedSamples = Math.floor(frameCount / checkStep) * this.audioConfig.channels;
    const invalidRatio = invalidSamples / checkedSamples;

    if (invalidRatio > 0.01) { // 超过1%的样本无效
      issues.push(`无效样本比例过高: ${(invalidRatio * 100).toFixed(2)}%`);
    }

    // 检查动态范围
    const dynamicRange = maxSample - minSample;
    if (dynamicRange === 0) {
      issues.push('音频数据无动态范围（可能是静音或损坏）');
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }

  /**
   * 验证数据连续性
   */
  private validateDataContinuity(data: Buffer): {
    isValid: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    // 这里可以添加更复杂的连续性检查
    // 例如：检查音频能量的突变、频谱的连续性等

    // 简单的能量连续性检查
    if (data.length >= 4) {
      const bytesPerSample = this.audioConfig.bitsPerSample / 8;
      const frameSize = bytesPerSample * this.audioConfig.channels;
      const frameCount = Math.floor(data.length / frameSize);

      if (frameCount >= 10) {
        // 检查前后能量差异
        const frontEnergy = this.calculateAudioEnergy(data.subarray(0, frameSize * 5));
        const backEnergy = this.calculateAudioEnergy(data.subarray(-frameSize * 5));

        const energyRatio = backEnergy > 0 ? frontEnergy / backEnergy : 1;

        if (energyRatio > 100 || energyRatio < 0.01) {
          issues.push(`音频能量变化异常: 比例=${energyRatio.toFixed(2)}`);
        }
      }
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }

  /**
   * 计算音频能量
   */
  private calculateAudioEnergy(data: Buffer): number {
    if (data.length === 0) return 0;

    const bytesPerSample = this.audioConfig.bitsPerSample / 8;
    const frameSize = bytesPerSample * this.audioConfig.channels;
    const frameCount = Math.floor(data.length / frameSize);

    let totalEnergy = 0;
    let sampleCount = 0;

    for (let i = 0; i < frameCount; i++) {
      const frameOffset = i * frameSize;

      for (let ch = 0; ch < this.audioConfig.channels; ch++) {
        const sampleOffset = frameOffset + ch * bytesPerSample;
        let sample: number;

        if (bytesPerSample === 2) {
          sample = data.readInt16LE(sampleOffset);
        } else if (bytesPerSample === 4) {
          sample = data.readInt32LE(sampleOffset);
        } else {
          continue;
        }

        totalEnergy += sample * sample;
        sampleCount++;
      }
    }

    return sampleCount > 0 ? Math.sqrt(totalEnergy / sampleCount) : 0;
  }

  /**
   * 位置指针管理器
   */
  private positionManager = {
    currentPosition: 0,
    lastValidPosition: 0,
    lastSuccessfulReadTime: 0,
    consecutiveErrors: 0,
    maxConsecutiveErrors: 5,

    // 更新位置
    updatePosition(newPosition: number, isValid: boolean = true): void {
      this.currentPosition = newPosition;
      if (isValid) {
        this.lastValidPosition = newPosition;
        this.lastSuccessfulReadTime = Date.now();
        this.consecutiveErrors = 0;
      }
    },

    // 记录错误
    recordError(): boolean {
      this.consecutiveErrors++;
      return this.consecutiveErrors >= this.maxConsecutiveErrors;
    },

    // 恢复到最后有效位置
    recoverToLastValid(): number {
      console.log(`位置指针恢复: ${this.currentPosition} -> ${this.lastValidPosition}`);
      this.currentPosition = this.lastValidPosition;
      this.consecutiveErrors = 0;
      return this.currentPosition;
    },

    // 检查位置是否需要重置
    shouldReset(): boolean {
      const timeSinceLastSuccess = Date.now() - this.lastSuccessfulReadTime;
      return timeSinceLastSuccess > 30000; // 30秒没有成功读取
    },

    // 重置位置管理器
    reset(): void {
      this.currentPosition = 0;
      this.lastValidPosition = 0;
      this.lastSuccessfulReadTime = Date.now();
      this.consecutiveErrors = 0;
      console.log('位置指针管理器已重置');
    }
  };

  /**
   * 处理音频读取错误
   * 增强的错误处理机制，包括重试和恢复策略
   */
  private handleAudioReadError(error: Error, currentPosition: number, scheduleNextRead: () => void): void {
    console.error(`音频读取错误: ${error.message}, 当前位置: ${currentPosition}`);

    // 记录音频读取错误
    this.performanceMetrics.audioReadErrors++;

    // 记录连续错误
    const shouldReset = this.positionManager.recordError();

    // 分析错误类型并采取相应措施
    if (error.message.includes('ENOENT')) {
      // 文件不存在错误
      console.warn('音频文件不存在，可能是录音进程异常终止');
      this.positionManager.reset(); // 重置位置管理器
      this.handleRecordingProcessFailure();
    } else if (error.message.includes('EACCES')) {
      // 权限错误
      console.error('音频文件访问权限错误');
      this.notifyError('音频文件访问权限不足');
    } else if (error.message.includes('EMFILE') || error.message.includes('ENFILE')) {
      // 文件句柄耗尽
      console.error('系统文件句柄耗尽');
      setTimeout(() => {
        scheduleNextRead();
      }, 1000); // 延迟1秒后重试
    } else if (shouldReset) {
      // 连续错误过多，重置位置
      console.error(`连续错误过多(${this.positionManager.consecutiveErrors})，重置位置指针`);
      this.positionManager.recoverToLastValid();
      setTimeout(() => {
        scheduleNextRead();
      }, 2000); // 延迟2秒后重试
    } else {
      // 其他未知错误，尝试恢复到最后有效位置
      console.error('未知音频读取错误，尝试恢复位置:', error);
      this.positionManager.recoverToLastValid();
      setTimeout(() => {
        scheduleNextRead();
      }, 500); // 延迟500ms后重试
    }
  }

  /**
   * 处理录音进程失败
   */
  private handleRecordingProcessFailure(): void {
    console.log('检测到录音进程可能失败，尝试重启录音');

    // 记录进程失败
    this.performanceMetrics.processFailures++;

    // 标记当前录音状态为失败
    this.isRecording = false;
    this.streamClosed = true;

    // 清理现有进程
    if (this.recordingProcess) {
      try {
        this.recordingProcess.kill('SIGTERM');
        this.recordingProcess = null;
      } catch (e) {
        console.warn('清理失败的录音进程时出错:', e);
      }
    }

    // 通知UI录音状态变化
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('system-audio:status', {
        recording: false,
        error: '录音进程异常，正在尝试重启...'
      });
    }

    // 延迟重启录音
    setTimeout(async () => {
      console.log('尝试重启系统音频录音...');
      try {
        // 记录录音重启
        this.performanceMetrics.recordingRestarts++;

        const result = await this.startCapturing();
        if (result.success) {
          console.log('系统音频录音重启成功');
        } else {
          console.error('系统音频录音重启失败:', result.error);
          this.notifyError(`录音重启失败: ${result.error}`);
        }
      } catch (error) {
        console.error('重启录音时出错:', error);
        this.notifyError('录音重启过程中发生错误');
      }
    }, 3000); // 3秒后重启
  }

  /**
   * 通知错误信息到UI
   */
  private notifyError(message: string): void {
    console.error(`SystemAudioManager错误: ${message}`);

    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('system-audio:error', {
        error: message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 健康检查 - 定期检查录音状态
   */
  private startHealthCheck(): void {
    setInterval(() => {
      if (this.isRecording) {
        // 检查输出文件是否还在增长
        if (fs.existsSync(this.outputPath)) {
          const stats = fs.statSync(this.outputPath);
          const currentSize = stats.size;

          // 如果文件大小在30秒内没有变化，可能录音进程有问题
          if (!this.lastFileSize) {
            this.lastFileSize = currentSize;
            this.lastFileSizeCheckTime = Date.now();
          } else {
            const now = Date.now();
            if (currentSize === this.lastFileSize && now - this.lastFileSizeCheckTime > 30000) {
              console.warn('检测到音频文件长时间未增长，可能录音进程异常');
              this.handleRecordingProcessFailure();
            } else if (currentSize !== this.lastFileSize) {
              // 文件大小有变化，更新记录
              this.lastFileSize = currentSize;
              this.lastFileSizeCheckTime = now;
            }
          }
        } else if (this.isRecording) {
          // 录音状态为true但文件不存在
          console.warn('录音状态异常：标记为录音中但输出文件不存在');
          this.handleRecordingProcessFailure();
        }
      }
    }, 10000); // 每10秒检查一次
  }

  // 添加健康检查相关属性
  private lastFileSize?: number;
  private lastFileSizeCheckTime: number = 0;

  // 进程状态监控属性
  private processTerminationLog: Array<{
    timestamp: number;
    action: string;
    pid?: number;
    success: boolean;
    details?: string;
  }> = [];

  // 性能监控系统
  private performanceMetrics = {
    totalRecordingTime: 0,
    recordingStartTime: 0,
    totalAudioDataProcessed: 0,
    totalAudioChunksSent: 0,
    averageChunkSize: 0,
    recordingRestarts: 0,
    audioReadErrors: 0,
    processFailures: 0,
    lastResetTime: Date.now(),
    memoryUsage: {
      heapUsed: 0,
      heapTotal: 0,
      external: 0,
      rss: 0
    }
  };

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    // 每30秒记录一次性能指标
    setInterval(() => {
      this.recordPerformanceMetrics();
    }, 30000);

    // 每5分钟输出详细的性能报告
    setInterval(() => {
      this.generatePerformanceReport();
    }, 300000);
  }

  /**
   * 记录性能指标
   */
  private recordPerformanceMetrics(): void {
    const now = Date.now();

    // 更新录音时间
    if (this.isRecording && this.performanceMetrics.recordingStartTime > 0) {
      this.performanceMetrics.totalRecordingTime = now - this.performanceMetrics.recordingStartTime;
    }

    // 记录内存使用情况
    const memUsage = process.memoryUsage();
    this.performanceMetrics.memoryUsage = {
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
      external: Math.round(memUsage.external / 1024 / 1024), // MB
      rss: Math.round(memUsage.rss / 1024 / 1024) // MB
    };

    // 计算平均音频块大小
    if (this.performanceMetrics.totalAudioChunksSent > 0) {
      this.performanceMetrics.averageChunkSize = Math.round(
        this.performanceMetrics.totalAudioDataProcessed / this.performanceMetrics.totalAudioChunksSent
      );
    }
  }

  /**
   * 生成性能报告
   */
  private generatePerformanceReport(): void {
    const now = Date.now();
    const runningTime = (now - this.performanceMetrics.lastResetTime) / 1000 / 60; // 分钟

    console.log('=== SystemAudioManager 性能报告 ===');
    console.log(`运行时间: ${runningTime.toFixed(1)} 分钟`);
    console.log(`当前录音状态: ${this.isRecording ? '录音中' : '未录音'}`);

    if (this.performanceMetrics.totalRecordingTime > 0) {
      console.log(`总录音时间: ${(this.performanceMetrics.totalRecordingTime / 1000 / 60).toFixed(1)} 分钟`);
    }

    console.log(`录音重启次数: ${this.performanceMetrics.recordingRestarts}`);
    console.log(`音频读取错误: ${this.performanceMetrics.audioReadErrors}`);
    console.log(`进程失败次数: ${this.performanceMetrics.processFailures}`);

    console.log(`音频数据处理: ${this.performanceMetrics.totalAudioChunksSent} 块`);
    console.log(`音频数据总量: ${(this.performanceMetrics.totalAudioDataProcessed / 1024 / 1024).toFixed(2)} MB`);

    if (this.performanceMetrics.averageChunkSize > 0) {
      console.log(`平均音频块大小: ${this.performanceMetrics.averageChunkSize} 字节`);
    }

    console.log(`内存使用 - 堆已用: ${this.performanceMetrics.memoryUsage.heapUsed}MB, 堆总计: ${this.performanceMetrics.memoryUsage.heapTotal}MB`);
    console.log(`内存使用 - 外部: ${this.performanceMetrics.memoryUsage.external}MB, RSS: ${this.performanceMetrics.memoryUsage.rss}MB`);

    console.log('================================');

    // 发送性能报告到UI
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('system-audio:performance-report', {
        runningTime: runningTime.toFixed(1),
        isRecording: this.isRecording,
        totalRecordingTime: this.performanceMetrics.totalRecordingTime > 0 ?
          (this.performanceMetrics.totalRecordingTime / 1000 / 60).toFixed(1) : '0',
        recordingRestarts: this.performanceMetrics.recordingRestarts,
        audioReadErrors: this.performanceMetrics.audioReadErrors,
        processFailures: this.performanceMetrics.processFailures,
        audioChunksSent: this.performanceMetrics.totalAudioChunksSent,
        audioDataProcessed: (this.performanceMetrics.totalAudioDataProcessed / 1024 / 1024).toFixed(2),
        averageChunkSize: this.performanceMetrics.averageChunkSize,
        memoryUsage: this.performanceMetrics.memoryUsage,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 记录进程操作日志
   */
  private logProcessAction(action: string, pid?: number, success: boolean = true, details?: string): void {
    const logEntry = {
      timestamp: Date.now(),
      action,
      pid,
      success,
      details
    };

    this.processTerminationLog.push(logEntry);

    // 保持日志数量在合理范围内
    if (this.processTerminationLog.length > 50) {
      this.processTerminationLog = this.processTerminationLog.slice(-30);
    }

    // 输出详细日志
    const timestamp = new Date(logEntry.timestamp).toISOString();
    const status = success ? '✅' : '❌';
    console.log(`${status} [${timestamp}] 进程操作: ${action} (PID: ${pid || 'N/A'}) - ${details || ''}`);
  }

  /**
   * 获取进程终止日志
   */
  public getProcessTerminationLog(): Array<{
    timestamp: number;
    action: string;
    pid?: number;
    success: boolean;
    details?: string;
  }> {
    return [...this.processTerminationLog];
  }

  /**
   * 获取当前进程状态诊断信息
   */
  public getProcessDiagnostics(): {
    isRecording: boolean;
    hasProcess: boolean;
    processId?: number;
    streamClosed: boolean;
    recentLogs: Array<any>;
    systemInfo: {
      platform: string;
      nodeVersion: string;
    };
  } {
    return {
      isRecording: this.isRecording,
      hasProcess: !!this.recordingProcess,
      processId: this.recordingProcess?.pid,
      streamClosed: this.streamClosed,
      recentLogs: this.processTerminationLog.slice(-10),
      systemInfo: {
        platform: process.platform,
        nodeVersion: process.version
      }
    };
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.streamClosed = true;
    this.stopCapturing();
  }
}

export const systemAudioManager = SystemAudioManager.getInstance();
export default systemAudioManager;