/**
 * Audio Converter Helper for Electron
 * Provides utilities for audio processing and conversion
 * Based on the ambio project implementation
 */

import { ipcMain } from 'electron';

export class AudioConverterHelper {
  /**
   * Initialize IPC handlers for audio conversion
   */
  public initialize(): void {
    // Register IPC handlers for audio conversion
    ipcMain.handle('audio-converter:convert-float32-to-pcm16', this.handleConvertFloat32ToPCM16);
  }

  /**
   * Convert Float32Array to Int16Array (PCM)
   * @param event - IPC event
   * @param float32Array - Float32Array to convert
   * @returns Int16Array PCM data
   */
  private handleConvertFloat32ToPCM16(_event: Electron.IpcMainInvokeEvent, float32Array: Float32Array): Int16Array {
    return this.convertFloat32ToPCM16(float32Array);
  }

  /**
   * Convert Float32Array to Int16Array (PCM)
   * @param float32Array - Float32Array to convert
   * @returns Int16Array PCM data
   */
  public convertFloat32ToPCM16(float32Array: Float32Array): Int16Array {
    const pcm16 = new Int16Array(float32Array.length);
    
    // Convert -1.0 ~ 1.0 float values to -32768 ~ 32767 integer values
    for (let i = 0; i < float32Array.length; i++) {
      const s = Math.max(-1, Math.min(1, float32Array[i]));
      pcm16[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
    }
    
    return pcm16;
  }

  /**
   * Create a PCM data collector function
   * @param chunkSize - Size of each chunk in samples
   * @returns Function to process PCM data
   */
  public createPCMCollector(chunkSize = 16000): (newData: ArrayBuffer, callback: (chunk: Int16Array, isLast: boolean) => void, forceFlush?: boolean) => void {
    let buffer = new Int16Array(0);
    
    /**
     * Process new PCM data
     * @param newData - New PCM data as ArrayBuffer
     * @param callback - Callback to process audio chunks (chunk, isLast)
     * @param forceFlush - Whether to force process all data (last chunk)
     */
    return function processPCMData(
      newData: ArrayBuffer, 
      callback: (chunk: Int16Array, isLast: boolean) => void, 
      forceFlush = false
    ): void {
      // Convert ArrayBuffer to Int16Array
      const int16Data = new Int16Array(newData);
      
      // Merge new data into buffer
      const newBuffer = new Int16Array(buffer.length + int16Data.length);
      newBuffer.set(buffer);
      newBuffer.set(int16Data, buffer.length);
      buffer = newBuffer;
      
      // Process buffer when it reaches chunk size or needs to be flushed
      while (buffer.length >= chunkSize || (forceFlush && buffer.length > 0)) {
        const chunkLength = Math.min(chunkSize, buffer.length);
        const chunk = buffer.slice(0, chunkLength);
        
        // Check if this is the last chunk
        const isLast = forceFlush && (chunkLength === buffer.length);
        
        // Process chunk via callback
        callback(chunk, isLast);
        
        // Remove processed data from buffer
        buffer = buffer.slice(chunkLength);
      }
    };
  }
}

export const audioConverterHelper = new AudioConverterHelper();
export default audioConverterHelper; 