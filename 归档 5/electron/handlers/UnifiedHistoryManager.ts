import { BrowserWindow } from 'electron';

export interface HistoryItem {
  text: string;
  source: 'microphone' | 'system';
  timestamp: string;
  isFinal: boolean;
  paragraphId?: string;
}

/**
 * 统一的历史消息管理器
 * 解决麦克风和系统音频历史消息闪烁问题
 */
export class UnifiedHistoryManager {
  private static instance: UnifiedHistoryManager | null = null;
  private mainWindow: BrowserWindow | null = null;
  
  // 统一的历史记录存储
  private history: HistoryItem[] = [];
  private lastUpdateTime: number = 0;
  private readonly UPDATE_THROTTLE = 100; // 减少到100ms节流，提高响应性
  private updateTimer: NodeJS.Timeout | null = null;
  private pendingUpdate: boolean = false; // 标记是否有待处理的更新
  
  // 去重缓存
  private recentTexts = new Map<string, { timestamp: number; source: string }>();
  private readonly DEDUPE_WINDOW = 5000; // 5秒去重窗口
  
  // 性能监控
  private stats = {
    totalUpdates: 0,
    throttledUpdates: 0,
    duplicatesFiltered: 0,
    lastStatsTime: Date.now()
  };

  private constructor() {
    console.log('UnifiedHistoryManager: 实例已创建');
  }

  public static getInstance(): UnifiedHistoryManager {
    if (!UnifiedHistoryManager.instance) {
      UnifiedHistoryManager.instance = new UnifiedHistoryManager();
    }
    return UnifiedHistoryManager.instance;
  }

  public setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;

    // 每次设置新窗口时，清空历史记录，确保干净的开始
    console.log('UnifiedHistoryManager: 🧹 设置新主窗口，清空历史记录');
    this.clearHistory();
  }

  /**
   * 添加历史记录项
   */
  public addHistoryItem(item: HistoryItem): void {
    try {
      console.log(`UnifiedHistoryManager: 🔍 尝试添加历史项 [${item.source}]: "${item.text?.substring(0, 50)}${item.text?.length > 50 ? '...' : ''}"`);

      // 数据验证
      if (!item.text || item.text.trim().length === 0) {
        console.log(`UnifiedHistoryManager: ❌ 跳过空文本`);
        return;
      }

      const now = Date.now();
      const text = item.text.trim();
      const source = item.source;

      console.log(`UnifiedHistoryManager: 🔍 当前历史记录数量: ${this.history.length}`);
      console.log(`UnifiedHistoryManager: 🔍 去重缓存大小: ${this.recentTexts.size}`);

      // 去重检查
      if (this.isDuplicate(text, source, now)) {
        this.stats.duplicatesFiltered++;
        console.log(`UnifiedHistoryManager: ❌ 过滤重复消息 [${source}]: "${text}"`);
        return;
      }

      // 创建标准化的历史项
      const historyItem: HistoryItem = {
        text,
        source,
        timestamp: item.timestamp || new Date().toISOString(),
        isFinal: item.isFinal,
        paragraphId: item.paragraphId || `${source}-${Date.now()}`
      };

      // 最后一次检查：确保不与现有历史记录完全相同
      const isDuplicateInHistory = this.history.some(existing =>
        existing.source === source && existing.text === text
      );

      if (isDuplicateInHistory) {
        console.log(`UnifiedHistoryManager: 与现有历史记录完全相同，跳过添加 [${source}]: "${text}"`);
        return;
      }

      // 添加到历史记录
      this.history.push(historyItem);

      // 更新去重缓存
      this.recentTexts.set(`${source}:${text}`, { timestamp: now, source });

      // 限制历史记录大小
      if (this.history.length > 100) {
        this.history = this.history.slice(-100);
      }
      
      // 清理过期的去重缓存
      this.cleanupDedupeCache(now);
      
      // 对于最终结果，立即更新；对于临时结果，使用节流
      if (item.isFinal) {
        this.forceUpdate();
      } else {
        this.scheduleUpdate();
      }

      console.log(`UnifiedHistoryManager: ✅ 添加历史项 [${source}]: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`);
      console.log(`UnifiedHistoryManager: 📊 当前历史记录总数: ${this.history.length}`);

    } catch (error) {
      console.error('UnifiedHistoryManager: 添加历史项时出错:', error);
    }
  }

  /**
   * 批量添加历史记录
   */
  public addHistoryItems(items: HistoryItem[]): void {
    if (!items || items.length === 0) return;
    
    console.log(`UnifiedHistoryManager: 批量添加 ${items.length} 个历史项`);
    
    for (const item of items) {
      this.addHistoryItem(item);
    }
  }

  /**
   * 检查是否为重复消息（简化版，减少误判）
   */
  private isDuplicate(text: string, source: string, timestamp: number): boolean {
    console.log(`UnifiedHistoryManager: 🔍 检查重复 [${source}]: "${text}"`);

    // 1. 精确匹配检查
    const exactKey = `${source}:${text}`;
    const exactMatch = this.recentTexts.get(exactKey);

    if (exactMatch && timestamp - exactMatch.timestamp < this.DEDUPE_WINDOW) {
      console.log(`UnifiedHistoryManager: 🔍 精确匹配重复`);
      return true;
    }

    // 2. 只检查最近3条同源历史记录的精确匹配
    const recentSameSourceHistory = this.history
      .filter(item => item.source === source)
      .slice(-3); // 只检查最近3条同源记录

    for (const item of recentSameSourceHistory) {
      const timeDiff = timestamp - new Date(item.timestamp).getTime();

      // 只在很短的时间窗口内（2秒）检查重复
      if (timeDiff < 2000) {
        if (item.text === text) {
          console.log(`UnifiedHistoryManager: 🔍 与最近历史记录完全相同`);
          return true;
        }
      }
    }

    console.log(`UnifiedHistoryManager: 🔍 未发现重复，允许添加`);
    return false;
  }



  /**
   * 清理过期的去重缓存
   */
  private cleanupDedupeCache(currentTime: number): void {
    for (const [key, value] of this.recentTexts.entries()) {
      if (currentTime - value.timestamp > this.DEDUPE_WINDOW) {
        this.recentTexts.delete(key);
      }
    }
  }

  /**
   * 强制立即更新UI
   */
  private forceUpdate(): void {
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
      this.updateTimer = null;
    }
    this.sendHistoryUpdate();
  }

  /**
   * 调度UI更新（优化的节流机制）
   */
  private scheduleUpdate(): void {
    this.pendingUpdate = true;

    if (this.updateTimer) {
      return; // 已经有待处理的更新
    }

    const now = Date.now();
    const timeSinceLastUpdate = now - this.lastUpdateTime;

    if (timeSinceLastUpdate >= this.UPDATE_THROTTLE) {
      // 立即更新
      this.sendHistoryUpdate();
    } else {
      // 延迟更新，但确保不会丢失更新
      const delay = this.UPDATE_THROTTLE - timeSinceLastUpdate;
      this.updateTimer = setTimeout(() => {
        if (this.pendingUpdate) {
          this.sendHistoryUpdate();
        }
      }, delay);

      this.stats.throttledUpdates++;
    }
  }

  /**
   * 发送历史更新到渲染进程
   */
  private sendHistoryUpdate(): void {
    try {
      if (this.updateTimer) {
        clearTimeout(this.updateTimer);
        this.updateTimer = null;
      }

      this.pendingUpdate = false; // 清除待处理标记

      if (!this.mainWindow || this.mainWindow.isDestroyed()) {
        return;
      }

      const now = Date.now();
      this.lastUpdateTime = now;
      this.stats.totalUpdates++;

      // 获取最近的历史记录
      const recentHistory = this.history.slice(-50);

      // 按时间戳排序
      const sortedHistory = recentHistory.sort((a, b) =>
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );

      // 按来源分组统计
      const sourceStats = sortedHistory.reduce((acc, item) => {
        acc[item.source] = (acc[item.source] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      console.log(`UnifiedHistoryManager: 🚀 发送统一历史更新，共 ${sortedHistory.length} 条记录`, sourceStats);

      // 发送统一的历史更新事件
      this.mainWindow.webContents.send('asr:unified-history-update', {
        history: sortedHistory,
        timestamp: now,
        sourceStats // 添加来源统计信息
      });

      // 定期输出统计信息
      if (now - this.stats.lastStatsTime > 10000) {
        console.log(`UnifiedHistoryManager 📊 统计: 总更新=${this.stats.totalUpdates}, 节流更新=${this.stats.throttledUpdates}, 过滤重复=${this.stats.duplicatesFiltered}`);
        this.stats.lastStatsTime = now;
      }

    } catch (error) {
      console.error('UnifiedHistoryManager: 发送历史更新时出错:', error);
    }
  }

  /**
   * 获取当前历史记录
   */
  public getHistory(): HistoryItem[] {
    return [...this.history];
  }

  /**
   * 清空历史记录
   */
  public clearHistory(): void {
    this.history = [];
    this.recentTexts.clear();
    
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
      this.updateTimer = null;
    }
    
    this.sendHistoryUpdate();
    console.log('UnifiedHistoryManager: 历史记录已清空');
  }

  /**
   * 获取统计信息
   */
  public getStats() {
    return {
      ...this.stats,
      historyCount: this.history.length,
      cacheSize: this.recentTexts.size
    };
  }

  /**
   * 销毁实例
   */
  public destroy(): void {
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
      this.updateTimer = null;
    }
    
    this.history = [];
    this.recentTexts.clear();
    this.mainWindow = null;
    
    console.log('UnifiedHistoryManager: 实例已销毁');
  }
}

// 导出单例实例
export const unifiedHistoryManager = UnifiedHistoryManager.getInstance();
