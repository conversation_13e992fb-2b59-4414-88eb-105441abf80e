// ScreenshotHelper.ts

import { execFile } from "child_process"
import { app, screen, nativeImage } from "electron"
import fs from "node:fs"
import path from "node:path"
import { promisify } from "util"
import { v4 as uuidv4 } from "uuid"

const execFileAsync = promisify(execFile)

export class ScreenshotHelper {
  private screenshotQueue: string[] = []
  private extraScreenshotQueue: string[] = []
  private readonly MAX_SCREENSHOTS = 2
  private readonly MAX_FILE_SIZE = 1 * 1024 * 1024 // 3MB in bytes

  private readonly screenshotDir: string
  private readonly extraScreenshotDir: string

  private view: "queue" | 'voice' | "solutions" = "queue"

  constructor(view: "queue" | 'voice' | "solutions" = "queue") {
    this.view = view

    // Initialize directories
    this.screenshotDir = path.join(app.getPath("userData"), "screenshots")
    this.extraScreenshotDir = path.join(
      app.getPath("userData"),
      "extra_screenshots"
    )

    // Create directories if they don't exist
    if (!fs.existsSync(this.screenshotDir)) {
      fs.mkdirSync(this.screenshotDir)
    }
    if (!fs.existsSync(this.extraScreenshotDir)) {
      fs.mkdirSync(this.extraScreenshotDir)
    }
  }

  public getView(): "queue" | "solutions" | 'voice'{
    return this.view
  }

  public setView(view: "queue" | "solutions" | 'voice'): void {
    this.view = view
  }

  public getScreenshotQueue(): string[] {
    return this.screenshotQueue
  }

  public getExtraScreenshotQueue(): string[] {
    return this.extraScreenshotQueue
  }

  public clearQueues(): void {
    // Clear screenshotQueue
    this.screenshotQueue.forEach((screenshotPath) => {
      fs.unlink(screenshotPath, (err) => {
        if (err)
          console.error(`Error deleting screenshot at ${screenshotPath}:`, err)
      })
    })
    this.screenshotQueue = []

    // Clear extraScreenshotQueue
    // this.extraScreenshotQueue.forEach((screenshotPath) => {
    //   fs.unlink(screenshotPath, (err) => {
    //     if (err)
    //       console.error(
    //         `Error deleting extra screenshot at ${screenshotPath}:`,
    //         err
    //       )
    //   })
    // })
    // this.extraScreenshotQueue = []
  }

  private async captureScreenshotMac(mainWindow: Electron.BrowserWindow): Promise<Buffer> {
    const bounds = mainWindow.getBounds();
    const display = screen.getPrimaryDisplay();
    const screenWidth = display.bounds.width;
    const screenHeight = display.bounds.height;

    const width = Math.min(bounds.width * 2 + bounds.width / 2, screenWidth - bounds.x);
    const height = Math.min(500, screenHeight - bounds.y);

    const tmpPath = path.join(app.getPath("temp"), `${uuidv4()}.png`);
    
    // Capture the specific area of the mainWindow
    await execFileAsync("screencapture", [
        "-x",
        "-R",
        `${bounds.x},${bounds.y},${width},${height}`,
        tmpPath
    ]);
    const buffer = await fs.promises.readFile(tmpPath);
    await fs.promises.unlink(tmpPath);
    return buffer;
  }

  private async captureScreenshotWindows(mainWindow: Electron.BrowserWindow): Promise<Buffer> {
    const bounds = mainWindow.getBounds();
    const screenWidth = bounds.width;
    const screenHeight = bounds.height;

    const width = Math.min(bounds.width * 2 + bounds.width / 2, screenWidth - bounds.x);
    const height = Math.min(500, screenHeight - bounds.y);

    const tmpPath = path.join(app.getPath("temp"), `${uuidv4()}.png`);

    // 使用 PowerShell 的本地截图功能
    const script = `
      Add-Type -AssemblyName System.Windows.Forms
      Add-Type -AssemblyName System.Drawing
      $bounds = [System.Drawing.Rectangle]::FromLTRB(${bounds.x}, ${bounds.y}, ${bounds.x + width}, ${bounds.y + height})
      $bitmap = New-Object System.Drawing.Bitmap $bounds.Width, $bounds.Height
      $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
      $graphics.CopyFromScreen($bounds.Location, [System.Drawing.Point]::Empty, $bounds.Size)
      $bitmap.Save('${tmpPath.replace(/\\/g, "\\\\")}')
      $graphics.Dispose()
      $bitmap.Dispose()
    `
    await execFileAsync("powershell", ["-command", script]);
    const buffer = await fs.promises.readFile(tmpPath);
    await fs.promises.unlink(tmpPath);
    return buffer;
  }

  /**
   * 检查并压缩图片
   * @param buffer 原始图片缓冲区
   * @returns 压缩后的图片缓冲区
   */
  private async compressImageIfNeeded(buffer: Buffer): Promise<Buffer> {
    const originalSize = buffer.length;
    
    // 如果图片大小小于3MB，直接返回原图
    if (originalSize <= this.MAX_FILE_SIZE) {
      console.log(`[ScreenshotHelper] 图片大小 ${(originalSize / 1024 / 1024).toFixed(2)}MB，无需压缩`);
      return buffer;
    }

    console.log(`[ScreenshotHelper] 图片大小 ${(originalSize / 1024 / 1024).toFixed(2)}MB，开始压缩...`);

    try {
      // 使用 Electron 的 nativeImage 进行压缩
      const image = nativeImage.createFromBuffer(buffer);
      const size = image.getSize();
      
      // 第一次压缩：降低质量
      let compressedBuffer = image.toPNG({
        scaleFactor: 1.0
      });

      // 如果压缩后仍然大于3MB，尝试缩放图片
      if (compressedBuffer.length > this.MAX_FILE_SIZE) {
        console.log(`[ScreenshotHelper] 第一次压缩后仍为 ${(compressedBuffer.length / 1024 / 1024).toFixed(2)}MB，进行尺寸压缩...`);
        
        // 计算缩放比例，确保文件大小在3MB以下
        const scaleFactor = Math.sqrt(this.MAX_FILE_SIZE * 0.9 / compressedBuffer.length);
        const newWidth = Math.floor(size.width * scaleFactor);
        const newHeight = Math.floor(size.height * scaleFactor);
        
        // 缩放图片
        const resizedImage = image.resize({
          width: newWidth,
          height: newHeight,
          quality: 'good'
        });
        
        compressedBuffer = resizedImage.toPNG();
      }

      // 如果还是太大，进一步降低质量 - 转换为 JPEG
      if (compressedBuffer.length > this.MAX_FILE_SIZE) {
        console.log(`[ScreenshotHelper] PNG压缩后仍为 ${(compressedBuffer.length / 1024 / 1024).toFixed(2)}MB，转换为JPEG...`);
        
        // 使用 JPEG 格式进一步压缩
        let quality = 80;
        let jpegBuffer;
        
        do {
          jpegBuffer = image.toJPEG(quality);
          quality -= 10;
        } while (jpegBuffer.length > this.MAX_FILE_SIZE && quality > 20);
        
        if (jpegBuffer.length <= this.MAX_FILE_SIZE) {
          compressedBuffer = jpegBuffer;
        }
      }

      const finalSize = compressedBuffer.length;
      const compressionRatio = ((originalSize - finalSize) / originalSize * 100).toFixed(1);
      
      console.log(`[ScreenshotHelper] 压缩完成: ${(originalSize / 1024 / 1024).toFixed(2)}MB -> ${(finalSize / 1024 / 1024).toFixed(2)}MB (压缩率: ${compressionRatio}%)`);

      return compressedBuffer;
    } catch (error) {
      console.error(`[ScreenshotHelper] 图片压缩失败:`, error);
      // 如果压缩失败，返回原图
      return buffer;
    }
  }

  public async takeScreenshot(
    hideMainWindow: () => void,
    showMainWindow: () => void,
    mainWindow: Electron.BrowserWindow
  ): Promise<string> {
    hideMainWindow()
    await new Promise((resolve) => setTimeout(resolve, 100))

    let screenshotPath = ""
    try {
      // Get screenshot buffer using native methods
      const screenshotBuffer =
        process.platform === "darwin"
          ? await this.captureScreenshotMac(mainWindow)
          : await this.captureScreenshotWindows(mainWindow)

      // 压缩图片（如果需要）
      const compressedBuffer = await this.compressImageIfNeeded(screenshotBuffer);

      // Save and manage the screenshot
      if (this.view === "queue") {
        screenshotPath = path.join(this.screenshotDir, `${uuidv4()}.png`)
        await fs.promises.writeFile(screenshotPath, compressedBuffer)

        this.screenshotQueue.push(screenshotPath)
        if (this.screenshotQueue.length > this.MAX_SCREENSHOTS) {
          const removedPath = this.screenshotQueue.shift()
          if (removedPath) {
            try {
              await fs.promises.unlink(removedPath)
            } catch (error) {
              console.error("Error removing old screenshot:", error)
            }
          }
        }
      } else {
        screenshotPath = path.join(this.extraScreenshotDir, `${uuidv4()}.png`)
        await fs.promises.writeFile(screenshotPath, compressedBuffer)

        this.extraScreenshotQueue.push(screenshotPath)
        if (this.extraScreenshotQueue.length > this.MAX_SCREENSHOTS) {
          const removedPath = this.extraScreenshotQueue.shift()
          if (removedPath) {
            try {
              await fs.promises.unlink(removedPath)
            } catch (error) {
              console.error("Error removing old screenshot:", error)
            }
          }
        }
      }
    } catch (error) {
      console.error("Screenshot error:", error)
      throw error
    } finally {
      await new Promise((resolve) => setTimeout(resolve, 50))
      showMainWindow()
    }

    return screenshotPath
  }

    // 添加区域截图方法
  public async captureRegionScreenshot(
    x: number,
    y: number,
    width: number,
    height: number,
    hideMainWindow: () => void,
    showMainWindow: () => void
  ): Promise<string> {
    hideMainWindow();
    await new Promise((resolve) => setTimeout(resolve, 100));

    let screenshotPath = "";
    try {
      // 获取截图缓冲区
      const screenshotBuffer = await this.captureRegion(x, y, width, height);

      // 压缩图片（如果需要）
      const compressedBuffer = await this.compressImageIfNeeded(screenshotBuffer);

      // 保存并管理截图
      screenshotPath = path.join(this.screenshotDir, `${uuidv4()}.png`);
      await fs.promises.writeFile(screenshotPath, compressedBuffer);

      if (this.screenshotQueue && this.screenshotQueue.length >= this.MAX_SCREENSHOTS) {
        // 清空队列
        for (const screenshotPath of this.screenshotQueue) {
          try {
            await fs.promises.unlink(screenshotPath);
          } catch (error) {
            console.error("Error removing old screenshot:", error);
          }
        }
        this.screenshotQueue = [];
      }
      this.screenshotQueue.push(screenshotPath);
    } catch (error) {
      console.error("Screenshot error:", error);
      throw error;
    } finally {
      await new Promise((resolve) => setTimeout(resolve, 50));
      showMainWindow();
    }

    return screenshotPath;
  }

  // 根据平台实现区域截图
  private async captureRegion(
    x: number,
    y: number,
    width: number,
    height: number
  ): Promise<Buffer> {
    if (process.platform === "darwin") {
      return this.captureRegionMac(x, y, width, height);
    } else {
      return this.captureRegionWindows(x, y, width, height);
    }
  }

  // Mac 平台区域截图
  private async captureRegionMac(
    x: number,
    y: number,
    width: number,
    height: number
  ): Promise<Buffer> {
    const tmpPath = path.join(app.getPath("temp"), `${uuidv4()}.png`);
    
    console.log(`[ScreenshotHelper] 请求区域截图: x=${x}, y=${y}, width=${width}, height=${height}`);
    
    try {
      // 确保宽度和高度是正数
      if (width <= 0 || height <= 0) {
        throw new Error("截图区域无效：宽度或高度不能为零或负数");
      }
      
      // 处理点击同一位置的情况
      if (width < 10 || height < 10) {
        throw new Error("截图区域太小：请选择更大的区域");
      }

      // 使用 screencapture 命令捕获指定区域
      // 注意: macOS screencapture 接受的参数格式为 x,y,width,height
      await execFileAsync("screencapture", [
        "-x",               // 无声截图
        "-R",               // 区域截图
        `${x},${y},${width},${height}`,
        tmpPath
      ]);
      
      // 检查文件是否存在且大小 > 0
      const stats = await fs.promises.stat(tmpPath);
      if (stats.size === 0) {
        console.error("[ScreenshotHelper] 截图失败: 生成的文件大小为0");
        throw new Error("截图失败: 文件大小为0");
      }
      
      console.log(`[ScreenshotHelper] 截图成功存储: ${tmpPath} (${stats.size} 字节)`);
      
      // 读取文件内容
      const buffer = await fs.promises.readFile(tmpPath);
      
      // 清理临时文件
      await fs.promises.unlink(tmpPath);
      
      return buffer;
    } catch (error) {
      console.error(`[ScreenshotHelper] 区域截图错误:`, error);
      
      // 如果临时文件存在，尝试删除
      try {
        if (fs.existsSync(tmpPath)) {
          await fs.promises.unlink(tmpPath);
        }
      } catch (unlinkError) {
        console.error("[ScreenshotHelper] 清理临时文件失败:", unlinkError);
      }
      
      throw error;
    }
  }

  // Windows 平台区域截图
  private async captureRegionWindows(
    x: number,
    y: number,
    width: number,
    height: number
  ): Promise<Buffer> {
    const tmpPath = path.join(app.getPath("temp"), `${uuidv4()}.png`);

    // 使用 PowerShell 的本地截图功能
    const script = `
      Add-Type -AssemblyName System.Windows.Forms
      Add-Type -AssemblyName System.Drawing
      $bounds = [System.Drawing.Rectangle]::FromLTRB(${x}, ${y}, ${x + width}, ${y + height})
      $bitmap = New-Object System.Drawing.Bitmap $bounds.Width, $bounds.Height
      $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
      $graphics.CopyFromScreen($bounds.Location, [System.Drawing.Point]::Empty, $bounds.Size)
      $bitmap.Save('${tmpPath.replace(/\\/g, "\\\\")}')
      $graphics.Dispose()
      $bitmap.Dispose()
    `;
    
    await execFileAsync("powershell", ["-command", script]);
    const buffer = await fs.promises.readFile(tmpPath);
    await fs.promises.unlink(tmpPath);
    return buffer;
  }



  public async getImagePreview(filepath: string): Promise<string> {
    try {
      const data = await fs.promises.readFile(filepath)
      return `data:image/png;base64,${data.toString("base64")}`
    } catch (error) {
      console.error("Error reading image:", error)
      throw error
    }
  }

  public async deleteScreenshot(
    path: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      await fs.promises.unlink(path)
      if (this.view === "queue") {
        this.screenshotQueue = this.screenshotQueue.filter(
          (filePath) => filePath !== path
        )
      } else {
        this.extraScreenshotQueue = this.extraScreenshotQueue.filter(
          (filePath) => filePath !== path
        )
      }
      return { success: true }
    } catch (error) {
      console.error("Error deleting file:", error)
      return { success: false, error: error.message }
    }
  }
}
