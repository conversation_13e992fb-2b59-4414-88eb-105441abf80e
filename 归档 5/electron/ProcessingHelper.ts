// ProcessingHelper.ts

import axios from "axios";
import { clipboard } from "electron";
import {
  debugSolutionResponses,
  generateSolutionByModel,
  generateScreenshotSolutionResponseNew
} from "./handlers/problemHandler";
import { AppState } from "./main";
import { ScreenshotHelper } from "./ScreenshotHelper";

export class ProcessingHelper {
  private appState: AppState
  private screenshotHelper: ScreenshotHelper

  // AbortControllers for API requests
  private currentProcessingAbortController: AbortController | null = null
  private currentExtraProcessingAbortController: AbortController | null = null
  
  // Flag to track if any processing is currently happening
  private isProcessing: boolean = false

  constructor(appState: AppState) {
    this.appState = appState
    this.screenshotHelper = appState.getScreenshotHelper()
  }
  
  // Method to check if processing is active
  public isProcessingActive(): boolean {
    return this.isProcessing
  }


  public async generateAndProcessScreenshotSolutionsNew(): Promise<void> {
    const mainWindow = this.appState.getMainWindow();
    if (!mainWindow) return;
  
    mainWindow.webContents.send(this.appState.PROCESSING_EVENTS.INITIAL_START);
    this.appState.setView("solutions");
  
    // Initialize AbortController
    this.currentProcessingAbortController = new AbortController();
    const { signal } = this.currentProcessingAbortController;
  
    try {
      this.isProcessing = true;
      const result = await this.generateScreenshotSolutionsHelperNew(signal);
  
      if (!result.success) {
        if (result.error?.includes("API Key out of credits")) {
          mainWindow.webContents.send(
            this.appState.PROCESSING_EVENTS.API_KEY_OUT_OF_CREDITS
          );
        } else if (
          result.error?.includes(
            "Please close this window and re-enter a valid Open AI API key."
          )
        ) {
          mainWindow.webContents.send(
            this.appState.PROCESSING_EVENTS.API_KEY_INVALID
          );
        } else {
          mainWindow.webContents.send(
            this.appState.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            result.error
          );
        }
      } else {
        mainWindow.webContents.send(
          this.appState.PROCESSING_EVENTS.SOLUTION_SUCCESS,
          result.data
        );
      }
    } catch (error: any) {
      if (axios.isCancel(error)) {
        mainWindow.webContents.send(
          this.appState.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
          "Processing was canceled by the user."
        );
      } else {
        mainWindow.webContents.send(
          this.appState.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
          error.message
        );
      }
    } finally {
      this.isProcessing = false;
      this.currentProcessingAbortController = null;
    }
  }

  public async processClipBorad(): Promise<void> {
    const mainWindow = this.appState.getMainWindow()
    if (!mainWindow) return


      mainWindow.webContents.send(this.appState.PROCESSING_EVENTS.INITIAL_START)
      this.appState.setView("solutions")

      // Initialize AbortController
      this.currentProcessingAbortController = new AbortController()

      try {
        this.isProcessing = true;
        const result = await this.processClipboardHelper()
        if (!result.success) {
          if (result.error?.includes("API Key out of credits")) {
            mainWindow.webContents.send(
              this.appState.PROCESSING_EVENTS.API_KEY_OUT_OF_CREDITS
            )
          } else if (
            result.error?.includes(
              "Please close this window and re-enter a valid Open AI API key."
            )
          ) {
            mainWindow.webContents.send(
              this.appState.PROCESSING_EVENTS.API_KEY_INVALID
            )
          } else {
            mainWindow.webContents.send(
              this.appState.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
              result.error
            )
          }
        }
      } catch (error: any) {
        if (axios.isCancel(error)) {
          mainWindow.webContents.send(
            this.appState.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            "Processing was canceled by the user."
          )
        } else {
          mainWindow.webContents.send(
            this.appState.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            error.message
          )
        }
      } finally {
        this.isProcessing = false;
        this.currentProcessingAbortController = null
      }
  }

  private async processClipboardHelper() {
    try {

      const mainWindow = this.appState.getMainWindow()
    
      if (mainWindow) {
        const solutionsResult = await this.generateClipBoardSolutionsHelper()
        if (solutionsResult.success) {
          mainWindow.webContents.send(
            this.appState.PROCESSING_EVENTS.SOLUTION_SUCCESS,
            solutionsResult.data
          )
        } else {
          throw new Error(
            solutionsResult.error || "Failed to generate solutions"
          )
        }
      }

      return { success: true, data: "" }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
    
  }

  public async generateScreenshotSolutionsHelperNew() {
    try {
      // 获取队列中的图片
      let screenshotQueue = this.screenshotHelper.getScreenshotQueue();
      if (screenshotQueue.length === 0) {
        throw new Error("No screenshots available")
      }

      const solutions = await generateScreenshotSolutionResponseNew(screenshotQueue)

      if (!solutions) {
        throw new Error("No solutions received")
      }

      return { success: true, data: solutions }
    } catch (error: any) {
      const mainWindow = this.appState.getMainWindow()

      // Check if error message indicates API key out of credits
      if (error.message?.includes("API Key out of credits")) {
        if (mainWindow) {
          mainWindow.webContents.send(
            this.appState.PROCESSING_EVENTS.API_KEY_OUT_OF_CREDITS
          )
        }
        return { success: false, error: error.message }
      }
      if (
        error.message?.includes(
          "Please close this window and re-enter a valid Open AI API key."
        )
      ) {
        if (mainWindow) {
          mainWindow.webContents.send(
            this.appState.PROCESSING_EVENTS.API_KEY_INVALID
          )
        }
        return { success: false, error: error.message }
      }

      return { success: false, error: error.message }
    }
  }


  private async generateClipBoardSolutionsHelper() {
    try {
      const problemInfo = clipboard.readText();
      if (!problemInfo) {
        throw new Error("No problem info available")
      }

      let solutions = await generateSolutionByModel(problemInfo)
 
      if (!solutions) {
        throw new Error("No solutions received")
      }

      return { success: true, data: solutions }
    } catch (error: any) {
      const mainWindow = this.appState.getMainWindow()

      // Check if error message indicates API key out of credits
      if (error.message?.includes("API Key out of credits")) {
        if (mainWindow) {
          mainWindow.webContents.send(
            this.appState.PROCESSING_EVENTS.API_KEY_OUT_OF_CREDITS
          )
        }
        return { success: false, error: error.message }
      }
      if (
        error.message?.includes(
          "Please close this window and re-enter a valid Open AI API key."
        )
      ) {
        if (mainWindow) {
          mainWindow.webContents.send(
            this.appState.PROCESSING_EVENTS.API_KEY_INVALID
          )
        }
        return { success: false, error: error.message }
      }

      return { success: false, error: error.message }
    }
  }

  private async processExtraScreenshotsHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  ) {
    try {
      const imageDataList = screenshots.map((screenshot) => screenshot.data)

      const problemInfo = this.appState.getProblemInfo()
      if (!problemInfo) {
        throw new Error("No problem info available")
      }

      // Use the debugSolutionResponses function
      const debugSolutions = await debugSolutionResponses(
        imageDataList,
        problemInfo
      )

      if (!debugSolutions) {
        throw new Error("No debug solutions received")
      }

      return { success: true, data: debugSolutions }
    } catch (error: any) {
      const mainWindow = this.appState.getMainWindow()

      // Check if error message indicates API key out of credits
      if (error.message?.includes("API Key out of credits")) {
        if (mainWindow) {
          mainWindow.webContents.send(
            this.appState.PROCESSING_EVENTS.API_KEY_OUT_OF_CREDITS
          )
        }
        return { success: false, error: error.message }
      }

      if (
        error.message?.includes(
          "Please close this window and re-enter a valid Open AI API key."
        )
      ) {
        if (mainWindow) {
          mainWindow.webContents.send(
            this.appState.PROCESSING_EVENTS.API_KEY_INVALID
          )
        }
        return { success: false, error: error.message }
      }
      return { success: false, error: error.message }
    }
  }

  public cancelOngoingRequests(): void {
    let wasCancelled = false

    if (this.currentProcessingAbortController) {
      this.currentProcessingAbortController.abort()
      this.currentProcessingAbortController = null

      wasCancelled = true
    }

    if (this.currentExtraProcessingAbortController) {
      this.currentExtraProcessingAbortController.abort()
      this.currentExtraProcessingAbortController = null

      wasCancelled = true
    }

    // Reset hasDebugged flag
    this.appState.setHasDebugged(false)

    const mainWindow = this.appState.getMainWindow()
    if (wasCancelled && mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send("Processing was canceled by the user.")
    }
  }
}
