import { screen } from 'electron';
import { AppState } from './main';
import { models, store, getCodeLanguages, getDefaultCodeLanguage } from "./store";
const ioHook = require('@mhgbrown/iohook');

// 添加工具栏区域类型定义
interface ToolbarRegion {
  x: number;
  y: number;
  width: number;
  height: number;
  type: 'model' | 'code-language' | 'reset' | 'other' | 'capture' | 'solution' | 'voice' | 'voice-back' | 'voice-microphone' | 'voice-system-audio' | 'voice-one-click-start' | 'voice-send-to-ai';
}

/**
 * Helper class to track mouse movements and handle screenshot functionality
 */
export class MouseTrackingHelper {
  private appState: AppState;
  private screenshotAvailable: boolean = false; // 是否有可用的截图
  
  // 记录窗口初始尺寸
  private initialWindowDimensions: { width: number; height: number } | null = null;
  
  // 工具条相关设置
  private toolbarBounds: { x: number; y: number; width: number; height: number } | null = null;
  private toolbarRegions: ToolbarRegion[] = []; // 工具条内部的不同区域
  private readonly DEFAULT_TOOLBAR_HEIGHT = 40; // 默认工具条高度（像素）
  private isShowingSelectionIndicator: boolean = false; // 修复缺失的属性
  private lastUpdateTime: number = 0;
  private readonly UPDATE_THROTTLE_MS: number = 100; // 限制更新频率为100ms一次

  // AI回复框位置信息
  private aiResponsePositions: {
    fastResponse?: { x: number; y: number; width: number; height: number; type: string };
    accurateResponse?: { x: number; y: number; width: number; height: number; type: string };
  } = {};

  // AI回复框箭头按钮位置信息
  private aiArrowPositions: {
    [key: string]: { x: number; y: number; width: number; height: number; type: string };
  } = {};

  constructor(appState: AppState) {
    this.appState = appState;
    this.startIoHook(); // 启动ioHook监听鼠标事件
    this.initCurModel(); // 初始化当前模型
  }


  public resetMouseTracking(): void {
    this.screenshotAvailable = false;
  }

  private initCurModel(): void {
    // 延迟初始化当前模型
    setTimeout(() => {
      const mainWindow = this.appState.getMainWindow();
      if (!mainWindow || mainWindow.isDestroyed()) {
        return;
      }
      // 使用类型断言解决linter错误
      const curModel = (store as any).get("curModel") || models[0];
      mainWindow.webContents.send("model-changed", {
        model: curModel,
        index: models.findIndex(model => model === curModel) + 1,
        total: models.length
      });
    }, 3000);
  }

  public startIoHook(): void {
    ioHook.start();
    this.setupMouseEvents();
    console.log('ioHook started');
  }
  
  public stopIoHook(): void {
    try {
      console.log('开始停止ioHook...');

      // 首先移除所有事件监听器
      ioHook.removeAllListeners();
      console.log('ioHook事件监听器已移除');

      // 然后停止ioHook
      ioHook.stop();
      console.log('ioHook.stop()调用完成');

      console.log('ioHook停止完成');
    } catch (error) {
      console.error('停止ioHook时出错:', error);
    }
  }

  /**
   * 异步停止ioHook，给予更多时间进行清理
   */
  public async stopIoHookAsync(): Promise<void> {
    return new Promise<void>((resolve) => {
      try {
        console.log('开始异步停止ioHook...');

        // 首先移除所有事件监听器
        ioHook.removeAllListeners();
        console.log('ioHook事件监听器已移除');

        // 停止ioHook
        ioHook.stop();
        console.log('ioHook.stop()调用完成');

        // 给ioHook一些时间来完全清理底层资源
        setTimeout(() => {
          console.log('ioHook异步停止完成');
          resolve();
        }, 300);

      } catch (error) {
        console.error('异步停止ioHook时出错:', error);
        resolve(); // 即使出错也要resolve，避免阻塞退出流程
      }
    });
  }
  
  /**
   * 设置鼠标事件监听
   */
  private setupMouseEvents(): void {
    // 处理鼠标点击事件
    ioHook.on('mouseclick', (event: any) => {
      console.log('🖱️ 检测到鼠标点击事件:', {
        x: event.x,
        y: event.y,
        button: event.button,
        clicks: event.clicks
      });

      // 首先检查是否点击了箭头按钮
      if (this.handleArrowButtonClick(event)) {
        return; // 如果点击了箭头按钮，不再处理其他点击
      }

      // 检查点击是否在窗口内，执行相应操作
      this.isPointInWindowBounds();
    });

    console.log('鼠标点击事件监听设置完成');
  }



  /**
   * 更新工具条位置信息
   * @param bounds 工具条的位置和尺寸
   * @returns 是否执行了实质性更新
   */
  public updateToolbarBounds(bounds: { x: number; y: number; width: number; height: number }): boolean {
    // 检查参数有效性，防止传入无效值
    if (!bounds || typeof bounds.x !== 'number' || typeof bounds.y !== 'number' || 
        typeof bounds.width !== 'number' || typeof bounds.height !== 'number') {
      console.error('无效的工具条位置信息:', bounds);
      return false;
    }
    
    // 检查是否是无效数据（在错误状态下可能发送的数据）
    if (bounds.width <= 0 || bounds.height <= 0) {
      console.debug('忽略无效的工具条尺寸:', bounds);
      return false;
    }
    
    // 记录上一次的位置，用于比较
    const previousBounds = this.toolbarBounds ? { ...this.toolbarBounds } : null;
    
    // 检查是否有实质性变化
    const hasSignificantChange = !previousBounds || 
        Math.abs(previousBounds.x - bounds.x) > 1 || 
        Math.abs(previousBounds.y - bounds.y) > 1 ||
        Math.abs(previousBounds.width - bounds.width) > 1 ||
        Math.abs(previousBounds.height - bounds.height) > 1;
        
    // 检查是否需要节流更新（避免短时间内多次更新）
    const now = Date.now();
    const shouldThrottle = (now - this.lastUpdateTime) < this.UPDATE_THROTTLE_MS;
    
    // 只有实质性变化且不需要节流时才更新
    if (hasSignificantChange && !shouldThrottle) {
      // 更新工具条位置
      this.toolbarBounds = bounds;
      this.lastUpdateTime = now;
      
      // 输出日志
      console.log('工具条位置已更新:', this.toolbarBounds);
      return true;
    } else if (shouldThrottle && hasSignificantChange) {
      console.debug('工具条位置更新被节流');
    } else if (!hasSignificantChange) {
      console.debug('工具条位置无实质性变化，忽略更新');
    }
    
    return false;
  }

  /**
   * 更新工具条内特定区域的位置信息
   * @param regions 工具条内部的不同区域
   */
  public updateToolbarRegions(regions: ToolbarRegion[]): void {
    this.toolbarRegions = regions;
    console.log('📍 工具条区域已更新:', {
      regionsCount: regions.length,
      regions: regions.map(r => ({ type: r.type, x: r.x, y: r.y, width: r.width, height: r.height }))
    });

    // 特别检查语音面板按钮
    const voiceRegions = regions.filter(r => r.type.startsWith('voice-'));
    if (voiceRegions.length > 0) {
      console.log('🎤 语音面板按钮区域:', voiceRegions);
    }
  }

  /**
   * 检查点击点是否在工具条范围内，并根据点击的区域执行相应操作
   * @returns 是否在工具条范围内
   */
  private isPointInWindowBounds(): boolean {
    const mainWindow = this.appState.getMainWindow();
    if (!mainWindow || mainWindow.isDestroyed()) {
      console.log('❌ 主窗口不可用或已销毁');
      return false;
    }

    // 获取当前鼠标屏幕坐标
    const point = screen.getCursorScreenPoint();
    const windowBounds = mainWindow.getBounds();

    console.log('🖱️ 鼠标点击检测:', {
      mousePosition: point,
      windowBounds: windowBounds,
      toolbarBounds: this.toolbarBounds,
      regionsCount: this.toolbarRegions.length
    });

    // 使用从渲染进程获取的工具条位置（如果有）
    // 如果没有收到工具条位置，则使用默认计算方式
    let effectiveToolbarBounds;
    
    if (this.toolbarBounds && this.toolbarBounds.width > 0 && this.toolbarBounds.height > 0) {
      // 使用从渲染进程报告的工具条位置
      // 但需要加上窗口的偏移，因为渲染进程中的坐标是相对于窗口的
      effectiveToolbarBounds = {
        x: windowBounds.x + this.toolbarBounds.x,
        y: windowBounds.y + this.toolbarBounds.y,
        width: this.toolbarBounds.width,
        height: this.toolbarBounds.height
      };
      
      // 日志输出，方便调试
      console.log('使用渲染进程报告的工具条位置:', effectiveToolbarBounds);
    } else {
      // 使用默认计算方式
      effectiveToolbarBounds = {
        x: windowBounds.x,
        y: windowBounds.y,
        width: windowBounds.width,
        height: this.DEFAULT_TOOLBAR_HEIGHT
      };
      
      console.log('使用默认工具条位置:', effectiveToolbarBounds);
    }

    // 检查点是否在工具条范围内
    const isInToolbar = (
      point.x >= effectiveToolbarBounds.x && 
      point.x <= effectiveToolbarBounds.x + effectiveToolbarBounds.width && 
      point.y >= effectiveToolbarBounds.y && 
      point.y <= effectiveToolbarBounds.y + effectiveToolbarBounds.height
    );
    
    // 如果点击在工具条范围内，检查是否在特定区域内
    if (isInToolbar) {
      console.log(`鼠标位置 (${point.x}, ${point.y}) 在工具条范围内`);
      
      // 如果有定义区域信息，判断点击区域类型
      if (this.toolbarRegions.length > 0) {
        // 遍历所有区域，检查点击点是否在某个特定区域内
        for (const region of this.toolbarRegions) {
          // 计算区域的绝对屏幕坐标
          const absoluteRegion = {
            x: windowBounds.x + this.toolbarBounds!.x + region.x,
            y: windowBounds.y + this.toolbarBounds!.y + region.y,
            width: region.width,
            height: region.height,
            type: region.type
          };
          
          // 检查点击点是否在此区域内
          const isInRegion = (
            point.x >= absoluteRegion.x && 
            point.x <= absoluteRegion.x + absoluteRegion.width && 
            point.y >= absoluteRegion.y && 
            point.y <= absoluteRegion.y + absoluteRegion.height
          );
          
          if (isInRegion) {
            console.log(`点击在 ${region.type} 区域内`);
            
            // 根据区域类型执行不同操作
            switch (region.type) {
              case 'model':
                // 执行模型切换操作
                this.handleModelClick(mainWindow);
                break;
              case 'code-language':
                // 执行编程语言切换操作
                this.handleCodeLanguageClick(mainWindow);
                break;
              case 'reset':
                // 执行重置操作
                this.handleResetClick(mainWindow);
                break;
              case 'capture':
                // 执行全屏截图操作
                this.handleCaptureClick(mainWindow);
                break;
              case 'solution':
                // 执行解决方案生成操作
                this.handleSolutionClick(mainWindow);
                break;
              case 'voice':
                // 执行语音助手操作
                this.handleVoiceClick(mainWindow);
                break;
              case 'voice-back':
                // 执行返回操作
                this.handleVoiceBackClick(mainWindow);
                break;
              case 'voice-microphone':
                // 执行麦克风操作
                this.handleVoiceMicrophoneClick(mainWindow);
                break;
              case 'voice-system-audio':
                // 执行系统音频操作
                this.handleVoiceSystemAudioClick(mainWindow);
                break;
              case 'voice-one-click-start':
                // 执行一键启动操作
                this.handleVoiceOneClickStartClick(mainWindow);
                break;
              case 'voice-send-to-ai':
                // 执行发送到AI操作
                this.handleVoiceSendToAIClick(mainWindow);
                break;
              default:
                // 其他区域不执行特殊操作
                break;
            }
            
            return true; // 点击在特定区域内，处理完成
          }
        }
        
        // 点击在工具条内但不在任何特定区域内
        console.log('点击在工具条内，但不在任何特定区域内');
        return true;
      }
      else {
        // 没有区域信息时，默认执行模型切换（兼容原有行为）
        this.handleModelClick(mainWindow);
      }
    } else {
      console.log(`鼠标位置 (${point.x}, ${point.y}) 不在工具条范围内`);
    }
    
    return isInToolbar;
  }

  /**
   * 处理模型区域的点击，切换模型
   */
  public switchModel(): void {
    const mainWindow = this.appState.getMainWindow();
    if (mainWindow && !mainWindow.isDestroyed()) {
      this.handleModelClick(mainWindow);
    }
  }

  /**
   * 处理模型区域的点击，切换模型
   */
  private handleModelClick(mainWindow: Electron.BrowserWindow): void {
    // 获取当前模型
    // 使用类型断言解决linter错误
    const curModel = (store as any).get("curModel") || models[0];
    let nextModelIndex = 0;

    // 找到当前模型的索引
    const currentIndex = models.findIndex(model => model === curModel);
    if (currentIndex !== -1) {
      // 计算下一个模型的索引（循环）
      nextModelIndex = (currentIndex + 1) % models.length;
    }

    // 设置新的当前模型
    const nextModel = models[nextModelIndex];
    // 使用类型断言解决linter错误
    (store as any).set("curModel", nextModel);
    console.log(`模型已切换到: ${nextModel} (模型区域点击)`);

    // 向渲染进程发送模型变更消息，以便在UI中显示
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send("model-changed", {
        model: nextModel,
        index: nextModelIndex + 1,
        total: models.length
      });
    }
  }

  /**
   * 处理编程语言区域的点击，切换编程语言
   */
  private handleCodeLanguageClick(mainWindow: Electron.BrowserWindow): void {
    // 获取可用的编程语言列表
    const codeLanguages = getCodeLanguages();

    // 获取当前编程语言
    const curCodeLanguage = (store as any).get("curCodeLanguage") || getDefaultCodeLanguage();
    let nextLanguageIndex = 0;

    // 找到当前编程语言的索引
    const currentIndex = codeLanguages.findIndex(lang => lang === curCodeLanguage);
    if (currentIndex !== -1) {
      // 计算下一个编程语言的索引（循环）
      nextLanguageIndex = (currentIndex + 1) % codeLanguages.length;
    }

    // 设置新的当前编程语言
    const nextCodeLanguage = codeLanguages[nextLanguageIndex];
    // 使用类型断言解决linter错误
    (store as any).set("curCodeLanguage", nextCodeLanguage);
    console.log(`编程语言已切换到: ${nextCodeLanguage} (编程语言区域点击)`);

    // 向渲染进程发送编程语言变更消息，以便在UI中显示
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send("code-language-changed", {
        codeLanguage: nextCodeLanguage,
        index: nextLanguageIndex + 1,
        total: codeLanguages.length
      });
    }
  }

  /**
   * 处理Reset按钮的点击，重置界面
   */
  private handleResetClick(mainWindow: Electron.BrowserWindow): void {
    if (!this.appState.isVisible()) return;
      console.log(
        "Command + R pressed. Canceling requests and resetting queues..."
      )

      // 检查并重置窗口位置（如果在屏幕外）
      this.appState.windowHelper.resetWindowPosition();

      // Cancel ongoing API requests
      this.appState.processingHelper.cancelOngoingRequests()

      // Clear both screenshot queues
      this.appState.clearQueues()

      // Update the view state to 'queue'
      this.appState.setView("queue")

      this.resetMouseTracking()
    
      // 向渲染进程发送重置消息
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send("reset-view");
      }
  }

  /**
   * 处理Capture区域的点击，执行全屏截图
   */
  private handleCaptureClick(mainWindow: Electron.BrowserWindow): void {
    console.log('Capture区域被点击，执行全屏截图');
    
    // 检查是否已经有处理中的请求
    if (this.appState.processingHelper.isProcessingActive()) {
      console.log("已有处理中的请求，忽略当前点击");
      return;
    }
    
    // 调用全屏截图方法
    // 需要通过 IPC 通知渲染进程，因为截图过程中将会隐藏窗口
    mainWindow.webContents.send("taking-screenshot");
    
    // 全屏截图
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width, height } = primaryDisplay.bounds;

    let physicalWidth = width;
    let physicalHeight = height;

    // 如果是win电脑
    if (process.platform === 'win32') {
      const scaleFactor = primaryDisplay.scaleFactor; // 获取缩放比例
      physicalWidth = Math.round(width * scaleFactor);
      physicalHeight = Math.round(height * scaleFactor);
    }
  
    
    // 使用区域截图方法，参数为全屏尺寸
    this.appState.takeRegionScreenshot(0, 0, physicalWidth, physicalHeight)
      .then(screenshotPath => {
        // 获取图片预览
        return this.appState.getImagePreview(screenshotPath)
          .then(preview => {
            // 发送截图结果到渲染进程
            mainWindow.webContents.send("screenshot-taken", {
              path: screenshotPath,
              preview
            });
            
            // 设置截图可用状态为true
            this.setScreenshotAvailable(true);
          });
      })
      .catch(error => {
        console.error("全屏截图错误:", error);
        mainWindow.webContents.send("screenshot-error", { error: error.message });
      });
  }

  /**
   * 处理Solution区域的点击，执行解决方案生成
   */
  private handleSolutionClick(mainWindow: Electron.BrowserWindow): void {
    console.log('Solution区域被点击，执行解决方案生成');
    
    // 检查是否已经有处理中的请求
    if (this.appState.processingHelper.isProcessingActive()) {
      console.log("已有处理中的请求，忽略当前点击");
      return;
    }
    
    // 检查是否有可用的截图
    if (this.appState.getScreenshotQueue().length === 0) {
      console.log("没有可用的截图，无法生成解决方案");
      // 通过 IPC 通知渲染进程没有可用的截图
      mainWindow.webContents.send("processing-no-screenshots");
      return;
    }
    
    // 生成解决方案
    console.log("开始生成解决方案...");
    this.appState.processingHelper.generateAndProcessScreenshotSolutionsNew();
  }

  /**
   * 处理Voice区域的点击，执行语音助手功能
   */
  private handleVoiceClick(mainWindow: Electron.BrowserWindow): void {
    console.log('Voice区域被点击，显示语音识别界面');

    if (!mainWindow || mainWindow.isDestroyed()) {
      console.error('主窗口不可用，无法显示语音识别界面');
      return;
    }

    // 通知前端切换到语音视图
    mainWindow.webContents.send('navigate-to-voice-view');
    this.appState.setView('voice'); // 更新应用状态为语音视图（这会自动更新鼠标事件处理）

    // 显示语音识别界面
    mainWindow.webContents.send('toggle-voice-assistant', {
      asrConnected: false,
      recording: false,
      status: 'show-ui-only',
      autoStart: false
    });

  }

  /**
   * Check if screenshot is available
   */
  public hasScreenshot(): boolean {
    return this.screenshotAvailable;
  }

  /**
   * Set screenshot availability status
   */
  public setScreenshotAvailable(available: boolean): void {
    console.log(`设置截图可用状态: ${available}`);
    this.screenshotAvailable = available;
  }

  /**
   * 清除截图选择状态
   */
  public clearScreenshotSelection(): void {
    this.isShowingSelectionIndicator = false;
    
    const mainWindow = this.appState.getMainWindow();
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send("screenshot-cancelled");
    }
    
    console.log("截图选择状态已清除");
  }

  /**
   * 处理语音面板返回按钮的点击
   */
  private handleVoiceBackClick(mainWindow: Electron.BrowserWindow): void {
    console.log('语音面板返回按钮被点击');

    if (!mainWindow || mainWindow.isDestroyed()) {
      console.error('主窗口不可用，无法处理返回操作');
      return;
    }

    // 通知前端执行返回操作
    mainWindow.webContents.send('voice-back-clicked');
  }

  /**
   * 处理语音面板麦克风按钮的点击
   */
  private handleVoiceMicrophoneClick(mainWindow: Electron.BrowserWindow): void {
    console.log('语音面板麦克风按钮被点击');

    if (!mainWindow || mainWindow.isDestroyed()) {
      console.error('主窗口不可用，无法处理麦克风操作');
      return;
    }

    // 通知前端执行麦克风操作
    mainWindow.webContents.send('voice-microphone-clicked');
  }

  /**
   * 处理语音面板系统音频按钮的点击
   */
  private handleVoiceSystemAudioClick(mainWindow: Electron.BrowserWindow): void {
    console.log('语音面板系统音频按钮被点击');

    if (!mainWindow || mainWindow.isDestroyed()) {
      console.error('主窗口不可用，无法处理系统音频操作');
      return;
    }

    // 通知前端执行系统音频操作
    mainWindow.webContents.send('voice-system-audio-clicked');
  }

  /**
   * 处理语音面板一键启动按钮的点击
   */
  private handleVoiceOneClickStartClick(mainWindow: Electron.BrowserWindow): void {
    console.log('语音面板一键启动按钮被点击');

    if (!mainWindow || mainWindow.isDestroyed()) {
      console.error('主窗口不可用，无法处理一键启动操作');
      return;
    }

    // 通知前端执行一键启动操作
    mainWindow.webContents.send('voice-one-click-start-clicked');
  }

  /**
   * 处理语音面板发送到AI按钮的点击
   */
  private handleVoiceSendToAIClick(mainWindow: Electron.BrowserWindow): void {
    console.log('语音面板发送到AI按钮被点击');

    if (!mainWindow || mainWindow.isDestroyed()) {
      console.error('主窗口不可用，无法处理发送到AI操作');
      return;
    }

    // 通知前端执行发送到AI操作
    mainWindow.webContents.send('voice-send-to-ai-clicked');
  }

  /**
   * 更新AI回复框位置信息
   */
  public updateAIResponsePositions(positions: {
    fastResponse?: { x: number; y: number; width: number; height: number; type: string };
    accurateResponse?: { x: number; y: number; width: number; height: number; type: string };
  }): void {
    this.aiResponsePositions = positions;
    console.log('📍 更新AI回复框位置信息:', this.aiResponsePositions);
  }

  /**
   * 更新AI回复框箭头按钮位置信息
   */
  public updateAIArrowPositions(positions: {
    [key: string]: { x: number; y: number; width: number; height: number; type: string };
  }): void {
    this.aiArrowPositions = positions;
    console.log('📍 更新AI箭头按钮位置信息:', Object.keys(positions).length, '个按钮');
  }

  /**
   * 处理箭头按钮点击
   */
  private handleArrowButtonClick(event: any): boolean {
    const point = { x: event.x, y: event.y };
    const mainWindow = this.appState.getMainWindow();

    if (!mainWindow || mainWindow.isDestroyed()) {
      return false;
    }

    // 检查是否点击了任何箭头按钮
    for (const [key, position] of Object.entries(this.aiArrowPositions)) {
      // 使用屏幕绝对坐标进行检查
      if (this.isPointInBounds(point, position)) {
        console.log(`🖱️ ✅ 点击了箭头按钮: ${key}`);

        // 根据按钮类型执行相应的滚动操作
        if (position.type.includes('up-arrow')) {
          const type = position.type.includes('fast') ? 'fast' : 'accurate';
          console.log(`🔼 执行${type}模式向上滚动`);
          mainWindow.webContents.send('ai-response-scroll', {
            type: type,
            scrollData: {
              deltaY: -350, // 向上滚动，增加滚动距离
              deltaX: 0
            }
          });
        } else if (position.type.includes('down-arrow')) {
          const type = position.type.includes('fast') ? 'fast' : 'accurate';
          console.log(`🔽 执行${type}模式向下滚动`);
          mainWindow.webContents.send('ai-response-scroll', {
            type: type,
            scrollData: {
              deltaY: 350, // 向下滚动，增加滚动距离
              deltaX: 0
            }
          });
        }

        return true; // 表示处理了箭头按钮点击
      }
    }

    return false; // 没有点击箭头按钮
  }

  /**
   * 检查点是否在指定边界内
   */
  private isPointInBounds(point: { x: number; y: number }, bounds: { x: number; y: number; width: number; height: number }): boolean {
    return point.x >= bounds.x &&
           point.x <= bounds.x + bounds.width &&
           point.y >= bounds.y &&
           point.y <= bounds.y + bounds.height;
  }
}