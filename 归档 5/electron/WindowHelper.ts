// electron/WindowHelper.ts

import { BrowserWindow, screen } from "electron";
import path from "node:path";
import { AppState } from "./main";
import { store } from "./store";

const isDev = process.env.NODE_ENV === "development"
const isMac = process.platform === "darwin"

const startUrl = isDev
  ? "http://localhost:5173"
  : `file://${path.join(__dirname, "../dist/index.html")}`

export class WindowHelper {
  private mainWindow: BrowserWindow | null = null
  private isWindowVisible: boolean = false
  private windowPosition: { x: number; y: number } | null = null
  private windowSize: { width: number; height: number } | null = null
  private appState: AppState

  // Initialize with explicit number type and 0 value
  private screenWidth: number = 0
  private screenHeight: number = 0
  private step: number = 0
  private currentX: number = 0
  private currentY: number = 0

  // Add this property to track focus
  private wasFocused: boolean = false

  constructor(appState: AppState) {
    this.appState = appState
  }

  public setWindowDimensions(width: number, height: number): void {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) return

    // Get current window position
    const [currentX, currentY] = this.mainWindow.getPosition()

    // Get screen dimensions
    const primaryDisplay = screen.getPrimaryDisplay()
    const workArea = primaryDisplay.workAreaSize
    const scaleFactor = primaryDisplay.scaleFactor || 1
    const isWindows = process.platform === 'win32'

    // Use 90% width if debugging has occurred, otherwise use 80%
    const maxAllowedWidth = Math.floor(
      workArea.width * (this.appState.getHasDebugged() ? 0.8 : 0.7)
    )

    // Add extra padding for Windows with high DPI scaling
    let extraPadding = 32
    if (isWindows && scaleFactor > 1) {
      // 根据缩放比例增加额外的填充，确保在高DPI下有足够空间
      extraPadding = Math.ceil(32 * (scaleFactor + 0.5))
    }

    // Ensure width doesn't exceed max allowed width and height is reasonable
    const newWidth = Math.min(width + extraPadding, maxAllowedWidth)
    const newHeight = Math.ceil(height)

    // Center the window horizontally if it would go off screen
    const maxX = workArea.width - newWidth
    const newX = Math.min(Math.max(currentX, 0), maxX)

    // Update window bounds
    this.mainWindow.setBounds({
      x: newX,
      y: currentY,
      width: newWidth,
      height: newHeight
    })

    // Update internal state
    this.windowPosition = { x: newX, y: currentY }
    this.windowSize = { width: newWidth, height: newHeight }
    this.currentX = newX
  }

  public createWindow(): void {
    if (this.mainWindow !== null) return

    const primaryDisplay = screen.getPrimaryDisplay()
    const workArea = primaryDisplay.workAreaSize
    this.screenWidth = workArea.width
    this.screenHeight = workArea.height

    this.step = Math.floor(this.screenWidth / 50) // 50 steps
    this.currentX = 0 // Start at the left

    const windowSettings: Electron.BrowserWindowConstructorOptions = {
      height: 600,
      minWidth: undefined,
      maxWidth: undefined,
      x: this.currentX,
      y: 0,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: true,
        preload: path.join(__dirname, "preload.js")
      },
      show: true,
      frame: false,
      transparent: true,
      fullscreenable: false,
      hasShadow: false,
      backgroundColor: "#00000000",
      focusable: true,
      alwaysOnTop: true,
      skipTaskbar: true,
      enableLargerThanScreen: true,
      movable: true,
      // 添加以下设置以禁用滚动条
      useContentSize: true,  // 使用内容尺寸而不是窗口尺寸
      resizable: false       // 禁用窗口调整大小
    }

    this.mainWindow = new BrowserWindow(windowSettings)

    this.mainWindow.setContentProtection(true)

    // Check if API key exists in store
    const apiKey = store.get('openaiApiKey')
    if (!apiKey) {
      this.mainWindow.setIgnoreMouseEvents(false)
    } else {
      this.mainWindow.setIgnoreMouseEvents(true)
    }

    // Only call macOS specific methods if running on macOS
    if (isMac) {
      this.mainWindow.setHiddenInMissionControl(true)
      this.mainWindow.setVisibleOnAllWorkspaces(true, {
        visibleOnFullScreen: true
      })
    }

    this.mainWindow.setAlwaysOnTop(true, "screen-saver", 1)

    this.mainWindow.loadURL(startUrl).catch((err) => {
      console.error("Failed to load URL:", err)
    })

    const bounds = this.mainWindow.getBounds()
    this.windowPosition = { x: bounds.x, y: bounds.y }
    this.windowSize = { width: bounds.width, height: bounds.height }
    this.currentX = bounds.x
    this.currentY = bounds.y

    this.setupWindowListeners()
    this.isWindowVisible = true

    // Open DevTools if in development mode
    if (isDev) {
      // this.mainWindow.webContents.openDevTools();
    }
    
  }

  private setupWindowListeners(): void {
    if (!this.mainWindow) return

    this.mainWindow.on("move", () => {
      if (this.mainWindow) {
        const bounds = this.mainWindow.getBounds()
        this.windowPosition = { x: bounds.x, y: bounds.y }
        this.currentX = bounds.x
        this.currentY = bounds.y
      }
    })

    this.mainWindow.on("resize", () => {
      if (this.mainWindow) {
        const bounds = this.mainWindow.getBounds()
        this.windowSize = { width: bounds.width, height: bounds.height }
      }
    })

    this.mainWindow.on("closed", () => {
      this.mainWindow = null
      this.isWindowVisible = false
      this.windowPosition = null
      this.windowSize = null
    })
  }

  public getMainWindow(): BrowserWindow | null {
    return this.mainWindow
  }

  public isVisible(): boolean {
    return this.isWindowVisible
  }

  public hideMainWindow(): void {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) {
      console.warn("Main window does not exist or is destroyed.")
      return
    }

    // Store focus state before hiding
    this.wasFocused = this.mainWindow.isFocused()

    const bounds = this.mainWindow.getBounds()
    this.windowPosition = { x: bounds.x, y: bounds.y }
    this.windowSize = { width: bounds.width, height: bounds.height }
    
    // Hide the window
    this.mainWindow.hide()
    this.isWindowVisible = false
  }

  /**
   * Set whether the window should ignore mouse events
   * @param value - True to ignore mouse events, false to handle them
   */
  public setIgnoreMouseEvents(value: boolean): void {
    const mainWindow = this.getMainWindow();
    if (mainWindow) {
      mainWindow.setIgnoreMouseEvents(value);
    }
  }

  public showMainWindow(): void {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) {
      console.warn("Main window does not exist or is destroyed.")
      return
    }

    const focusedWindow = BrowserWindow.getFocusedWindow()

    if (this.windowPosition && this.windowSize) {
      this.mainWindow.setBounds({
        x: this.windowPosition.x,
        y: this.windowPosition.y,
        width: this.windowSize.width,
        height: this.windowSize.height
      })
    }

    if (!store.get("openaiApiKey")) {
      this.mainWindow.setIgnoreMouseEvents(false)
    } else {
      this.mainWindow.setIgnoreMouseEvents(true)
    }
    this.mainWindow.setVisibleOnAllWorkspaces(true, {
      visibleOnFullScreen: true
    })
    this.mainWindow.setAlwaysOnTop(true, "screen-saver", 1)
    this.mainWindow.setContentProtection(true)
    this.mainWindow.setOpacity(1)

    this.mainWindow.showInactive()

    if (focusedWindow && !focusedWindow.isDestroyed()) {
      focusedWindow.focus()
    }

    this.isWindowVisible = true
    

  }

  public toggleMainWindow(): void {
    if (this.isWindowVisible) {
      this.hideMainWindow()
      this.appState.mouseTrackingHelper.stopIoHook()
    } else {
      this.showMainWindow()
      this.appState.mouseTrackingHelper.startIoHook()
    }
  }

  // New methods for window movement
  public moveWindowRight(): void {
    if (!this.mainWindow) return

    const windowWidth = this.windowSize?.width || 0
    const halfWidth = windowWidth / 5

    // Ensure currentX and currentY are numbers
    this.currentX = Number(this.currentX) || 0
    this.currentY = Number(this.currentY) || 0

    this.currentX = Math.min(
      this.screenWidth - halfWidth,
      this.currentX + this.step
    )
    this.mainWindow.setPosition(
      Math.round(this.currentX),
      Math.round(this.currentY)
    )
    
    // Emit event for window moved
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send("window-moved");
    }
  }

  public moveWindowLeft(): void {
    if (!this.mainWindow) return

    const windowWidth = this.windowSize?.width || 0
    const halfWidth = windowWidth / 5

    // Ensure currentX and currentY are numbers
    this.currentX = Number(this.currentX) || 0
    this.currentY = Number(this.currentY) || 0

    this.currentX = Math.max(-halfWidth, this.currentX - this.step)
    this.mainWindow.setPosition(
      Math.round(this.currentX),
      Math.round(this.currentY)
    )
    
    // Emit event for window moved
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send("window-moved");
    }
  }

  public moveWindowDown(): void {
    if (!this.mainWindow) return;

    this.currentY = Number(this.currentY) || 0;
    this.currentY += this.step;

    this.mainWindow.setPosition(
      Math.round(this.currentX),
      Math.round(this.currentY)
    );
    
    // Emit event for window moved
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send("window-moved");
    }
  }

  public moveWindowUp(): void {
    if (!this.mainWindow) return;
    this.currentY = Number(this.currentY) || 0;
    this.currentY -= this.step;

    this.mainWindow.setPosition(
      Math.round(this.currentX),
      Math.round(this.currentY)
    );
    
    // Emit event for window moved
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send("window-moved");
    }
  }

  public resetWindowPosition(): void {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) return;
    
    // 获取主屏幕信息
    const primaryDisplay = screen.getPrimaryDisplay();
    const workArea = primaryDisplay.workAreaSize;
    
    // 获取当前窗口位置
    const [currentX, currentY] = this.mainWindow.getPosition();
    
    // 检查窗口是否在屏幕外或部分在屏幕外
    const isOffScreen = 
      currentX < 0 || 
      currentY < 0 || 
      currentX > workArea.width || 
      currentY > workArea.height;
    
    if (isOffScreen) {
      // 如果窗口在屏幕外，重置到左上角位置
      this.currentX = 20; // 留一点边距
      this.currentY = 20; // 留一点边距
      
      this.mainWindow.setPosition(
        Math.round(this.currentX),
        Math.round(this.currentY)
      );
      
      console.log("Window was off-screen and has been reset to top-left position");
    }
  }
}
