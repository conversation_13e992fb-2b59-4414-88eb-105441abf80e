import { app, globalShortcut, screen } from "electron";
import { AppState } from "./main"; // Adjust the import path if necessary
import { globalKeyboardHook } from "./keyboard-hook";


export class ShortcutsHelper {
  private appState: AppState
  private registeredShortcuts: string[] = [];

  // 添加存储鼠标位置的变量
  private firstPoint: { x: number; y: number } | null = null;

  constructor(appState: AppState) {
    this.appState = appState
    this.registerShortcuts();
    this.setupKeyboardInterception();
    this.setupSystemLevelInterception();

    // 输出日志，确认ShortcutsHelper被正确初始化
    console.log("ShortcutsHelper initialized");
  }

  private registerShortcuts() {
    try {
      // 清除之前注册的所有快捷键
      this.unregisterAllShortcuts();

      // 注册复制内容的快捷键
      this.registerShortcut('Alt+C', () => {
        // 如果视图是 voice，则不处理
        if (this.appState.getView() === "voice") {
          console.log("当前视图为 voice，跳过剪贴板处理");
          return;
        }
        console.log("Alt+C pressed. Copying content...");
        const mainWindow = this.appState.getMainWindow();
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send("copy-solution-content");
        }
      });

      // 注册一键启动/关闭快捷键
      this.registerShortcut('Alt+V', () => {
        // 只在 voice 视图时处理
        if (this.appState.getView() !== "voice") {
          console.log("当前视图不是 voice，跳过一键启动/关闭处理");
          return;
        }
        console.log("Alt+V pressed. 触发一键启动/关闭...");
        const mainWindow = this.appState.getMainWindow();
        if (mainWindow && !mainWindow.isDestroyed()) {
          // 复用 MouseTrackingHelper 中的事件发送机制
          mainWindow.webContents.send('voice-one-click-start-clicked');
        }
      });

      // 注册请求AI快捷键
      this.registerShortcut('Alt+A', () => {
        // 只在 voice 视图时处理
        if (this.appState.getView() !== "voice") {
          console.log("当前视图不是 voice，跳过请求AI处理");
          return;
        }
        console.log("Alt+A pressed. 触发请求AI...");
        const mainWindow = this.appState.getMainWindow();
        if (mainWindow && !mainWindow.isDestroyed()) {
          // 复用 MouseTrackingHelper 中的事件发送机制
          mainWindow.webContents.send('voice-send-to-ai-clicked');
        }
      });

      globalShortcut.register("CommandOrControl+R", () => {
        if (!this.appState.isVisible()) return;
        // 如果视图是 voice，则不处理
        if (this.appState.getView() === "voice") {
          console.log("当前视图为 voice，跳过剪贴板处理");
          return;
        }
        console.log(
          "Command + R pressed. Canceling requests and resetting queues..."
        )

        // 检查并重置窗口位置（如果在屏幕外）
        this.appState.windowHelper.resetWindowPosition();

        // Cancel ongoing API requests
        this.appState.processingHelper.cancelOngoingRequests()

        // Clear both screenshot queues
        this.appState.clearQueues()

        // Update the view state to 'queue'
        this.appState.setView("queue")

        this.appState.mouseTrackingHelper.resetMouseTracking()

        this.firstPoint = null; // 重置第一个点

        // Notify renderer process to switch view to 'queue'
        const mainWindow = this.appState.getMainWindow()
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send("reset-view")
        }
      })

      // New shortcuts for moving the window
      this.registerShortcut("CommandOrControl+Left", () => {
        if (!this.appState.isVisible()) return;
        console.log("Command/Ctrl + Left pressed. Moving window left.")
        this.appState.moveWindowLeft()
      })

      this.registerShortcut("CommandOrControl+Right", () => {
        if (!this.appState.isVisible()) return;
        console.log("Command/Ctrl + Right pressed. Moving window right.")
        this.appState.moveWindowRight()
      })
      this.registerShortcut("CommandOrControl+Down", () => {
        if (!this.appState.isVisible()) return;
        console.log("Command/Ctrl + down pressed. Moving window down.")
        this.appState.moveWindowDown()
      })
      this.registerShortcut("CommandOrControl+Up", () => {
        if (!this.appState.isVisible()) return;
        console.log("Command/Ctrl + Up pressed. Moving window Up.")
        this.appState.moveWindowUp()
      })

      this.registerShortcut("CommandOrControl+B", () => {
        this.appState.toggleMainWindow()
      })


      // deepseek r1 模型
      this.registerShortcut('Alt+Z', () => {
        // 延迟读取剪贴板中的文本，确保系统复制操作完成
        if (!this.appState.isVisible()) return;

        // 如果视图是 voice，则不处理
        if (this.appState.getView() === "voice") {
          console.log("当前视图为 voice，跳过剪贴板处理");
          return;
        }

        this.appState.processingHelper.processClipBorad();
      });

      // 模型切换快捷键
      this.registerShortcut('Alt+2', () => {
        if (!this.appState.isVisible()) return;

        // 如果视图是 voice，则不处理
        if (this.appState.getView() === "voice") {
          console.log("当前视图为 voice，跳过剪贴板处理");
          return;
        }

        console.log("Alt+2 pressed. Switching model...");
        // 调用 MouseTrackingHelper 中的模型切换方法
        this.appState.mouseTrackingHelper.switchModel();
      });

      // 识别图片 chat - 修改快捷键格式，移除内部空格
      this.registerShortcut("Alt+3", async () => {
        if (!this.appState.isVisible()) return;

        // 如果视图是 voice，则不处理
        if (this.appState.getView() === "voice") {
          console.log("当前视图为 voice，跳过剪贴板处理");
          return;
        }
        console.log("Alt+Shift+1 pressed. Taking screenshot...")

           // 检查是否有可用的截图
        if (this.appState.getScreenshotQueue().length === 0) {
          const mainWindow = this.appState.getMainWindow();
          // 通过 IPC 通知渲染进程没有可用的截图
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send("processing-no-screenshots");
          }
          return;
        }

        // 检查是否已经有处理中的请求
        if (!this.appState.processingHelper.isProcessingActive()) {
          await this.appState.processingHelper.generateAndProcessScreenshotSolutionsNew()
        }
        
      })

      // 合并 Alt+1 和 Alt+2 功能为单个快捷键 Alt+1
      this.registerShortcut('Alt+1', async () => { 
        if (!this.appState.isVisible()) return;
        // 如果视图是 voice，则不处理
        if (this.appState.getView() === "voice") {
          console.log("当前视图为 voice，跳过剪贴板处理");
          return;
        }
        // 获取当前鼠标屏幕坐标
        const cursorPoint = screen.getCursorScreenPoint();
        
        // 获取鼠标所在的显示器
        const display = screen.getDisplayNearestPoint(cursorPoint);
        
        // 如果没有第一个点，则记录第一个点（左上角）
        if (!this.firstPoint) {
          // 将相对于操作系统的绝对坐标存储为第一个点
          this.firstPoint = cursorPoint;
          
          console.log("记录第一个点（左上角）:", this.firstPoint);
          console.log("显示器信息:", {
            id: display.id,
            bounds: display.bounds,
            workArea: display.workArea,
            scaleFactor: display.scaleFactor
          });
          
          // 通知用户已记录第一个点
          const mainWindow = this.appState.getMainWindow();
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send("first-point-recorded");
          }
        } 
        // 如果已有第一个点，则记录第二个点并截图
        else {
          const secondPoint = cursorPoint;
          console.log("记录第二个点（右下角）:", secondPoint);
        
          const mainWindow = this.appState.getMainWindow();
          if (mainWindow) {
            try {
              // 如果是win电脑
              if (process.platform === 'win32') {
                // 获取主显示器信息
                const primaryDisplay = screen.getPrimaryDisplay();
                const scaleFactor = primaryDisplay.scaleFactor; // 获取缩放比例
                console.log("屏幕缩放比例:", scaleFactor);
                
                // 确保坐标正确（左上角坐标小于右下角坐标）
                const x1 = Math.min(this.firstPoint.x, secondPoint.x);
                const y1 = Math.min(this.firstPoint.y, secondPoint.y);
                const x2 = Math.max(this.firstPoint.x, secondPoint.x);
                const y2 = Math.max(this.firstPoint.y, secondPoint.y);
          
                // 计算宽度和高度
                const width = x2 - x1;
                const height = y2 - y1;
          
                console.log("逻辑截图区域：", x1, y1, width, height);
          
                // 如果宽度或高度为0，则不截图
                if (width <= 0 || height <= 0) {
                  console.log("截图区域无效，宽度或高度为0");
                  this.firstPoint = null; // 重置第一个点
                  return;
                }
          
                // 将逻辑坐标转换为物理像素坐标
                const physicalX1 = Math.round(x1 * scaleFactor);
                const physicalY1 = Math.round(y1 * scaleFactor);
                const physicalWidth = Math.round(width * scaleFactor);
                const physicalHeight = Math.round(height * scaleFactor);
          
                console.log("物理截图区域：", physicalX1, physicalY1, physicalWidth, physicalHeight);
          
                // 调用截图方法
                const screenshotPath = await this.appState.takeRegionScreenshot(
                  physicalX1,
                  physicalY1,
                  physicalWidth,
                  physicalHeight
                );
                const preview = await this.appState.getImagePreview(screenshotPath);
          
                // 发送截图结果到渲染进程
                mainWindow.webContents.send("screenshot-taken", {
                  path: screenshotPath,
                  preview
                });
                
                // 启用鼠标跟踪和点击检测
                this.appState.mouseTrackingHelper.setScreenshotAvailable(true);
          
                // 重置第一个点
                this.firstPoint = null;
              } else {
                // 确保坐标正确（左上角坐标小于右下角坐标）
                const x1 = Math.min(this.firstPoint.x, secondPoint.x);
                const y1 = Math.min(this.firstPoint.y, secondPoint.y);
                const x2 = Math.max(this.firstPoint.x, secondPoint.x);
                const y2 = Math.max(this.firstPoint.y, secondPoint.y);
                
                // 计算宽度和高度
                const width = x2 - x1;
                const height = y2 - y1;

                console.log("截图区域：", x1, y1, width, height);
                
                // 如果宽度或高度为0，则不截图
                if (width <= 0 || height <= 0) {
                  console.log("截图区域无效，宽度或高度为0");
                  this.firstPoint = null; // 重置第一个点
                  return;
                }
                
                // 调用截图方法
                const screenshotPath = await this.appState.takeRegionScreenshot(x1, y1, width, height);
                const preview = await this.appState.getImagePreview(screenshotPath);
                
                // 发送截图结果到渲染进程
                mainWindow.webContents.send("screenshot-taken", {
                  path: screenshotPath,
                  preview
                });
                
                // 启用鼠标跟踪和点击检测
                this.appState.mouseTrackingHelper.setScreenshotAvailable(true);
                
                // 重置第一个点
                this.firstPoint = null;
              }
            } catch (error) {
              console.error("区域截图错误:", error);
              this.firstPoint = null; // 发生错误时也重置第一个点
            }
          }
        }
      });

      this.registerShortcut("CommandOrControl+Q", () => {
        console.log('Ctrl+Q 触发退出');
        app.quit(); // 退出应用
      })

      console.log("所有快捷键注册成功");
    } catch (error) {
      console.error("注册快捷键失败:", error);
    }
  }

  private registerShortcut(accelerator: string, callback: () => void) {
    try {
      // 先尝试注销该快捷键，避免重复注册
      if (globalShortcut.isRegistered(accelerator)) {
        globalShortcut.unregister(accelerator);
      }

      const success = globalShortcut.register(accelerator, () => {
        // 执行回调函数
        callback();

        // 阻止事件继续传播到其他应用程序
        // 通过返回 false 或不返回任何值来阻止默认行为
        return false;
      });

      if (success) {
        this.registeredShortcuts.push(accelerator);
        console.log(`快捷键 ${accelerator} 注册成功，已阻止事件传播`);
      } else {
        console.error(`快捷键 ${accelerator} 注册失败`);
      }
    } catch (error) {
      console.error(`注册快捷键 ${accelerator} 时发生错误:`, error);
    }
  }

  private unregisterAllShortcuts() {
    try {
      // 注销之前注册的所有快捷键
      this.registeredShortcuts.forEach(shortcut => {
        if (globalShortcut.isRegistered(shortcut)) {
          globalShortcut.unregister(shortcut);
          console.log(`快捷键 ${shortcut} 已注销`);
        }
      });
      
      // 清空已注册快捷键列表
      this.registeredShortcuts = [];
      
      // 为确保所有快捷键都被注销，调用globalShortcut.unregisterAll()
      globalShortcut.unregisterAll();
      console.log("所有快捷键已注销");
    } catch (error) {
      console.error("注销快捷键失败:", error);
    }
  }

  public dispose() {
    this.unregisterAllShortcuts();
    // 停用系统级键盘钩子
    globalKeyboardHook.deactivate();
  }

  /**
   * 设置键盘事件拦截，阻止其他程序捕获
   */
  private setupKeyboardInterception() {
    const mainWindow = this.appState.getMainWindow();
    if (mainWindow && !mainWindow.isDestroyed()) {
      // 在渲染进程中拦截键盘事件
      mainWindow.webContents.on('before-input-event', (event, input) => {
        // 检查是否是我们注册的快捷键
        const isOurShortcut = this.isRegisteredShortcut(input);
        if (isOurShortcut) {
          console.log(`拦截键盘事件: ${input.key} (${input.modifiers.join('+')}) - 阻止传播到其他程序`);
          // 阻止事件传播
          event.preventDefault();
        }
      });
    }
  }

  /**
   * 设置系统级别的键盘拦截
   */
  private setupSystemLevelInterception() {
    try {
      // 激活全局键盘钩子
      globalKeyboardHook.activate();

      // 添加我们要拦截的快捷键
      const shortcutsToIntercept = [
        'Alt+C', 'Alt+Z', 'Alt+1', 'Alt+2', 'Alt+3', 'Alt+V', 'Alt+A',
        'CommandOrControl+R', 'CommandOrControl+Left', 'CommandOrControl+Right',
        'CommandOrControl+Down', 'CommandOrControl+Up', 'CommandOrControl+B',
        'CommandOrControl+Q'
      ];

      shortcutsToIntercept.forEach(shortcut => {
        globalKeyboardHook.addHookedKey(shortcut);
      });

      console.log("系统级键盘拦截已设置");
    } catch (error) {
      console.error("设置系统级键盘拦截失败:", error);
    }
  }

  /**
   * 检查输入是否是我们注册的快捷键
   */
  private isRegisteredShortcut(input: any): boolean {
    // 构建快捷键字符串
    const modifiers = input.modifiers || [];
    const key = input.key;

    // 检查常见的快捷键组合
    const shortcuts = [
      'Alt+C', 'Alt+Z', 'Alt+1', 'Alt+2', 'Alt+3',
      'CommandOrControl+R', 'CommandOrControl+Left', 'CommandOrControl+Right',
      'CommandOrControl+Down', 'CommandOrControl+Up', 'CommandOrControl+B',
      'CommandOrControl+Q'
    ];

    // 简单的匹配逻辑
    for (const shortcut of shortcuts) {
      if (this.matchesShortcut(shortcut, modifiers, key)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 匹配快捷键
   */
  private matchesShortcut(shortcut: string, modifiers: string[], key: string): boolean {
    const parts = shortcut.split('+');
    const shortcutKey = parts[parts.length - 1].toLowerCase();
    const shortcutModifiers = parts.slice(0, -1).map(m => m.toLowerCase());

    // 检查按键是否匹配
    if (key.toLowerCase() !== shortcutKey) {
      return false;
    }

    // 检查修饰键是否匹配
    const inputModifiers = modifiers.map(m => m.toLowerCase());

    for (const mod of shortcutModifiers) {
      if (mod === 'commandorcontrol') {
        if (process.platform === 'darwin') {
          if (!inputModifiers.includes('cmd') && !inputModifiers.includes('meta')) {
            return false;
          }
        } else {
          if (!inputModifiers.includes('ctrl') && !inputModifiers.includes('control')) {
            return false;
          }
        }
      } else if (mod === 'alt') {
        if (!inputModifiers.includes('alt')) {
          return false;
        }
      }
    }

    return true;
  }

  /**
   * 注册全局快捷键
   */
  public registerGlobalShortcuts(): void {
    console.log("重新注册全局快捷键");
    this.registerShortcuts();
    this.setupKeyboardInterception();
  }
}
