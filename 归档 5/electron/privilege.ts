import { app, desktopCapturer, dialog, shell, systemPreferences } from 'electron';
import { exec } from 'node:child_process';
import * as os from 'node:os';
import * as path from 'node:path';
import { promisify } from 'node:util';

const execAsync = promisify(exec);

// 定义平台类型
type Platform = 'darwin' | 'win32';

// 错误信息常量
const ERROR_MESSAGES = {
  PERMISSION_TIMEOUT: '检查权限超时',
  UNSUPPORTED_PLATFORM: (platform: string) => 
    `不支持的操作系统平台: ${platform}. 目前仅支持 macOS (darwin) 和 Windows (win32).`
};

// 权限状态接口
interface PermissionStatus {
  accessibility: boolean;
  microphone: boolean;
  screen: boolean;
}

/**
 * 获取录音程序的二进制文件路径
 * @returns 二进制文件的完整路径
 */
export function getRecorderBinaryPath(): string {
  const baseDir = app.isPackaged
    ? process.resourcesPath
    : app.getAppPath();

  const currentPlatform = os.platform() as Platform;
  
  const platformConfig: Record<Platform, { dir: string; binary: string }> = {
    'win32': { dir: 'windows', binary: 'audio_capture_cli.exe' },
    'darwin': { dir: 'macos', binary: 'system-audio-capture' }
  };
  
  const config = platformConfig[currentPlatform];
  
  if (!config) {
    console.error(`不支持的平台: ${currentPlatform}`);
    throw new Error(ERROR_MESSAGES.UNSUPPORTED_PLATFORM(currentPlatform));
  }
  
  return path.join(baseDir, 'bin', config.dir, config.binary);
}

/**
 * 检查录音/屏幕录制权限
 * @returns 是否有录音/屏幕录制权限
 */
async function checkRecordingPermissions(): Promise<boolean> {
  const platform = process.platform as Platform;
  
  // Windows 平台不需要检查权限
  if (platform === 'win32') {
    return true;
  }
  
  // macOS 平台检查
  if (platform === 'darwin') {
    // 检查 macOS 版本，对于 10.14 (Mojave) 及更早版本，无需进行权限检查
    const version = os.release();
    const majorVersion = Number.parseInt(version.split('.')[0]);
    console.log('macOS 版本:', majorVersion);
    
    if (majorVersion <= 18) { // 10.14 的 macOS 版本是 18
      return true;
    }
    
    // 对于 macOS 10.15+ 执行权限检查
    try {
      const binaryPath = getRecorderBinaryPath();
      
      // 创建带超时的执行承诺
      const execPromise = execAsync(`${binaryPath} --check-permissions`);
      const timeoutPromise = new Promise<never>((_, reject) => 
        setTimeout(() => reject(new Error(ERROR_MESSAGES.PERMISSION_TIMEOUT)), 5000)
      );

      // 等待命令执行或超时
      const { stdout } = await Promise.race([execPromise, timeoutPromise]);
      const { code } = JSON.parse(stdout);
      
      return code === "PERMISSION_GRANTED";
    } catch (error) {
      console.error("检查权限失败:", error instanceof Error ? error.message : String(error));
      return false;
    }
  }
  
  // 不支持的平台
  console.warn(`不支持的平台 ${platform}，默认返回 false`);
  return false;
}

/**
 * 请求应用所需的系统权限
 * 包括辅助功能、麦克风和屏幕录制权限
 */
export async function requestPermissions(): Promise<void> {
  if (process.platform === 'darwin') {
    await requestMacOSPermissions();
  } else if (process.platform === 'win32') {
    // Windows 目前不需要实现特定的权限请求
    // 如果将来需要，可以取消下面的注释
    // await requestWindowsPermissions();
  }
}

/**
 * 请求 macOS 平台所需的权限
 */
async function requestMacOSPermissions(): Promise<void> {
  try {
    console.log('开始请求 macOS 权限...');
    
    // 检查辅助功能权限
    if (!systemPreferences.isTrustedAccessibilityClient(false)) {
      console.log('请求辅助功能权限...');
      systemPreferences.isTrustedAccessibilityClient(true);
      shell.openExternal('x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility');
    }
    
    // 检查麦克风权限
    let microphoneStatus = systemPreferences.getMediaAccessStatus('microphone');
    console.log('当前麦克风权限状态:', microphoneStatus);
    
    if (microphoneStatus !== 'granted') {
      try {
        console.log('请求麦克风权限...');
        const granted = await systemPreferences.askForMediaAccess('microphone');
        microphoneStatus = granted ? 'granted' : systemPreferences.getMediaAccessStatus('microphone');
        console.log('麦克风权限请求结果:', microphoneStatus);
      } catch (error) {
        console.error('请求麦克风权限失败:', error);
        
        // 如果直接请求失败，引导用户手动授权
        if (microphoneStatus !== 'granted') {
          console.log('引导用户手动授权麦克风权限');
          shell.openExternal('x-apple.systempreferences:com.apple.preference.security?Privacy_Microphone');
        }
      }
    }
    
    // 检查屏幕录制权限（用于系统音频捕获）
    let screenCaptureStatus = systemPreferences.getMediaAccessStatus('screen');
    console.log('当前屏幕录制权限状态:', screenCaptureStatus);
    
    if (screenCaptureStatus !== 'granted') {
      try {
        console.log('请求屏幕录制权限...');
        // 注意：macOS 不允许直接请求屏幕录制权限，需要用户手动授权
        console.log('引导用户手动授权屏幕录制权限');
        shell.openExternal('x-apple.systempreferences:com.apple.preference.security?Privacy_ScreenCapture');
      } catch (error) {
        console.error('处理屏幕录制权限时出错:', error);
      }
    }
    
    console.log('macOS 权限检查完成');
  } catch (error) {
    console.error('请求 macOS 权限时出错:', error);
  }
}

/**
 * Windows平台请求权限（目前注释掉，将来可能会使用）
 */
async function requestWindowsPermissions(): Promise<void> {
  const response = await dialog.showMessageBox({
    message: '需要麦克风和屏幕录制权限才能正常使用。请在系统设置中手动授予权限。',
    buttons: ['打开系统设置', '取消']
  });
  
  if (response.response === 0) {
    // 打开系统设置 (Windows)
    shell.openExternal('ms-settings:privacy-microphone');
    shell.openExternal('ms-settings:privacy-screenrecording');
  }
}

/**
 * 检查是否已授予所有必要权限
 * @returns 包含各权限状态的对象
 */
export async function checkPermissions(): Promise<PermissionStatus> {
  const result: PermissionStatus = {
    accessibility: false,
    microphone: false,
    screen: false
  };

  if (process.platform === 'darwin') {
    // 检查辅助功能权限
    result.accessibility = systemPreferences.isTrustedAccessibilityClient(false);
    
    // 检查麦克风权限
    result.microphone = systemPreferences.getMediaAccessStatus('microphone') === 'granted';
    
    // 检查屏幕录制权限
    result.screen = await checkRecordingPermissions();
  } else if (process.platform === 'win32') {
    // Windows默认假设权限已授予
    result.accessibility = true;
    result.microphone = true;
    result.screen = true;
  }

  return result;
}