/**
 * systemAudioHandlers.ts
 * 系统音频相关的IPC处理器
 */

import { BrowserWindow, ipcMain } from 'electron';
import { systemAudioManager } from '../handlers/SystemAudioManager';

/**
 * 注册系统音频相关的IPC处理器
 * @param mainWindow - 主窗口实例
 */
export function registerSystemAudioHandlers(mainWindow: BrowserWindow): void {
  // 设置最大监听器数量，避免内存泄漏警告
  mainWindow.webContents.setMaxListeners(20); // 增加最大监听器数量
  
  // 设置主窗口引用
  systemAudioManager.setMainWindow(mainWindow);
  
  // 在注册新处理器之前，尝试移除现有的处理器
  try {
    ipcMain.removeHandler('system-audio:start-capturing');
    console.log('移除已存在的system-audio:start-capturing处理器');
  } catch (e) {
    // 忽略错误，如果处理器不存在
  }
  
  try {
    ipcMain.removeHandler('system-audio:stop-capturing');
    console.log('移除已存在的system-audio:stop-capturing处理器');
  } catch (e) {
    // 忽略错误，如果处理器不存在
  }
  
  try {
    ipcMain.removeHandler('system-audio:get-status');
    console.log('移除已存在的system-audio:get-status处理器');
  } catch (e) {
    // 忽略错误，如果处理器不存在
  }
  
  // 启动系统音频捕获
  ipcMain.handle('system-audio:start-capturing', async () => {
    console.log('IPC: 收到启动系统音频捕获请求');
    try {
      const result = await systemAudioManager.startCapturing();
      return result;
    } catch (error) {
      console.error('启动系统音频捕获失败:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '启动系统音频捕获失败' 
      };
    }
  });
  
  // 停止系统音频捕获
  ipcMain.handle('system-audio:stop-capturing', async () => {
    console.log('IPC: 收到停止系统音频捕获请求');
    try {
      await systemAudioManager.stopCapturing();
      return { success: true };
    } catch (error) {
      console.error('停止系统音频捕获失败:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '停止系统音频捕获失败' 
      };
    }
  });
  
  // 获取系统音频捕获状态
  ipcMain.handle('system-audio:get-status', () => {
    return {
      capturing: systemAudioManager.isCapturing()
    };
  });

  // 获取系统音频进程诊断信息
  ipcMain.handle('system-audio:get-diagnostics', async () => {
    try {
      return systemAudioManager.getProcessDiagnostics();
    } catch (error) {
      console.error('获取系统音频诊断信息失败:', error);
      return {
        error: error instanceof Error ? error.message : '获取诊断信息失败'
      };
    }
  });

  // 获取进程终止日志
  ipcMain.handle('system-audio:get-termination-log', async () => {
    try {
      return systemAudioManager.getProcessTerminationLog();
    } catch (error) {
      console.error('获取进程终止日志失败:', error);
      return [];
    }
  });

  console.log('系统音频相关的IPC处理器已注册');
}