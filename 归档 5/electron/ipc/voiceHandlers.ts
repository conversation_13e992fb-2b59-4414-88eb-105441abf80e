import { BrowserWindow, ipcMain } from 'electron';
import { microphoneManager } from '../handlers/MicrophoneManager';
import { processAccurateVoiceText, processFastVoiceText } from '../handlers/problemHandler';
import { systemAudioManager } from '../handlers/SystemAudioManager';

// 已注册的处理程序集合，用于避免重复注册
const registeredHandlers = new Set<string>();

// 存储主窗口引用
let mainWindowRef: BrowserWindow | null = null;

/**
 * 安全注册 IPC 处理程序，防止重复注册
 */
function safeRegisterHandler(channel: string, handler: (...args: any[]) => Promise<any> | any): void {
  if (registeredHandlers.has(channel)) {
    try {
      ipcMain.removeHandler(channel);
    } catch (error) {
      console.log(`无法移除已存在的处理程序 ${channel}:`, error);
    }
  }
  
  ipcMain.handle(channel, handler);
  registeredHandlers.add(channel);
}

/**
 * 注册与语音助手相关的所有 IPC 处理程序
 */
export function setupVoiceHandlers(mainWindow: BrowserWindow): void {
  console.log('注册语音助手 IPC 处理程序...');
  
  // 保存主窗口引用
  mainWindowRef = mainWindow;

  // 一键启动语音识别处理器
  safeRegisterHandler("voice-recognition:start", async (_event, startMicrophone = false) => {
    console.log(`一键启动语音识别服务${startMicrophone ? '（包含麦克风）' : ''}`);
    
    try {
      // 使用新的流程启动接口
      const result = await startVoiceRecognitionFlow({
        enableSystemAudio: true,
        enableMicrophone: startMicrophone
      });
      
      return result;
      
    } catch (error) {
      console.error('启动语音识别流程失败:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '启动语音识别流程失败' 
      };
    }
  });

  // 优化的语音识别启动处理器 - 支持独立启动系统音频和麦克风
  safeRegisterHandler("voice-recognition:start-flow", async (_event, options: { 
    enableSystemAudio?: boolean, 
    enableMicrophone?: boolean 
  } = {}) => {
    return startVoiceRecognitionFlow(options);
  });

  // 停止语音识别流程 - 重构版本
  safeRegisterHandler("voice-recognition:stop", async (_event, options: {
    stopSystemAudio?: boolean,
    stopMicrophone?: boolean
  } = {}) => {
    const { stopSystemAudio = true, stopMicrophone = true } = options;
    console.log(`🛑 收到停止语音识别流程请求: 系统音频=${stopSystemAudio}, 麦克风=${stopMicrophone}`);

    return connectionManager.ensureSequentialInitialization(async () => {
      try {
        // 步骤1: 停止系统音频相关服务
        if (stopSystemAudio) {
          console.log('🔇 停止系统音频服务...');

          try {
            await systemAudioManager.stopCapturing();
            connectionManager.setConnectionState('systemAudio', false);
            console.log('✅ 系统音频捕获已停止');
          } catch (error) {
            console.error('❌ 停止系统音频捕获失败:', error);
          }

          try {
            const systemASRManager = await getSystemASRManager();
            await systemASRManager.stopASRSession();
            connectionManager.setConnectionState('systemASR', false);
            console.log('✅ 系统音频ASR连接已关闭');
          } catch (error) {
            console.error('❌ 关闭系统音频ASR连接失败:', error);
          }
        }

        // 步骤2: 停止麦克风相关服务
        if (stopMicrophone) {
          console.log('🎤 停止麦克风服务...');

          try {
            await microphoneManager.stopCapturing();
            connectionManager.setConnectionState('microphone', false);
            console.log('✅ 麦克风捕获已停止');
          } catch (error) {
            console.error('❌ 停止麦克风捕获失败:', error);
          }

          try {
            const microphoneASRManager = await getMicrophoneASRManager();
            await microphoneASRManager.stopASRSession();
            connectionManager.setConnectionState('microphoneASR', false);
            console.log('✅ 麦克风ASR连接已关闭');
          } catch (error) {
            console.error('❌ 关闭麦克风ASR连接失败:', error);
          }
        }

        console.log('📊 停止后连接状态:', connectionManager.getAllConnectionStates());

        // 通知前端状态
        const window = getMainWindow();
        if (window) {
          window.webContents.send('toggle-voice-assistant', {
            asrConnected: false,
            recording: false,
            status: 'inactive',
            systemAudioActive: false,
            microphoneActive: false
          });
        }

        return { success: true };
      } catch (error) {
        console.error('💥 停止语音识别服务异常:', error);

        // 异常情况下强制重置连接状态
        connectionManager.reset();

        return {
          success: false,
          error: error instanceof Error ? error.message : '停止语音识别服务异常'
        };
      }
    });
  });

  // 处理语音文本，返回AI回复
  safeRegisterHandler('process-voice-to-text', async (_event, text: string) => {
    try {
      if (!text || typeof text !== 'string') {
        return {
          success: false,
          error: '无效的文本输入',
          response: '请提供有效的文本'
        };
      }

      // 使用快速模式处理文本（实际应用中，这里应该选择合适的模式）
      const response = await processFastVoiceText(text);
      
      return {
        success: true,
        response
      };
    } catch (error: any) {
      console.error('处理语音文本时出错:', error);
      return {
        success: false,
        error: error.message || '处理语音文本时出错',
        response: '处理失败，请重试。'
      };
    }
  });

  // 处理极速模式语音文本
  safeRegisterHandler('process-fast-voice-text', async (_event, text: string) => {
    try {
      if (!text || typeof text !== 'string') {
        return {
          success: false,
          error: '无效的文本输入',
          response: '请提供有效的文本'
        };
      }

      const response = await processFastVoiceText(text);
      
      return {
        success: true,
        response
      };
    } catch (error: any) {
      console.error('处理极速模式语音文本时出错:', error);
      return {
        success: false,
        error: error.message || '处理语音文本时出错',
        response: '处理失败，请重试。'
      };
    }
  });

  // 处理极速模式语音文本（流式）
  safeRegisterHandler('process-fast-voice-text-stream', async (event, text: string) => {
    try {
      if (!text || typeof text !== 'string') {
        return {
          success: false,
          error: '无效的文本输入',
          response: '请提供有效的文本'
        };
      }

      // 生成一个唯一的响应ID
      const responseId = Date.now().toString();
      
      // 开始处理，并在每个响应块准备好时通过事件通知前端
      await processFastVoiceText(text, (chunk, done) => {
        // 通过事件发送响应块到前端
        event.sender.send('fast-voice-text-chunk', {
          id: responseId,
          chunk,
          done
        });
      });
      
      return {
        success: true,
        responseId
      };
    } catch (error: any) {
      console.error('处理流式极速模式语音文本时出错:', error);
      // 通知前端出错
      event.sender.send('fast-voice-text-chunk', {
        id: Date.now().toString(),
        chunk: `处理失败，请重试: ${error.message || '未知错误'}`,
        done: true,
        error: true
      });
      
      return {
        success: false,
        error: error.message || '处理语音文本时出错',
        response: '处理失败，请重试。'
      };
    }
  });

  // 处理精确模式语音文本
  safeRegisterHandler('process-accurate-voice-text', async (_event, text: string) => {
    try {
      if (!text || typeof text !== 'string') {
        return {
          success: false,
          error: '无效的文本输入',
          response: '请提供有效的文本'
        };
      }

      const response = await processAccurateVoiceText(text);
      
      return {
        success: true,
        response
      };
    } catch (error: any) {
      console.error('处理精确模式语音文本时出错:', error);
      return {
        success: false,
        error: error.message || '处理语音文本时出错',
        response: '处理失败，请重试。'
      };
    }
  });

  // 处理精确模式语音文本（流式）
  safeRegisterHandler('process-accurate-voice-text-stream', async (event, text: string) => {
    try {
      if (!text || typeof text !== 'string') {
        return {
          success: false,
          error: '无效的文本输入',
          response: '请提供有效的文本'
        };
      }

      // 生成一个唯一的响应ID
      const responseId = Date.now().toString();
      
      // 开始处理，并在每个响应块准备好时通过事件通知前端
      await processAccurateVoiceText(text, (chunk, done) => {
        // 通过事件发送响应块到前端
        event.sender.send('accurate-voice-text-chunk', {
          id: responseId,
          chunk,
          done
        });
      });
      
      return {
        success: true,
        responseId
      };
    } catch (error: any) {
      console.error('处理流式精确模式语音文本时出错:', error);
      // 通知前端出错
      event.sender.send('accurate-voice-text-chunk', {
        id: Date.now().toString(),
        chunk: `处理失败，请重试: ${error.message || '未知错误'}`,
        done: true,
        error: true
      });
      
      return {
        success: false,
        error: error.message || '处理语音文本时出错',
        response: '处理失败，请重试。'
      };
    }
  });
}

/**
 * 获取主窗口实例
 */
function getMainWindow(): BrowserWindow | null {
  // 首先尝试使用存储的引用
  if (mainWindowRef && !mainWindowRef.isDestroyed()) {
    return mainWindowRef;
  }
  
  // 如果没有有效的引用，尝试获取当前焦点窗口
  const focusedWindow = BrowserWindow.getFocusedWindow();
  if (focusedWindow) {
    return focusedWindow;
  }
  
  // 如果没有焦点窗口，尝试获取所有窗口并返回第一个
  const allWindows = BrowserWindow.getAllWindows();
  if (allWindows.length > 0) {
    return allWindows[0];
  }
  
  // 如果没有任何窗口，返回null
  return null;
}

/**
 * 连接状态管理器
 */
class VoiceConnectionManager {
  private static instance: VoiceConnectionManager | null = null;
  private isInitializing = false;
  private connectionStates = {
    systemASR: false,
    microphoneASR: false,
    systemAudio: false,
    microphone: false
  };
  private initializationPromise: Promise<any> | null = null;

  static getInstance(): VoiceConnectionManager {
    if (!VoiceConnectionManager.instance) {
      VoiceConnectionManager.instance = new VoiceConnectionManager();
    }
    return VoiceConnectionManager.instance;
  }

  async ensureSequentialInitialization<T>(operation: () => Promise<T>): Promise<T> {
    if (this.initializationPromise) {
      console.log('等待之前的初始化完成...');
      await this.initializationPromise;
    }

    this.initializationPromise = operation();
    try {
      const result = await this.initializationPromise;
      return result;
    } finally {
      this.initializationPromise = null;
    }
  }

  setConnectionState(service: keyof typeof this.connectionStates, connected: boolean): void {
    this.connectionStates[service] = connected;
    console.log(`连接状态更新: ${service} = ${connected}`, this.connectionStates);
  }

  getConnectionState(service: keyof typeof this.connectionStates): boolean {
    return this.connectionStates[service];
  }

  getAllConnectionStates() {
    return { ...this.connectionStates };
  }

  reset(): void {
    this.connectionStates = {
      systemASR: false,
      microphoneASR: false,
      systemAudio: false,
      microphone: false
    };
    this.isInitializing = false;
    this.initializationPromise = null;
  }
}

const connectionManager = VoiceConnectionManager.getInstance();

/**
 * 统一的语音识别启动流程 - 重构版本
 */
async function startVoiceRecognitionFlow(options: {
  enableSystemAudio?: boolean,
  enableMicrophone?: boolean
} = {}): Promise<{
  success: boolean,
  systemAudio?: { success: boolean, error?: string },
  microphone?: { success: boolean, error?: string },
  error?: string
}> {
  const { enableSystemAudio = true, enableMicrophone = false } = options;
  console.log(`🚀 执行语音识别流程启动: 系统音频=${enableSystemAudio}, 麦克风=${enableMicrophone}`);

  return connectionManager.ensureSequentialInitialization(async () => {
    try {
      let systemAudioResult: { success: boolean; error?: string } = { success: true };
      let microphoneResult: { success: boolean; error?: string } = { success: true };

      // 步骤1: 启动系统音频ASR连接（如果需要）
      if (enableSystemAudio) {
        console.log('📡 步骤1: 启动系统音频ASR连接...');

        // 检查是否已经连接
        if (connectionManager.getConnectionState('systemASR')) {
          console.log('✅ 系统音频ASR已连接，跳过');
          systemAudioResult = { success: true };
        } else {
          try {
            const systemASRManager = await getSystemASRManager();
            const asrResult = await systemASRManager.startASRSession();

            if (asrResult.success) {
              connectionManager.setConnectionState('systemASR', true);
              console.log('✅ 系统音频ASR连接成功');

              // 等待连接稳定
              await new Promise(resolve => setTimeout(resolve, 1000));

              // 启动系统音频捕获
              console.log('📡 启动系统音频捕获...');
              systemAudioResult = await systemAudioManager.startCapturing();

              if (systemAudioResult.success) {
                connectionManager.setConnectionState('systemAudio', true);
                console.log('✅ 系统音频捕获启动成功');
              } else {
                console.error('❌ 系统音频捕获启动失败:', systemAudioResult.error);
              }
            } else {
              console.error('❌ 系统音频ASR连接失败:', asrResult.error);
              systemAudioResult = { success: false, error: asrResult.error };
            }
          } catch (error) {
            console.error('❌ 系统音频ASR启动异常:', error);
            systemAudioResult = {
              success: false,
              error: error instanceof Error ? error.message : '系统音频ASR启动异常'
            };
          }
        }
      }

      // 步骤2: 启动麦克风ASR连接（如果需要）
      if (enableMicrophone) {
        console.log('🎤 步骤2: 启动麦克风ASR连接...');

        // 检查是否已经连接
        if (connectionManager.getConnectionState('microphoneASR')) {
          console.log('✅ 麦克风ASR已连接，跳过');
          microphoneResult = { success: true };
        } else {
          try {
            const microphoneASRManager = await getMicrophoneASRManager();
            const asrResult = await microphoneASRManager.startASRSession();

            if (asrResult.success) {
              connectionManager.setConnectionState('microphoneASR', true);
              console.log('✅ 麦克风ASR连接成功');

              // 等待连接稳定
              await new Promise(resolve => setTimeout(resolve, 1000));

              // 启动麦克风捕获
              console.log('🎤 启动麦克风捕获...');
              microphoneResult = await microphoneManager.startCapturing();

              if (microphoneResult.success) {
                connectionManager.setConnectionState('microphone', true);
                console.log('✅ 麦克风捕获启动成功');

                // 通知渲染进程
                const window = getMainWindow();
                if (window) {
                  console.log('📡 发送 start-microphone-recognition 事件到渲染进程');
                  window.webContents.send('start-microphone-recognition');
                } else {
                  console.error('❌ 主窗口不可用，无法发送事件');
                }
              } else {
                console.error('❌ 麦克风捕获启动失败:', microphoneResult.error);
              }
            } else {
              console.error('❌ 麦克风ASR连接失败:', asrResult.error);
              microphoneResult = { success: false, error: asrResult.error };
            }
          } catch (error) {
            console.error('❌ 麦克风ASR启动异常:', error);
            microphoneResult = {
              success: false,
              error: error instanceof Error ? error.message : '麦克风ASR启动异常'
            };
          }
        }
      }

      // 评估整体成功状态
      const systemSuccess = enableSystemAudio ? systemAudioResult.success : true;
      const microphoneSuccess = enableMicrophone ? microphoneResult.success : true;
      const overallSuccess = systemSuccess && microphoneSuccess;

      console.log(`🎯 语音识别流程启动结果: 整体=${overallSuccess}, 系统音频=${systemSuccess}, 麦克风=${microphoneSuccess}`);
      console.log('📊 连接状态:', connectionManager.getAllConnectionStates());

      // 如果整体失败，清理已建立的连接
      if (!overallSuccess) {
        console.log('⚠️ 启动失败，开始清理连接...');
        await cleanupFailedConnections(enableSystemAudio, enableMicrophone);
      }

      // 通知前端状态
      const window = getMainWindow();
      if (window) {
        window.webContents.send('toggle-voice-assistant', {
          asrConnected: overallSuccess,
          recording: overallSuccess,
          status: overallSuccess ? 'active' : 'error',
          systemAudioActive: enableSystemAudio && systemAudioResult.success,
          microphoneActive: enableMicrophone && microphoneResult.success,
          error: overallSuccess ? undefined : '语音识别服务启动失败'
        });
      }

      return {
        success: overallSuccess,
        systemAudio: systemAudioResult,
        microphone: microphoneResult
      };

    } catch (error) {
      console.error('💥 语音识别流程启动异常:', error);

      // 异常情况下也要清理连接
      await cleanupFailedConnections(enableSystemAudio, enableMicrophone);

      return {
        success: false,
        error: error instanceof Error ? error.message : '语音识别流程启动异常'
      };
    }
  });
}

/**
 * 清理失败的连接
 */
async function cleanupFailedConnections(enableSystemAudio: boolean, enableMicrophone: boolean): Promise<void> {
  console.log('🧹 开始清理失败的连接...');

  try {
    if (enableSystemAudio) {
      // 停止系统音频捕获
      try {
        await systemAudioManager.stopCapturing();
        connectionManager.setConnectionState('systemAudio', false);
      } catch (error) {
        console.error('清理系统音频捕获失败:', error);
      }

      // 停止系统音频ASR
      try {
        const systemASRManager = await getSystemASRManager();
        await systemASRManager.stopASRSession();
        connectionManager.setConnectionState('systemASR', false);
      } catch (error) {
        console.error('清理系统音频ASR失败:', error);
      }
    }

    if (enableMicrophone) {
      // 停止麦克风捕获
      try {
        await microphoneManager.stopCapturing();
        connectionManager.setConnectionState('microphone', false);
      } catch (error) {
        console.error('清理麦克风捕获失败:', error);
      }

      // 停止麦克风ASR
      try {
        const microphoneASRManager = await getMicrophoneASRManager();
        await microphoneASRManager.stopASRSession();
        connectionManager.setConnectionState('microphoneASR', false);
      } catch (error) {
        console.error('清理麦克风ASR失败:', error);
      }
    }

    console.log('✅ 连接清理完成');
  } catch (error) {
    console.error('❌ 连接清理过程中出错:', error);
  }
}

/**
 * 获取系统音频ASR管理器实例
 */
async function getSystemASRManager() {
  const { SystemASRManager } = await import('../handlers/SystemASRManager');
  return SystemASRManager.getInstance();
}

/**
 * 获取麦克风ASR管理器实例
 */
async function getMicrophoneASRManager() {
  const { MicrophoneASRManager } = await import('../handlers/MicrophoneASRManager');
  return MicrophoneASRManager.getInstance();
}