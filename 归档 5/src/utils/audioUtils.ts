/**
 * Audio utility functions for the renderer process
 */

/**
 * Convert Float32Array to Int16Array (PCM)
 * @param float32Array Float32Array audio data
 * @returns Int16Array audio data
 */
export function convertFloat32ToPCM16(float32Array: Float32Array): Int16Array {
  const pcm16 = new Int16Array(float32Array.length);
  
  // 将-1.0 ~ 1.0的浮点值转换为-32768 ~ 32767的整数值
  for (let i = 0; i < float32Array.length; i++) {
    const s = Math.max(-1, Math.min(1, float32Array[i]));
    pcm16[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
  }
  
  return pcm16;
}

/**
 * Resample audio data to a different sample rate
 * @param audioData Audio data to resample
 * @param inputSampleRate Input sample rate
 * @param outputSampleRate Output sample rate
 * @returns Resampled audio data
 */
export function resampleAudio(audioData: Int16Array, inputSampleRate: number, outputSampleRate: number): Int16Array {
  if (!audioData || audioData.length === 0) {
    console.warn('Empty audio data provided to resampleAudio');
    return new Int16Array(0);
  }
  
  try {
    // If the sample rates are the same, return the original data
    if (inputSampleRate === outputSampleRate) {
      return audioData;
    }
    
    const ratio = inputSampleRate / outputSampleRate;
    const outputLength = Math.ceil(audioData.length / ratio);
    const result = new Int16Array(outputLength);
    
    // Use a more efficient resampling algorithm for better quality
    // This is a simple linear interpolation method
    for (let i = 0; i < outputLength; i++) {
      const inputIndex = i * ratio;
      const inputIndexFloor = Math.floor(inputIndex);
      const inputIndexCeil = Math.min(inputIndexFloor + 1, audioData.length - 1);
      const fraction = inputIndex - inputIndexFloor;
      
      // Linear interpolation between adjacent samples
      result[i] = Math.round((1 - fraction) * audioData[inputIndexFloor] + fraction * audioData[inputIndexCeil]);
    }
    
    return result;
  } catch (error) {
    console.error('Error resampling audio data:', error);
    return audioData; // Return original data on error
  }
}

/**
 * Prepare audio data for ASR service
 * @param audioData Audio data to prepare
 * @param sampleRate Sample rate of the audio data
 * @returns Prepared audio data object
 */
export function prepareAudioData(pcmData: Int16Array, sampleRate: number = 16000): { 
  audio_data: number[]; 
  sample_rate: number; 
  audio_format: string; 
  bits: number;
  channel: number;
  codec: string;
} {
  return {
    audio_data: Array.from(pcmData),
    sample_rate: sampleRate,
    audio_format: 'pcm',
    bits: 16,
    channel: 1,
    codec: 'raw'
  };
}

/**
 * Detect if audio contains silence based on RMS value
 * @param audioData Audio data to analyze
 * @param threshold Silence threshold (0-1)
 * @returns True if audio is silent
 */
export function detectSilence(audioData: Float32Array, threshold: number = 0.01): boolean {
  // 计算音频数据的均方根值 (RMS)
  let sum = 0;
  for (let i = 0; i < audioData.length; i++) {
    sum += audioData[i] * audioData[i];
  }
  const rms = Math.sqrt(sum / audioData.length);
  
  // 如果RMS值低于阈值，则判定为静音
  return rms < threshold;
}

/**
 * 混合多个音频流
 * @param streams - 要混合的音频流数组
 * @returns 混合后的音频流
 */
export function mixAudioStreams(streams: MediaStream[]): MediaStream | null {
  if (!streams.length) return null;
  
  // 如果只有一个流，直接返回它
  if (streams.length === 1) return streams[0];
  
  // 创建音频上下文
  const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
    sampleRate: 16000 // 设置为ASR所需的采样率
  });
  
  // 创建目标节点
  const destination = audioContext.createMediaStreamDestination();
  
  // 将所有流连接到目标节点
  streams.forEach(stream => {
    if (stream && stream.getAudioTracks().length > 0) {
      const source = audioContext.createMediaStreamSource(stream);
      source.connect(destination);
    }
  });
  
  return destination.stream;
}

/**
 * 为音频节点添加音量控制
 * @param audioContext - 音频上下文
 * @param sourceNode - 源节点
 * @param volume - 音量 (0.0 ~ 1.0)
 * @returns 增益节点
 */
export function addVolumeControl(
  audioContext: AudioContext, 
  sourceNode: AudioNode, 
  volume: number = 1.0
): GainNode {
  const gainNode = audioContext.createGain();
  gainNode.gain.value = volume;
  sourceNode.connect(gainNode);
  return gainNode;
} 