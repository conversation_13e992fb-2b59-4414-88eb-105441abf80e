import { useEffect, useRef, useState } from "react"
import { <PERSON><PERSON> } from "./ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "./ui/card"
import { Input } from "./ui/input"

interface ApiKeyAuthProps {
  onApiKeySubmit: (apiKey: string) => void
}

const ApiKeyAuth: React.FC<ApiKeyAuthProps> = ({ onApiKeySubmit }) => {
  const [apiKey, setApiKey] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [errorMessage, setErrorMessage] = useState("")
  const contentRef = useRef<HTMLDivElement>(null)
  useEffect(() => {
    // Height update logic
    const updateDimensions = () => {
      if (contentRef.current) {
        let contentHeight = contentRef.current.scrollHeight
        const contentWidth = contentRef.current.scrollWidth
        window.electronAPI.updateContentDimensions({
          width: contentWidth,
          height: contentHeight
        })
      }
    }

    // Initialize resize observer
    const resizeObserver = new ResizeObserver(updateDimensions)
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current)
    }
    updateDimensions()

    return () => {
      resizeObserver.disconnect()
    }
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!apiKey.trim()) return

    setIsLoading(true)
    setErrorMessage("")

    try {
      // 调用主进程的配置初始化方法
      const result = await (window as any).electronAPI.invoke('init-config', apiKey.trim())

      if (result.success) {
        // 配置初始化成功，调用父组件的回调
        onApiKeySubmit(apiKey.trim())
      } else {
        // 配置初始化失败，显示错误信息
        setErrorMessage(result.message || result.error || 'API Key 验证失败')
      }
    } catch (error) {
      console.error('配置初始化出错:', error)
      setErrorMessage('配置初始化失败，请检查网络连接')
    } finally {
      setIsLoading(false)
    }
  }

  const handleOpenLink = (url: string) => {
    window.electronAPI.openExternal(url)
  }

  return (
    <div
      ref={contentRef}
      className="w-fit h-fit flex flex-col items-center justify-center bg-gray-50 rounded-xl"
    >
      <Card>
        <CardHeader className="space-y-2">
          <CardTitle className="text-2xl font-semibold text-center">
            Welcome to Assistant
          </CardTitle>
          <CardDescription className="text-center text-gray-500">
            Please enter your key to continue. Please contact Java_Plus if you need
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Input
                type="password"
                placeholder="sk-..."
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                className="w-full"
                disabled={isLoading}
              />
              {errorMessage && (
                <p className="text-red-500 text-sm mt-2">{errorMessage}</p>
              )}
            </div>

            <Button
              type="submit"
              className="w-full font-medium"
              disabled={!apiKey.trim() || isLoading}
            >
              {isLoading ? "验证中..." : "Continue"}
            </Button>
            <p className="text-gray-400 text-xs text-center pt-2">
              built out of frustration by{" "}
              <button
                onClick={() =>
                  handleOpenLink("https://www.offer-helper.top")
                }
                className="text-gray-400 hover:text-gray-600 underline"
              >
                Java_Plus
              </button>
            </p>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}

export default ApiKeyAuth
