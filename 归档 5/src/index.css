@tailwind base;
@tailwind components;
@tailwind utilities;

/* 隐藏滚动条但保留滚动功能 */
@layer utilities {
  /* Webkit浏览器 (Chrome, Safari) */
  ::-webkit-scrollbar {
    display: none;
  }

  /* 所有元素 */
  * {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
}

/* 语音识别界面的自定义滚动条 */
@layer components {
  .voice-chat-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 130, 246, 0.5) transparent;
  }

  .voice-chat-scrollbar::-webkit-scrollbar {
    display: block !important;
    width: 8px;
  }

  .voice-chat-scrollbar::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  .voice-chat-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.5);
    border-radius: 4px;
    border: 1px solid rgba(59, 130, 246, 0.2);
  }

  .voice-chat-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.7);
  }

  /* 优化语音聊天区域的滚动体验 */
  .voice-chat-scrollbar {
    scroll-behavior: smooth;
  }
}
