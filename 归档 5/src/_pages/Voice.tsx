// Voice.tsx
import React, { useEffect, useRef, useState } from "react";
import { useQuery } from "react-query";
import { useToast } from "../App";
import VoiceRecognitionUI, { VoiceRecognitionUIRef } from "../components/Voice/VoiceRecognitionUI";
import ToolbarPositionReporter from "../components/Common/ToolbarPositionReporter";

// Define the ElectronAPI interface
interface ElectronAPI {
  invoke: (channel: string, ...args: any[]) => Promise<any>;
  on: (channel: string, callback: (...args: any[]) => void) => void;
  off: (channel: string, callback: (...args: any[]) => void) => void;
  processVoiceToText: (text: string) => Promise<{ success: boolean; response: string; error?: string }>;
  processFastVoiceText: (text: string) => Promise<{ success: boolean; response: string; error?: string }>;
  processAccurateVoiceText: (text: string) => Promise<{ success: boolean; response: string; error?: string }>;
  // Voice panel button event listeners
  onVoiceBackClicked: (callback: () => void) => () => void;
  onVoiceMicrophoneClicked: (callback: () => void) => () => void;
  onVoiceSystemAudioClicked: (callback: () => void) => () => void;
  onVoiceOneClickStartClicked: (callback: () => void) => () => void;
  onVoiceSendToAIClicked: (callback: () => void) => () => void;
}

// Access the API safely
const electronAPI: ElectronAPI | undefined = (window as any).electronAPI;

interface VoiceProps {
  setView: React.Dispatch<React.SetStateAction<"queue" | "solutions" | "debug" | "voice">>
}

const Voice: React.FC<VoiceProps> = ({ setView }) => {
  const { showToast } = useToast();
  const contentRef = useRef<HTMLDivElement>(null);
  const [currentTranscription, setCurrentTranscription] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [screenshotStatus, setScreenshotStatus] = useState<'idle' | 'first-point' | 'with-screenshot'>('idle');
  const [fastResponse, setFastResponse] = useState('等待语音输入...');
  const [accurateResponse, setAccurateResponse] = useState('等待语音输入...');
  const [isLoadingFast, setIsLoadingFast] = useState(false);
  const [isLoadingAccurate, setIsLoadingAccurate] = useState(false);

  // VoiceRecognitionUI组件的ref
  const voiceRecognitionRef = useRef<VoiceRecognitionUIRef>(null);
  
  // Query for screenshots
  const { data: screenshots = [], refetch } = useQuery({
    queryKey: ["screenshots"],
    queryFn: async () => {
      try {
        const existing = await window.electronAPI.getScreenshots();
        return existing;
      } catch (error) {
        console.error("Error loading screenshots:", error);
        showToast("Error", "Failed to load existing screenshots", "error");
        return [];
      }
    },
    staleTime: Infinity,
    cacheTime: Infinity,
    refetchOnWindowFocus: true,
    refetchOnMount: true
  });

  // Handle new transcription
  const handleTranscription = (text: string) => {
    setCurrentTranscription(text);
  };

  // Handle sending to AI
  const handleSendToAI = async (text: string, mode: 'fast' | 'accurate') => {
    console.log(`发送文本到AI (${mode}模式):`, text);
    
    if (mode === 'fast') {
      setIsLoadingFast(true);
      try {
        if (electronAPI?.processFastVoiceText) {
          const result = await electronAPI.processFastVoiceText(text);
          if (result.success) {
            setFastResponse(result.response);
          } else {
            setFastResponse(`处理失败: ${result.error || '未知错误'}`);
          }
        } else {
          setTimeout(() => {
            setFastResponse(`[极速模式] 这是对"${text}"的模拟快速回复。实际应用中会从Gemini API获取回复。`);
          }, 800);
        }
      } catch (error) {
        console.error('极速模式处理失败:', error);
        setFastResponse('处理请求时出错，请重试');
      } finally {
        setIsLoadingFast(false);
      }
    } else if (mode === 'accurate') {
      setIsLoadingAccurate(true);
      try {
        if (electronAPI?.processAccurateVoiceText) {
          const result = await electronAPI.processAccurateVoiceText(text);
          if (result.success) {
            setAccurateResponse(result.response);
          } else {
            setAccurateResponse(`处理失败: ${result.error || '未知错误'}`);
          }
        } else {
          setTimeout(() => {
            setAccurateResponse(`[精确模式] 这是对"${text}"的模拟精确回复。\n\n精确模式会提供更详细和全面的回答，但需要更长的处理时间。`);
          }, 2000);
        }
      } catch (error) {
        console.error('精确模式处理失败:', error);
        setAccurateResponse('处理请求时出错，请重试');
      } finally {
        setIsLoadingAccurate(false);
      }
    }
    
    return true; // 返回成功
  };

  // Navigate back to queue view with smart service handling
  const navigateToQueueView = async () => {
    console.log('🔙 返回按钮被点击');

    // 通过ref调用状态检查方法
    if (!voiceRecognitionRef.current) {
      console.warn('⚠️ VoiceRecognitionUI ref不可用，直接切换视图');
      setView('queue');
      return;
    }

    console.log('🔙 通过ref调用状态检查方法...');
    const currentStates = voiceRecognitionRef.current.getServiceStates();
    console.log('🔙 当前服务状态:', currentStates);

    const { asrServiceActive, isSystemActive, isMicrophoneActive } = currentStates;
    const activeServices: string[] = [];

    if (asrServiceActive) activeServices.push('ASR服务');
    if (isSystemActive) activeServices.push('系统音频');
    if (isMicrophoneActive) activeServices.push('麦克风');

    const hasActiveServices = activeServices.length > 0;
    console.log('🔙 服务检查结果:', { hasActiveServices, activeServices });

    // 如果检测到任何服务运行，显示提示
    if (hasActiveServices) {
      // 有服务正在运行，提示用户先关闭
      console.log('⚠️ 检测到以下服务正在运行:', activeServices.join(', '));
      console.log('⚠️ 显示Toast提示并阻止切换视图');
      showToast("提示", `请先关闭正在运行的语音识别服务`, "error");
      return; // 不切换视图
    } else {
      console.log('✅ 没有语音识别服务正在运行，切换到队列视图');

      // 切换到队列视图
      setView('queue');

      // 多重保障机制，确保语音按钮状态和位置正确上报
      const ensureMicrophoneButtonState = async (attempt: number = 1) => {
        try {
          if (window.electronAPI?.shouldShowMicrophoneButton) {
            const shouldShow = await window.electronAPI.shouldShowMicrophoneButton();
            console.log(`🔙 第${attempt}次检查麦克风按钮状态:`, shouldShow);

            // 如果需要显示麦克风按钮，额外触发一次状态检查
            if (shouldShow) {
              setTimeout(async () => {
                try {
                  await window.electronAPI.shouldShowMicrophoneButton();
                  console.log(`🔙 第${attempt}次额外状态确认完成`);
                } catch (error) {
                  console.warn(`🔙 第${attempt}次额外状态确认失败:`, error);
                }
              }, 100);
            }
          }
        } catch (error) {
          console.warn(`🔙 第${attempt}次触发麦克风按钮状态检查时出错:`, error);
        }
      };

      // 多次检查，确保状态正确
      setTimeout(() => ensureMicrophoneButtonState(1), 200);
      setTimeout(() => ensureMicrophoneButtonState(2), 600);
      setTimeout(() => ensureMicrophoneButtonState(3), 1000);

      // 注意：ToolbarPositionReporter组件会自动检测视图变化并重新上报位置
      console.log('🔙 切换到队列视图完成，ToolbarPositionReporter将自动重新上报位置');
    }
  };

  // Update content dimensions when component changes
  useEffect(() => {
    const updateDimensions = () => {
      if (contentRef.current) {
        const contentHeight = contentRef.current.scrollHeight;
        const contentWidth = contentRef.current.scrollWidth;
        window.electronAPI.updateContentDimensions({
          width: contentWidth,
          height: contentHeight
        });
      }
    };

    // Initialize resize observer
    const resizeObserver = new ResizeObserver(updateDimensions);
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current);
    }
    updateDimensions();

    // Set up event listeners
    const cleanupFunctions = [
      window.electronAPI.onScreenshotTaken(() => {
        refetch();
        setScreenshotStatus('with-screenshot');
      }),
      window.electronAPI.onResetView(() => {
        refetch();
        setScreenshotStatus('idle');
      }),
      window.electronAPI.onFirstPointRecorded(() => {
        setScreenshotStatus('first-point');
      }),
      // Voice panel button event listeners
      window.electronAPI.onVoiceBackClicked(() => {
        console.log('收到返回按钮点击事件');
        navigateToQueueView();
      }),
      window.electronAPI.onVoiceMicrophoneClicked(() => {
        console.log('收到麦克风按钮点击事件');
        // 触发麦克风按钮点击
        const microphoneButton = document.querySelector('.voice-microphone-button') as HTMLButtonElement;
        if (microphoneButton) {
          microphoneButton.click();
        }
      }),
      window.electronAPI.onVoiceSystemAudioClicked(() => {
        console.log('收到系统音频按钮点击事件');
        // 触发系统音频按钮点击
        const systemAudioButton = document.querySelector('.voice-system-audio-button') as HTMLButtonElement;
        if (systemAudioButton) {
          systemAudioButton.click();
        }
      }),
      window.electronAPI.onVoiceOneClickStartClicked(() => {
        console.log('收到一键启动按钮点击事件');
        // 触发一键启动按钮点击
        const oneClickStartButton = document.querySelector('.voice-one-click-start-button') as HTMLButtonElement;
        if (oneClickStartButton) {
          oneClickStartButton.click();
        }
      }),
      window.electronAPI.onVoiceSendToAIClicked(() => {
        console.log('收到发送到AI按钮点击事件');
        // 触发发送到AI按钮点击
        const sendToAIButton = document.querySelector('.voice-send-to-ai-button') as HTMLButtonElement;
        if (sendToAIButton) {
          sendToAIButton.click();
        }
      })
    ];

    if (screenshots && screenshots.length > 0) {
      setScreenshotStatus('with-screenshot');
    }

    return () => {
      resizeObserver.disconnect();
      cleanupFunctions.forEach((cleanup) => cleanup());
    };
  }, [refetch, screenshots]);

  return (
    <div ref={contentRef} className="bg-transparent w-full max-w-[1600px] mx-auto">
 
      {/* 主内容区 - 左侧语音对话框，右侧AI响应 */}
      <div className="px-1">
        <div className="bg-gray-900/30 backdrop-blur-md rounded-lg border border-blue-500/20 shadow-lg overflow-hidden">
          <ToolbarPositionReporter
            backSelector=".voice-back-button"
            microphoneSelector=".voice-microphone-button"
            systemAudioSelector=".voice-system-audio-button"
            oneClickStartSelector=".voice-one-click-start-button"
            sendToAISelector=".voice-send-to-ai-button"
            currentView="voice"
          >
            <div className="flex items-center justify-between p-3 bg-black/20 border-b border-blue-500/10">
              <h3 className="text-white text-sm font-medium flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-400">
                  <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
                  <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                  <line x1="12" x2="12" y1="19" y2="22"></line>
                </svg>
                语音助手
              </h3>
              <div className="flex gap-2">
                <button
                  onClick={navigateToQueueView}
                  className="voice-back-button px-3 py-1 bg-gray-700 text-white rounded-md text-xs"
                >
                  返回
                </button>
              </div>
            </div>

            {/* 左右布局：左侧语音对话，右侧AI响应 */}
            <div className="h-[700px] overflow-hidden">
              <VoiceRecognitionUI
                ref={voiceRecognitionRef}
                onTranscription={handleTranscription}
                onSendToAI={handleSendToAI}
              />
            </div>
          </ToolbarPositionReporter>
        </div>
      </div>
    </div>
  );
};

export default Voice; 