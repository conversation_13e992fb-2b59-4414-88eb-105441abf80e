#!/usr/bin/env node

/**
 * 测试混淆配置是否有效
 */

try {
  const config = require('./obfuscation-simple.config.js');
  
  console.log('✅ 配置文件加载成功');
  
  // 检查每个配置
  const configs = ['mainProcessConfig', 'rendererProcessConfig', 'preloadConfig'];
  
  for (const configName of configs) {
    if (config[configName]) {
      const cfg = config[configName];
      
      console.log(`\n📋 检查 ${configName}:`);
      
      // 检查 reservedNames 是否有重复
      if (cfg.reservedNames) {
        const uniqueNames = [...new Set(cfg.reservedNames)];
        if (uniqueNames.length !== cfg.reservedNames.length) {
          console.log(`❌ ${configName}.reservedNames 有重复项`);
          console.log(`原始长度: ${cfg.reservedNames.length}, 去重后: ${uniqueNames.length}`);
        } else {
          console.log(`✅ ${configName}.reservedNames 无重复 (${cfg.reservedNames.length} 项)`);
        }
      }
      
      // 检查 reservedStrings 是否有重复
      if (cfg.reservedStrings) {
        const uniqueStrings = [...new Set(cfg.reservedStrings)];
        if (uniqueStrings.length !== cfg.reservedStrings.length) {
          console.log(`❌ ${configName}.reservedStrings 有重复项`);
          console.log(`原始长度: ${cfg.reservedStrings.length}, 去重后: ${uniqueStrings.length}`);
        } else {
          console.log(`✅ ${configName}.reservedStrings 无重复 (${cfg.reservedStrings.length} 项)`);
        }
      }
    } else {
      console.log(`❌ 配置 ${configName} 不存在`);
    }
  }
  
  console.log('\n🎉 配置验证完成');
  
} catch (error) {
  console.error('❌ 配置文件加载失败:', error.message);
  process.exit(1);
}
