{
  "compilerOptions": {
    "target": "ESNext",
    "lib": ["DOM", "DOM.Iterable", "ESNext"], // Browser APIs
    "module": "ESNext", // For Vite/Modern browsers
    "moduleResolution": "bundler", // For Vite
    "jsx": "react-jsx", // React specific
    "noEmit": true, // Vite handles the building
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "skipLibCheck": true
  },
  "include": ["src"], // Only React source files
  "references": [{ "path": "./tsconfig.node.json" }]
}
