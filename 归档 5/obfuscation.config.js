/**
 * JavaScript代码混淆配置
 * 用于保护Electron应用的源代码安全
 */

const JavaScriptObfuscator = require('javascript-obfuscator');

// 中等强度混淆配置 - 平衡安全性和稳定性
const productionConfig = {
  // 基础混淆选项 - 降低强度
  compact: true,
  controlFlowFlattening: false, // 禁用控制流扁平化，避免运行时错误
  controlFlowFlatteningThreshold: 0,
  numbersToExpressions: false, // 禁用数字表达式转换
  simplify: true,
  stringArrayShuffle: false, // 禁用字符串数组洗牌
  splitStrings: false, // 禁用字符串分割
  stringArrayThreshold: 0.4, // 降低字符串数组使用率

  // 标识符混淆
  identifierNamesGenerator: 'hexadecimal',
  renameGlobals: false,

  // 字符串混淆 - 简化设置
  stringArray: true,
  stringArrayCallsTransform: false, // 禁用字符串数组调用转换
  stringArrayCallsTransformThreshold: 0,
  stringArrayEncoding: [], // 移除字符串编码，避免解码错误
  stringArrayIndexShift: false,
  stringArrayRotate: false,
  stringArrayWrapperName: '',
  stringArrayWrapperParametersCount: 0,
  stringArrayWrapperType: 'variable',

  // 控制流混淆 - 禁用危险选项
  deadCodeInjection: false, // 禁用死代码注入
  deadCodeInjectionThreshold: 0,
  debugProtection: false,
  disableConsoleOutput: false, // 保留console输出用于调试

  // 反调试保护 - 禁用
  selfDefending: false, // 禁用自我防护，避免误杀
  unicodeEscapeSequence: false,

  // 性能优化
  target: 'node',
  transformObjectKeys: false, // 禁用对象键转换

  // 排除混淆的标识符
  reservedNames: [
    // Electron相关
    'require',
    'module',
    'exports',
    '__dirname',
    '__filename',
    'process',
    'global',
    'Buffer',
    'console',

    // Node.js内置模块
    'fs',
    'path',
    'os',
    'crypto',
    'events',
    'stream',
    'util',

    // 保留的函数名
    'main',
    'createWindow',
    'app',
    'BrowserWindow',
    'ipcMain',
    'ipcRenderer',
    'contextBridge',

    // 避免混淆的关键字
    'electron',
    'webContents',
    'session',
    'protocol'
  ],

  // 排除混淆的字符串
  reservedStrings: [
    'electron',
    'app',
    'ready',
    'window-all-closed',
    'activate',
    'preload',
    'nodeIntegration',
    'contextIsolation',
    'enableRemoteModule',
    'webSecurity'
  ]
};

// 中等强度混淆配置 - 用于开发环境
const developmentConfig = {
  compact: false,
  controlFlowFlattening: false,
  deadCodeInjection: false,
  debugProtection: false,
  disableConsoleOutput: false,
  identifierNamesGenerator: 'hexadecimal',
  renameGlobals: false,
  selfDefending: false,
  stringArray: true,
  stringArrayThreshold: 0.75,
  unicodeEscapeSequence: false,
  target: 'node',
  reservedNames: productionConfig.reservedNames,
  reservedStrings: productionConfig.reservedStrings
};

// 主进程专用配置
const mainProcessConfig = {
  ...productionConfig,
  target: 'node',
  // 主进程特殊保留
  reservedNames: [
    ...productionConfig.reservedNames,
    'Menu',
    'MenuItem',
    'dialog',
    'shell',
    'screen',
    'nativeTheme',
    'powerMonitor',
    'powerSaveBlocker',
    'autoUpdater',
    'crashReporter',
    'nativeImage',
    'clipboard',
    'globalShortcut'
  ]
};

// 渲染进程专用配置
const rendererProcessConfig = {
  ...productionConfig,
  target: 'browser',
  // 渲染进程特殊保留
  reservedNames: [
    ...productionConfig.reservedNames,
    'window',
    'document',
    'navigator',
    'location',
    'history',
    'localStorage',
    'sessionStorage',
    'indexedDB',
    'fetch',
    'XMLHttpRequest',
    'WebSocket',
    'Worker',
    'ServiceWorker',
    'electronAPI'
  ]
};

// 预加载脚本专用配置 - 最保守的设置
const preloadConfig = {
  ...productionConfig,
  target: 'node',
  compact: false, // 不压缩预加载脚本
  stringArray: false, // 完全禁用字符串数组
  stringArrayThreshold: 0,
  identifierNamesGenerator: 'mangled', // 使用更简单的标识符

  // 预加载脚本特殊保留 - 扩展保留名称
  reservedNames: [
    ...productionConfig.reservedNames,
    'webFrame',
    'exposeInMainWorld',
    'contextBridge',
    'ipcRenderer',
    'shell',
    'invoke',
    'on',
    'off',
    'removeListener',
    'send',
    'electronAPI',
    'PROCESSING_EVENTS',

    // 事件名称保留
    'screenshot-taken',
    'solutions-ready',
    'reset-view',
    'config-updated',
    'model-changed',
    'window-moved',
    'toggle-voice-assistant',
    'navigate-to-voice-view',
    'start-microphone-recognition',
    'restore-focus'
  ]
};

module.exports = {
  production: productionConfig,
  development: developmentConfig,
  mainProcess: mainProcessConfig,
  rendererProcess: rendererProcessConfig,
  preload: preloadConfig,
  
  // 混淆函数
  obfuscate: (code, config = productionConfig) => {
    return JavaScriptObfuscator.obfuscate(code, config).getObfuscatedCode();
  },
  
  // 混淆文件
  obfuscateFile: (inputPath, outputPath, config = productionConfig) => {
    const fs = require('fs');
    const code = fs.readFileSync(inputPath, 'utf8');
    const obfuscatedCode = JavaScriptObfuscator.obfuscate(code, config).getObfuscatedCode();
    fs.writeFileSync(outputPath, obfuscatedCode);
    return obfuscatedCode;
  }
};
