import React, { useEffect, useRef, useState } from "react"
import { useQuery } from "react-query"
import { useToast } from "../App"
import ToolbarPositionReporter from "../components/Common/ToolbarPositionReporter"
import ScreenshotQueue from "../components/Queue/ScreenshotQueue"

interface QueueProps {
  setView: React.Dispatch<React.SetStateAction<"queue" | "solutions" | "debug" | "voice">>
  currentView?: "queue" | "solutions" | "debug" | "voice"
}

interface ModelInfo {
  name: string
  index?: number
  total?: number
}

interface CodeLanguageInfo {
  name: string
  index: number
  total: number
}

const Queue: React.FC<QueueProps> = ({ setView, currentView = 'queue' }) => {
  const { showToast } = useToast()

  const [isTooltipVisible, setIsTooltipVisible] = useState(false)
  const [tooltipHeight, setTooltipHeight] = useState(0)
  const contentRef = useRef<HTMLDivElement>(null)
  const [screenshotStatus, setScreenshotStatus] = useState<'idle' | 'first-point' | 'with-screenshot'>('idle')
  const [modelInfo, setModelInfo] = useState<ModelInfo | null>(null)
  const [codeLanguageInfo, setCodeLanguageInfo] = useState<CodeLanguageInfo | null>(null)
  const [isErrorState, setIsErrorState] = useState(false)
  const [showMicrophoneButton, setShowMicrophoneButton] = useState(false)

  // Function to navigate to Voice view
  const navigateToVoiceView = () => {
    console.log('Navigating to Voice view');
    setView('voice');
  };

  // Fetch model info, code language info and check voice config when component mounts
  useEffect(() => {
    const fetchCurrentModel = async () => {
      try {
        // This will trigger the model-changed event via the IPC handler
        await window.electronAPI.getCurrentModel()
      } catch (error) {
        console.error("Failed to fetch current model:", error)
        setIsErrorState(true)
      }
    }

    const fetchCurrentCodeLanguage = async () => {
      try {
        // This will trigger the code-language-changed event via the IPC handler
        await window.electronAPI.getCurrentCodeLanguage()
      } catch (error) {
        console.error("Failed to fetch current code language:", error)
        setIsErrorState(true)
      }
    }

    const checkVoiceConfig = async () => {
      try {
        // 检查语音功能是否启用
        const shouldShow = await window.electronAPI.shouldShowMicrophoneButton()
        const previousState = showMicrophoneButton;
        setShowMicrophoneButton(shouldShow)
        console.log('麦克风按钮显示状态:', shouldShow)

        // 如果按钮状态发生变化，记录日志但不强制重新渲染
        if (previousState !== shouldShow) {
          console.log('🔄 麦克风按钮状态变化，状态已更新');
        }
      } catch (error) {
        console.error("Failed to check voice config:", error)
        setShowMicrophoneButton(false) // 默认不显示
      }
    }

    fetchCurrentModel()
    fetchCurrentCodeLanguage()
    checkVoiceConfig()
  }, [])
  
  const { data: screenshots = [], refetch, error: screenshotsError } = useQuery({
    queryKey: ["screenshots"],
    queryFn: async () => {
      try {
        const existing = await window.electronAPI.getScreenshots()
        // If we successfully load data, reset error state
        setIsErrorState(false)
        return existing
      } catch (error) {
        console.error("Error loading screenshots:", error)
        showToast("Error", "Failed to load existing screenshots", "error")
        setIsErrorState(true)
        return []
      }
    },
    staleTime: Infinity,
    cacheTime: Infinity,
    refetchOnWindowFocus: true,
    refetchOnMount: true
  })

  // Update error state if screenshots query has an error
  useEffect(() => {
    if (screenshotsError) {
      setIsErrorState(true)
    }
  }, [screenshotsError])

  // 监听麦克风按钮状态变化，确保工具条位置正确上报
  useEffect(() => {
    console.log('🔄 麦克风按钮状态变化:', showMicrophoneButton);
    // 移除延迟和强制重新渲染，避免抖动
  }, [showMicrophoneButton])

  // 监听视图变化，当切换到 queue 视图时重新检查麦克风按钮状态
  useEffect(() => {
    if (currentView === 'queue') {
      console.log('🔄 切换到 Queue 视图，重新检查麦克风按钮状态');

      // 多次检查确保状态正确，解决偶发性问题
      const checkMicrophoneState = async (attempt: number = 1) => {
        try {
          const shouldShow = await window.electronAPI.shouldShowMicrophoneButton();
          const previousState = showMicrophoneButton;
          setShowMicrophoneButton(shouldShow);
          console.log(`🔄 麦克风按钮状态检查完成 (第${attempt}次):`, shouldShow);

          if (previousState !== shouldShow) {
            console.log('🔄 视图切换导致麦克风按钮状态变化');

            // 状态变化后，额外延迟确保 DOM 更新完成，然后强制重新上报位置
            setTimeout(() => {
              console.log('🔄 强制触发位置重新上报');
              // 触发一个微小的状态变化来强制 ToolbarPositionReporter 重新计算
              setShowMicrophoneButton(prev => {
                setTimeout(() => setShowMicrophoneButton(shouldShow), 10);
                return prev;
              });
            }, 200);
          }
        } catch (error) {
          console.error(`🔄 第${attempt}次检查麦克风按钮状态失败:`, error);
          setShowMicrophoneButton(false);
        }
      };

      // 第一次检查
      const timer1 = setTimeout(() => checkMicrophoneState(1), 100);
      // 第二次检查，确保状态正确
      const timer2 = setTimeout(() => checkMicrophoneState(2), 500);

      return () => {
        clearTimeout(timer1);
        clearTimeout(timer2);
      };
    }
  }, [currentView])

  const handleDeleteScreenshot = async (index: number) => {
    const screenshotToDelete = screenshots[index]

    try {
      const response = await window.electronAPI.deleteScreenshot(
        screenshotToDelete.path
      )

      if (response.success) {
        refetch() // Refetch screenshots instead of managing state directly
        setIsErrorState(false)
      } else {
        console.error("Failed to delete screenshot:", response.error)
        showToast("Error", "Failed to delete the screenshot file", "error")
        setIsErrorState(true)
      }
    } catch (error) {
      console.error("Error deleting screenshot:", error)
      setIsErrorState(true)
    }
  }

  useEffect(() => {
    // Height update logic
    const updateDimensions = () => {
      if (contentRef.current) {
        let contentHeight = contentRef.current.scrollHeight
        const contentWidth = contentRef.current.scrollWidth
        if (isTooltipVisible) {
          contentHeight += tooltipHeight
        }
        window.electronAPI.updateContentDimensions({
          width: contentWidth,
          height: contentHeight
        })
      }
    }

    // Initialize resize observer
    const resizeObserver = new ResizeObserver(updateDimensions)
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current)
    }
    updateDimensions()

    // Set up event listeners
    const cleanupFunctions = [
      window.electronAPI.onScreenshotTaken(() => {
        refetch()
        setScreenshotStatus('with-screenshot')
        setIsErrorState(false)
      }),
      window.electronAPI.onResetView(() => {
        refetch()
        setScreenshotStatus('idle')
        // Reset error state when view is reset
        setIsErrorState(false)
      }),
      window.electronAPI.onFirstPointRecorded(() => {
        setScreenshotStatus('first-point')
      }),
      window.electronAPI.onSolutionError((error: string) => {
        showToast(
          "Processing Failed",
          "There was an error processing your screenshots.",
          "error"
        )
        setView("queue") // Revert to queue if processing fails
        console.error("Processing error:", error)
        setIsErrorState(true)
      }),
      window.electronAPI.onProcessingNoScreenshots(() => {
        showToast(
          "No Screenshots",
          "There are no screenshots to process.",
          "neutral"
        )
      }),
      window.electronAPI.onConfigInitFailed((data) => {
        showToast(
          "Configuration Failed",
          `${data.message}\n${data.detail}`,
          "error"
        )
      }),
      window.electronAPI.onConfigUpdated(async (data) => {
        console.log('收到配置更新事件:', data);
        if (data.success) {
          // 配置更新成功，重新检查麦克风按钮显示状态
          try {
            const previousState = showMicrophoneButton;
            const shouldShow = await window.electronAPI.shouldShowMicrophoneButton();
            setShowMicrophoneButton(shouldShow);
            console.log('麦克风按钮显示状态已更新:', shouldShow);

            // 如果按钮状态发生变化，记录日志但不强制重新渲染
            if (previousState !== shouldShow) {
              console.log('🔄 配置更新导致麦克风按钮状态变化，状态已更新');
            }

            showToast(
              "Configuration Updated",
              "Voice configuration has been updated successfully",
              "success"
            );
          } catch (error) {
            console.error('更新麦克风按钮状态失败:', error);
          }
        }
      }),
      window.electronAPI.onModelChanged((_, data) => {
        setModelInfo({
          name: data.model || 'Unknown',
          index: data.index,
          total: data.total
        })
      }),
      window.electronAPI.onCodeLanguageChanged((_, data) => {
        setCodeLanguageInfo({
          name: data.codeLanguage || 'Unknown',
          index: data.index,
          total: data.total
        })
      })
    ]

    if (screenshots && screenshots.length > 0) {
      setScreenshotStatus('with-screenshot')
    }

    return () => {
      resizeObserver.disconnect()
      cleanupFunctions.forEach((cleanup) => cleanup())
    }
  }, [isTooltipVisible, tooltipHeight, screenshots, setView])

  const handleTooltipVisibilityChange = (visible: boolean, height: number) => {
    setIsTooltipVisible(visible)
    setTooltipHeight(height)
  }

  const getStatusText = () => {
    switch (screenshotStatus) {
      case 'first-point':
        return "▛"
      case 'with-screenshot':
        return "▛▟"
      default:
        return ""
    }
  }

  // Model display renderer - always shows either the model info or a loading state
  const renderModelInfo = () => {
    if (modelInfo) {
      let displayText = modelInfo.name;
      if (modelInfo.index && modelInfo.total) {
        displayText += ` (${modelInfo.index}/${modelInfo.total})`;
      }
      return displayText;
    }
    return 'Loading model...';
  }

  // Code language display renderer - always shows either the code language info or a loading state
  const renderCodeLanguageInfo = () => {
    if (codeLanguageInfo) {
      let displayText = codeLanguageInfo.name;
      if (codeLanguageInfo.index && codeLanguageInfo.total) {
        displayText += ` (${codeLanguageInfo.index}/${codeLanguageInfo.total})`;
      }
      return displayText;
    }
    return 'Loading...';
  }

  return (
    <div ref={contentRef} className={`bg-transparent w-auto min-w-[50%] max-w-[85vw]`}>
      <div className="px-4 py-3">
        <div className="space-y-1 w-fit">
          <ScreenshotQueue
            isLoading={false}
            screenshots={screenshots}
            onDeleteScreenshot={handleDeleteScreenshot}
          />

          <ToolbarPositionReporter
            modelSelector=".model-info"
            codeLanguageSelector=".code-language-info"
            resetSelector=".reset-button"
            captureSelector=".capture-area"
            solutionSelector=".solution-button"
            voiceSelector=".voice-assistant"
            errorState={isErrorState}
            currentView={currentView}
          >
            <div className="pt-2 w-fit max-w-full">
              <div className="text-xs text-white/95 backdrop-blur-lg bg-black/70 rounded-xl py-2.5 px-4 flex items-center shadow-lg border border-white/10 transition-all duration-200 w-fit max-w-full">
                <div className="flex items-center gap-4 flex-shrink-0">
                  {/* Capture (previously Show/Hide) */}
                  <div
                    className="flex items-center gap-2 cursor-pointer capture-area hover:text-white transition-colors duration-200 group flex-shrink-0"
                  >
                    <span className="flex items-center justify-center text-white/90 group-hover:text-white transition-colors duration-200 w-4 h-4">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="w-full h-full"
                      >
                        <path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z" />
                        <circle cx="12" cy="13" r="3" />
                      </svg>
                    </span>
                    <span className="text-[11px] leading-none font-medium my-auto">Capture</span>
                  </div>
                  <div className="h-4 w-px bg-white/20 flex-shrink-0" />
                  {/* Reset */}
                  <div className="flex items-center gap-2 reset-button group flex-shrink-0">
                    <span className="text-[11px] leading-none font-medium truncate group-hover:text-white transition-colors duration-200">
                      Reset
                    </span>
                    <div className="flex gap-1">
                      <div className="bg-white/15 group-hover:bg-white/25 transition-colors duration-200 rounded-md px-1.5 py-1 text-[11px] leading-none text-white/80 shadow-sm">
                        ⌘
                      </div>
                      <div className="bg-white/15 group-hover:bg-white/25 transition-colors duration-200 rounded-md px-1.5 py-1 text-[11px] leading-none text-white/80 shadow-sm">
                        R
                      </div>
                    </div>
                  </div>
                  <div className="h-4 w-px bg-white/20" />
                  {/* Capture Status Indicator */}
                  <div className="flex items-center flex-shrink-0">
                    <span className="text-[11px] leading-none text-yellow-300 font-medium flex items-center">
                      {getStatusText()}
                    </span>
                  </div>
                  <div className="h-4 w-px bg-white/20 flex-shrink-0" />
                </div>
                
                {/* Model Info - 完整显示 */}
                <div className="flex items-center model-info flex-shrink-0 mx-2">
                  <span className="text-[10px] leading-none text-green-400 font-medium whitespace-nowrap" title={modelInfo?.name || 'Loading model...'}>
                    {renderModelInfo()}
                  </span>
                </div>

                <div className="h-4 w-px bg-white/20 flex-shrink-0" />

                {/* Code Language Info - 完整显示 */}
                <div className="flex items-center code-language-info flex-shrink-0 mx-2">
                  <span className="text-[10px] leading-none text-blue-400 font-medium whitespace-nowrap" title={codeLanguageInfo?.name || 'Loading language...'}>
                    {renderCodeLanguageInfo()}
                  </span>
                </div>
                
                {/* 右侧按钮区域 - 使用 ml-auto 确保始终在右侧，不被挤压 */}
                <div className="flex items-center gap-2 flex-shrink-0 ml-auto pl-4">
                  <div className="h-4 w-px bg-white/20" />
                  {/* Solution button */}
                  <div
                    className="flex items-center cursor-pointer solution-button hover:text-white transition-colors duration-200 group ml-2"
                  >
                    <span className="flex items-center font-medium whitespace-nowrap">
                      <span className="text-[11px] leading-none mr-1 group-hover:scale-110 transition-transform duration-200">🚀</span>
                      <span className="text-[11px] leading-none">Solution</span>
                    </span>
                  </div>
                  {/* Voice Assistant Button - 条件显示 */}
                  {showMicrophoneButton && (
                    <>
                      <div className="h-4 w-px bg-white/20 flex-shrink-0" />
                      <div
                        className="flex items-center gap-2 cursor-pointer voice-assistant hover:text-white transition-all duration-200 group flex-shrink-0 ml-1 mr-1"
                        onClick={navigateToVoiceView}
                      >
                        <span className="flex items-center justify-center text-white/90 group-hover:text-white transition-colors duration-200 w-4 h-4">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="w-full h-full"
                          >
                            <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
                            <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                            <line x1="12" x2="12" y1="19" y2="22"></line>
                          </svg>
                        </span>
                        <span className="text-[11px] leading-none font-medium my-auto">语音</span>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </ToolbarPositionReporter>
        </div>
      </div>
    </div>
  )
}

export default Queue
