import { ToastViewport } from "@radix-ui/react-toast"
import { createContext, useContext, useEffect, useRef, useState } from "react"
import { QueryClient, QueryClientProvider } from "react-query"
import Queue from "./_pages/Queue"
import Solutions from "./_pages/Solutions"
import Voice from "./_pages/Voice"
import ApiKeyAuth from "./components/ApiKeyAuth"
import {
  Toast,
  ToastDescription,
  ToastMessage,
  ToastProvider,
  ToastTitle,
  ToastVariant
} from "./components/ui/toast"

declare global {
  interface Window {
    electronAPI: {
      //RANDOM GETTER/SETTERS
      updateContentDimensions: (dimensions: {
        width: number
        height: number
      }) => Promise<void>
      getScreenshots: () => Promise<Array<{ path: string; preview: string }>>
      getApiKey: () => Promise<string | null>
      getCurrentModel: () => Promise<{
          model: string
          index: number
          total: number
        }>
      getCurrentCodeLanguage: () => Promise<{
          codeLanguage: string
          index: number
          total: number
        }>
      onCodeLanguageChanged: (callback: (event: any, data: any) => void) => () => void
      //GLOBAL EVENTS
      onUnauthorized: (callback: () => void) => () => void
      onApiKeyOutOfCredits: (callback: () => void) => () => void
      onScreenshotTaken: (
        callback: (data: { path: string; preview: string }) => void
      ) => () => void
      onProcessingNoScreenshots: (callback: () => void) => () => void
      onResetView: (callback: () => void) => () => void
      onFirstPointRecorded: (callback: () => void) => () => void
      takeScreenshot: () => Promise<void>

      //INITIAL SOLUTION EVENTS
      deleteScreenshot: (
        path: string
      ) => Promise<{ success: boolean; error?: string }>
      onSolutionStart: (callback: () => void) => () => void
      onSolutionError: (callback: (error: string) => void) => () => void
      onSolutionSuccess: (callback: (data: any) => void) => () => void
      onProblemExtracted: (callback: (data: any) => void) => () => void

      onDebugSuccess: (callback: (data: any) => void) => () => void
      onDebugError: (callback: (error: string) => void) => () => void
      onDebugStart: (callback: () => void) => () => void
      onCopySolutionContent: (callback: () => void) => () => void
      onModelChanged: (callback: (event: any, data: any) => void) => () => void
      onWindowMoved: (callback: () => void) => () => void
      onToggleVoiceAssistant: (callback: () => void) => () => void
      openExternal: (url: string) => void

      moveWindowLeft: () => Promise<void>
      moveWindowRight: () => Promise<void>
      takeFullScreenshot: () => Promise<void>
      generateSolution: () => Promise<void>
      updateApiKey: (apiKey: string) => Promise<void>
      setApiKey: (apiKey: string) => Promise<{ success: boolean }>
      onConfigInitFailed: (callback: (data: { message: string, detail: string }) => void) => () => void
      onConfigUpdated: (callback: (data: { success: boolean, message: string }) => void) => () => void
      shouldShowMicrophoneButton: () => Promise<boolean>

      // Toolbar position reporting
      updateToolbarBounds: (bounds: { 
        x: number; 
        y: number; 
        width: number; 
        height: number 
      }) => Promise<{ success: boolean; error?: string }>
      updateToolbarRegions: (regions: Array<{
        x: number;
        y: number;
        width: number;
        height: number;
        type: 'model' | 'code-language' | 'reset' | 'capture' | 'solution' | 'other' | 'voice' | 'voice-back' | 'voice-microphone' | 'voice-system-audio' | 'voice-one-click-start' | 'voice-send-to-ai';
      }>) => Promise<{ success: boolean; error?: string }>
      
      // Voice assistant related functions
      processVoiceToText: (text: string) => Promise<{ 
        success: boolean;
        response?: string;
        error?: string;
      }>

      // 添加导航到语音视图的监听器
      onNavigateToVoiceView: (callback: () => void) => () => void

      // Voice panel button event listeners
      onVoiceBackClicked: (callback: () => void) => () => void
      onVoiceMicrophoneClicked: (callback: () => void) => () => void
      onVoiceSystemAudioClicked: (callback: () => void) => () => void
      onVoiceOneClickStartClicked: (callback: () => void) => () => void
      onVoiceSendToAIClicked: (callback: () => void) => () => void

      // 添加配置初始化相关方法
      invoke: (channel: string, ...args: any[]) => Promise<any>
    }
  }
}

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: Infinity,
      cacheTime: Infinity
    }
  }
})

interface ToastContextType {
  showToast: (title: string, description: string, variant: ToastVariant) => void
}

export const ToastContext = createContext<ToastContextType | undefined>(
  undefined
)

export function useToast() {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider")
  }
  return context
}

const App: React.FC = () => {
  const [view, setView] = useState<"queue" | "solutions" | "debug" | "voice">("queue")
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const [toastOpen, setToastOpen] = useState(false)
  const [toastMessage, setToastMessage] = useState<ToastMessage>({
    title: "",
    description: "",
    variant: "neutral"
  })
  const [isVoiceAssistantVisible, setIsVoiceAssistantVisible] = useState(false)
  const contentRef = useRef<HTMLDivElement>(null)
  const toastTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const handleApiKeySubmit = async (key: string) => {
    try {
      // 先设置 API Key
      const setKeyResult = await window.electronAPI.setApiKey(key)
      if (setKeyResult.success) {
        setIsAuthenticated(true)
      }
    } catch (error) {
      console.error('设置 API Key 失败:', error)
    }
  }

  const showToast = (
    title: string,
    description: string,
    variant: ToastVariant
  ) => {
    // 清除之前的定时器
    if (toastTimeoutRef.current) {
      clearTimeout(toastTimeoutRef.current)
      toastTimeoutRef.current = null
    }

    // 先关闭当前的 toast，然后设置新的消息
    setToastOpen(false)

    // 使用 setTimeout 确保状态更新完成后再显示新的 toast
    setTimeout(() => {
      setToastMessage({ title, description, variant })
      setToastOpen(true)

      // 设置自动关闭定时器作为备用机制
      toastTimeoutRef.current = setTimeout(() => {
        setToastOpen(false)
        toastTimeoutRef.current = null
      }, 3000) // 3秒后自动关闭
    }, 50)
  }

  // Effect for height monitoring

  useEffect(() => {
    const cleanup = window.electronAPI.onResetView(() => {
      queryClient.invalidateQueries(["screenshots"])
      queryClient.invalidateQueries(["problem_statement"])
      queryClient.invalidateQueries(["solution"])
      queryClient.invalidateQueries(["new_solution"])
      setView("queue")
    })

    return () => {
      cleanup()
    }
  }, [])

  useEffect(() => {
    if (!containerRef.current) return

    const updateHeight = () => {
      if (!containerRef.current) return
      const height = containerRef.current.scrollHeight
      const width = containerRef.current.scrollWidth
      window.electronAPI?.updateContentDimensions({ width, height })
    }

    const resizeObserver = new ResizeObserver(() => {
      updateHeight()
    })

    // Initial height update
    updateHeight()

    // Observe for changes
    resizeObserver.observe(containerRef.current)

    // Also update height when view changes
    const mutationObserver = new MutationObserver(() => {
      updateHeight()
    })

    mutationObserver.observe(containerRef.current, {
      childList: true,
      subtree: true,
      attributes: true,
      characterData: true
    })

    return () => {
      resizeObserver.disconnect()
      mutationObserver.disconnect()
    }
  }, [view]) // Re-run when view changes
  useEffect(() => {
    const cleanupFunctions = [
      window.electronAPI.onSolutionStart(() => {
        setView("solutions")
      }),

      window.electronAPI.onUnauthorized(() => {
        queryClient.removeQueries(["screenshots"])
        queryClient.removeQueries(["solution"])
        queryClient.removeQueries(["problem_statement"])
        setView("queue")
      }),
      // Update this reset handler
      window.electronAPI.onResetView(() => {
        queryClient.removeQueries(["screenshots"])
        queryClient.removeQueries(["solution"])
        queryClient.removeQueries(["problem_statement"])
        setView("queue")
      }),
      window.electronAPI.onProblemExtracted((data: any) => {
        if (view === "queue") {
          queryClient.invalidateQueries(["problem_statement"])
          queryClient.setQueryData(["problem_statement"], data)
        }
      }),
      // Add listener for voice assistant toggle
      window.electronAPI.onToggleVoiceAssistant(() => {
        // Instead of toggling the popup, switch to voice view
        setView("voice")
      }),
      // 添加导航到语音视图的监听器
      window.electronAPI.onNavigateToVoiceView(() => {
        console.log('收到导航到语音视图的请求');
        setView("voice")
      })
    ]
    return () => cleanupFunctions.forEach((cleanup) => cleanup())
  }, [view])

  useEffect(() => {
    const checkApiKey = async () => {
      const apiKey = await window.electronAPI.getApiKey()
      if (apiKey) {
        // 如果存在 API Key，说明配置初始化已经在主进程中处理
        // 这里直接设置为已认证状态
        setIsAuthenticated(true)
      }
    }
    checkApiKey()
  }, [])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (toastTimeoutRef.current) {
        clearTimeout(toastTimeoutRef.current)
      }
    }
  }, [])

  const toggleVoiceAssistant = () => {
    // Instead of toggling visibility, switch to voice view
    setView("voice")
  }

  if (!isAuthenticated) {
    return <ApiKeyAuth onApiKeySubmit={handleApiKeySubmit} />
  }

  return (
    <div ref={containerRef} className="min-h-0">
      <QueryClientProvider client={queryClient}>
      <ToastProvider duration={3000}>
          <ToastContext.Provider value={{ showToast }}>
            {view === "queue" ? (
              <Queue setView={setView} currentView={view} />
            ) : view === "solutions" ? (
              <Solutions setView={setView} />
            ) : view === "voice" ? (
              <Voice setView={setView} />
            ) : (
              <></>
            )}
          </ToastContext.Provider>
          <Toast
            open={toastOpen}
            onOpenChange={(open) => {
              setToastOpen(open)
              // 当 toast 关闭时，清理定时器
              if (!open && toastTimeoutRef.current) {
                clearTimeout(toastTimeoutRef.current)
                toastTimeoutRef.current = null
              }
            }}
            variant={toastMessage.variant}
            type="foreground"
          >
            <ToastTitle>{toastMessage.title}</ToastTitle>
            <ToastDescription>{toastMessage.description}</ToastDescription>
          </Toast>
          <ToastViewport />
      </ToastProvider>
      </QueryClientProvider>
    </div>
  )
}

export default App
