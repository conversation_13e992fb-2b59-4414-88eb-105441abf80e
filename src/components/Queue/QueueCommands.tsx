import React, { useEffect, useRef, useState } from "react"
import ToolbarPositionReporter from "../Common/ToolbarPositionReporter"

interface QueueCommandsProps {
  onTooltipVisibilityChange: (visible: boolean, height: number) => void
  errorState?: boolean;
}

const QueueCommands: React.FC<QueueCommandsProps> = ({
  onTooltipVisibilityChange,
  errorState = false
}) => {
  const [isTooltipVisible, setIsTooltipVisible] = useState(false)
  const tooltipRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    let tooltipHeight = 0
    if (tooltipRef.current && isTooltipVisible) {
      tooltipHeight = tooltipRef.current.offsetHeight + 10 // Adding margin/padding if necessary
    }
    onTooltipVisibilityChange(isTooltipVisible, tooltipHeight)
  }, [isTooltipVisible])

  const handleMouseEnter = () => {
    setIsTooltipVisible(true)
  }

  const handleMouseLeave = () => {
    setIsTooltipVisible(false)
  }

  return (
    <ToolbarPositionReporter 
      modelSelector=".model-info"
      resetSelector=".reset-button"
      errorState={errorState}
    >
      <div
        className="relative inline-block"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {/* Question mark circle */}
        <div className="w-6 h-6 rounded-full bg-white/10 hover:bg-white/20 backdrop-blur-sm transition-colors flex items-center justify-center cursor-help z-10">
          <span className="text-xs text-white/70">?</span>
        </div>

        {/* Tooltip Content */}
        {isTooltipVisible && (
          <div ref={tooltipRef} className="absolute top-full right-0 mt-2 w-80">
            <div className="p-3 text-xs bg-black/80 backdrop-blur-md rounded-lg border border-white/10 text-white/90 shadow-lg">
              <div className="space-y-4">
                <h3 className="font-medium truncate">Keyboard Shortcuts</h3>
                <div className="space-y-3">
                  {/* Toggle Command */}
                  <div className="space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="truncate">Toggle Window</span>
                      <div className="flex gap-1 flex-shrink-0">
                        <span className="bg-white/10 px-1.5 py-0.5 rounded text-[10px] leading-none">
                          ⌘
                        </span>
                        <span className="bg-white/10 px-1.5 py-0.5 rounded text-[10px] leading-none">
                          B
                        </span>
                      </div>
                    </div>
                    <p className="text-[10px] leading-relaxed text-white/70 truncate">
                      Show or hide this window.
                    </p>
                  </div>
                  {/* Screenshot Command */}
                  <div className="space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="truncate">Take Screenshot</span>
                      <div className="flex gap-1 flex-shrink-0">
                        <span className="bg-white/10 px-1.5 py-0.5 rounded text-[10px] leading-none">
                          ⌘
                        </span>
                        <span className="bg-white/10 px-1.5 py-0.5 rounded text-[10px] leading-none">
                          H
                        </span>
                      </div>
                    </div>
                    <p className="text-[10px] leading-relaxed text-white/70 truncate">
                      Take a screenshot of the problem description. The tool will
                      extract and analyze the problem. The 5 latest screenshots
                      are saved.
                    </p>
                  </div>

                  {/* Solve Command */}
                  <div className="space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="truncate">Solve Problem</span>
                      <div className="flex gap-1 flex-shrink-0">
                        <span className="bg-white/10 px-1.5 py-0.5 rounded text-[10px] leading-none">
                          ⌘
                        </span>
                        <span className="bg-white/10 px-1.5 py-0.5 rounded text-[10px] leading-none">
                          ↵
                        </span>
                      </div>
                    </div>
                    <p className="text-[10px] leading-relaxed text-white/70 truncate">
                      Generate a solution based on the current problem.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </ToolbarPositionReporter>
  )
}

export default QueueCommands
