import React, { useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import AIResponse from './AIResponse.tsx';
// @ts-ignore - RecordRTC 类型定义可能不完整
import RecordRTC, { StereoAudioRecorder } from 'recordrtc';
interface VoiceRecognitionUIProps {
  onTranscription?: (text: string) => void;
  onSendToAI?: (text: string, mode: 'fast' | 'accurate') => void;
}

// 定义ref暴露的方法接口
export interface VoiceRecognitionUIRef {
  getServiceStates: () => { asrServiceActive: boolean; isSystemActive: boolean; isMicrophoneActive: boolean };
}

interface TranscriptionItem {
  id: number;
  text: string;
  timestamp: Date;
  source: 'system' | 'microphone';
  isFinal: boolean;
  isError?: boolean;
  paragraphId?: string;
}

// Define the ElectronAPI interface
interface ElectronAPI {
  invoke: (channel: string, ...args: any[]) => Promise<any>;
  on: (channel: string, callback: (...args: any[]) => void) => void;
  off: (channel: string, callback: (...args: any[]) => void) => void;
  startMicrophoneCapturing: () => Promise<{ success: boolean, error?: string }>;
  stopMicrophoneCapturing: () => Promise<{ success: boolean, error?: string }>;
  processMicrophoneAudio: (audioData: any) => Promise<{ success: boolean, error?: string }>;
  getMicrophoneStatus: () => Promise<{ capturing: boolean }>;
  onMicrophoneStatus: (callback: (event: any, data: any) => void) => () => void;
  onAsrTranscription: (callback: (event: any, data: any) => void) => () => void;
  onHistoryUpdate: (callback: (event: any, data: any) => void) => () => void;
  onUnifiedHistoryUpdate?: (callback: (event: any, data: any) => void) => () => void;
  onFastVoiceTextChunk: (callback: (event: any, data: { id: string, chunk: string, done: boolean, error?: boolean }) => void) => () => void;
  onAccurateVoiceTextChunk: (callback: (event: any, data: { id: string, chunk: string, done: boolean, error?: boolean }) => void) => () => void;
}

// Access the API safely
const electronAPI: ElectronAPI | undefined = (window as any).electronAPI;

const VoiceRecognitionUI = forwardRef<VoiceRecognitionUIRef, VoiceRecognitionUIProps>(({ onTranscription }, ref) => {
  const [isSystemActive, setIsSystemActive] = useState(false);
  const [isMicrophoneActive, setIsMicrophoneActive] = useState(false);
  const [transcriptionHistory, setTranscriptionHistory] = useState<TranscriptionItem[]>([]);
  const [systemTranscript, setSystemTranscript] = useState<string>('');
  const [microphoneTranscript, setMicrophoneTranscript] = useState<string>('');
  const [isProcessingAI, setIsProcessingAI] = useState<{fast: boolean, accurate: boolean}>({fast: false, accurate: false});
  const historyContainerRef = useRef<HTMLDivElement>(null);
  const chatContentRef = useRef<HTMLDivElement>(null);

  // AI回复框的引用
  const fastResponseRef = useRef<HTMLDivElement>(null);
  const accurateResponseRef = useRef<HTMLDivElement>(null);
  
  // 添加AI响应状态
  const [fastResponse, setFastResponse] = useState<string>('');
  const [accurateResponse, setAccurateResponse] = useState<string>('');
  
  // 添加ASR服务状态
  const [asrServiceActive, setAsrServiceActive] = useState(false);
  const [asrServiceInitializing, setAsrServiceInitializing] = useState(false);
  const [asrServiceError, setAsrServiceError] = useState<string | null>(null);

  // 处理系统音频文字转写
  const handleSystemTranscription = (text: string) => {
    setSystemTranscript(text);
    if (onTranscription) {
      onTranscription(text);
    }
  };

  // 处理麦克风音频文字转写
  const handleMicrophoneTranscription = (text: string) => {
    setMicrophoneTranscript(text);
    if (onTranscription) {
      onTranscription(text);
    }
  };

  // 添加文本到历史记录的统一函数
  const addToHistory = (text: string, source: 'system' | 'microphone', timestamp: Date, paragraphId?: string) => {
    // 只是临时添加到UI显示，最终数据会由主进程的历史更新替换
    if (!text.trim()) return;
    
    const newItem: TranscriptionItem = {
      id: Date.now() + Math.random(),
      text: text.trim(),
      timestamp,
      source,
      isFinal: true,
      paragraphId
    };

    console.log(`添加临时转录结果 (${source}): "${text}"`);
    setTranscriptionHistory(prev => [...prev, newItem]);
  };

  // 从VoiceToText组件接收最终转录结果
  const handleSystemFinalTranscription = (text: string, timestamp: Date, paragraphId?: string) => {
    if (text.trim()) {
      addToHistory(text, 'system', timestamp, paragraphId);
    }
  };

  // 从MicrophoneToText组件接收最终转录结果
  const handleMicrophoneFinalTranscription = (text: string, timestamp: Date, paragraphId?: string) => {
    if (text.trim()) {
      addToHistory(text, 'microphone', timestamp, paragraphId);
    }
  };



  // 一键启动所有服务 - 重构版本
  const toggleAllServices = async () => {
    if (!electronAPI) {
      console.error('❌ ElectronAPI 不可用');
      setAsrServiceError('ElectronAPI 不可用，请确保在 Electron 环境中运行');
      return;
    }

    if (asrServiceActive) {
      // 关闭所有服务
      console.log('🛑 开始关闭所有语音识别服务...');
      setAsrServiceInitializing(true);
      setAsrServiceError(null);

      try {
        // 第一步：记录麦克风是否正在运行，然后立即设置停止标志
        const wasMicrophoneActive = isMicrophoneActive;
        if (wasMicrophoneActive) {
          console.log('🔇 检测到麦克风正在运行，设置停止标志...');
          (window as any).microphoneActive = false;
          setIsMicrophoneActive(false);
        }

        // 第二步：调用主进程停止方法
        console.log('� 第二步：调用主进程停止语音识别服务...');
        const result = await electronAPI.invoke('voice-recognition:stop');
        console.log('🛑 voice-recognition:stop 返回结果:', result);

        // 第三步：强制释放所有麦克风资源（无论状态如何）
        console.log('🔇 第三步：强制释放所有麦克风资源...');

        // 等待主进程操作完成
        await new Promise(resolve => setTimeout(resolve, 300));

        // 执行多轮资源释放，确保彻底清理
        let releaseAttempts = 0;
        const maxReleaseAttempts = 3;
        let finalSuccess = false;

        while (releaseAttempts < maxReleaseAttempts && !finalSuccess) {
          releaseAttempts++;
          console.log(`🔄 执行第 ${releaseAttempts} 轮资源释放...`);

          const resourcesReleased = await forceReleaseMicrophoneResources();

          if (resourcesReleased) {
            console.log(`✅ 第 ${releaseAttempts} 轮资源释放成功`);
            finalSuccess = true;
          } else {
            console.warn(`⚠️ 第 ${releaseAttempts} 轮资源释放未完全成功`);
            if (releaseAttempts < maxReleaseAttempts) {
              console.log('🔄 等待后重试...');
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          }
        }

        if (finalSuccess) {
          console.log('🎉 麦克风资源最终释放成功');

          // 等待一段时间后进行诊断
          setTimeout(async () => {
            console.log('🔍 执行释放后诊断...');
            await diagnoseMicrophoneStatus();
          }, 1000);

        } else {
          console.error('❌ 麦克风资源释放失败，可能仍在占用');
          setAsrServiceError('麦克风资源释放失败，请检查系统权限');

          // 失败时也进行诊断
          setTimeout(async () => {
            console.log('🔍 执行失败诊断...');
            await diagnoseMicrophoneStatus();
          }, 500);
        }

        // 无论主进程返回什么结果，都重置UI状态
        // 因为浏览器端的资源已经被释放了
        console.log('🔄 重置所有UI状态...');
        setAsrServiceActive(false);
        setIsSystemActive(false);
        setIsMicrophoneActive(false);

        // 清除所有历史记录和实时转录
        console.log('🧹 清除所有历史记录和实时转录...');
        setTranscriptionHistory([]);
        setSystemTranscript('');
        setMicrophoneTranscript('');
        setFastResponse('');
        setAccurateResponse('');

        if (result.success) {
          console.log('✅ 语音识别服务已关闭');
          setAsrServiceError(null);
        } else {
          console.error('❌ 主进程关闭服务失败，但UI状态已重置:', result.error);
          setAsrServiceError(result.error || '主进程关闭服务失败，但浏览器端资源已释放');
        }
      } catch (error) {
        console.error('💥 关闭语音识别服务出错:', error);
        // 即使出错也要重置状态，因为浏览器端资源已经被释放
        console.log('🔄 异常情况下强制重置所有UI状态...');
        setAsrServiceActive(false);
        setIsSystemActive(false);
        setIsMicrophoneActive(false);

        // 异常情况下也要清除所有历史记录和实时转录
        console.log('🧹 异常情况下清除所有历史记录和实时转录...');
        setTranscriptionHistory([]);
        setSystemTranscript('');
        setMicrophoneTranscript('');
        setFastResponse('');
        setAccurateResponse('');

        setAsrServiceError(error instanceof Error ? error.message : '关闭语音识别服务时出错');
      } finally {
        setAsrServiceInitializing(false);
      }
    } else {
      // 启动所有服务
      console.log('🚀 开始一键启动所有语音识别服务...');
      setAsrServiceInitializing(true);
      setAsrServiceError(null);

      // 重置状态
      setIsSystemActive(false);
      setIsMicrophoneActive(false);

      try {
        // 使用统一的语音识别流程启动服务
        console.log('📞 调用 voice-recognition:start-flow，启用系统音频和麦克风');
        const result = await electronAPI.invoke('voice-recognition:start-flow', {
          enableSystemAudio: true,
          enableMicrophone: true
        });
        console.log('📞 voice-recognition:start-flow 返回结果:', result);

        if (result.success) {
          console.log('✅ 语音识别服务启动成功');
          setAsrServiceActive(true);
          setAsrServiceError(null);

          // 根据实际启动结果更新UI状态
          if (result.systemAudio && result.systemAudio.success) {
            setIsSystemActive(true);
            console.log('✅ 系统音频已启动');
          } else {
            console.warn('⚠️ 系统音频启动失败:', result.systemAudio?.error);
          }

          if (result.microphone && result.microphone.success) {
            setIsMicrophoneActive(true);
            console.log('✅ 麦克风已启动');
          } else {
            console.warn('⚠️ 麦克风启动失败:', result.microphone?.error);
          }

          // 如果部分服务启动失败，显示警告
          if (!result.systemAudio?.success || !result.microphone?.success) {
            const failedServices = [];
            if (!result.systemAudio?.success) failedServices.push('系统音频');
            if (!result.microphone?.success) failedServices.push('麦克风');

            setAsrServiceError(`部分服务启动失败: ${failedServices.join(', ')}`);
          }
        } else {
          console.error('❌ 启动语音识别服务失败:', result.error);
          setAsrServiceError(result.error || '启动语音识别服务失败');

          // 确保状态正确重置
          setAsrServiceActive(false);
          setIsSystemActive(false);
          setIsMicrophoneActive(false);
        }
      } catch (error) {
        console.error('💥 启动语音识别服务出错:', error);
        setAsrServiceError(error instanceof Error ? error.message : '启动语音识别服务时出错');

        // 确保状态正确重置
        setAsrServiceActive(false);
        setIsSystemActive(false);
        setIsMicrophoneActive(false);
      } finally {
        setAsrServiceInitializing(false);
      }
    }
  };



  // 启动系统音频捕获
  const startSystemAudio = async (): Promise<void> => {
    console.log('🚀 开始启动系统音频捕获...');

    if (!electronAPI) {
      console.error('❌ ElectronAPI 不可用');
      setAsrServiceError('ElectronAPI 不可用，请确保在 Electron 环境中运行');
      return;
    }

    try {
      console.log('📞 调用主进程启动系统音频捕获...');
      const result = await electronAPI.invoke('system-audio:start-capturing');
      if (result.success) {
        console.log('✅ 系统音频捕获已启动');
        setIsSystemActive(true);
      } else {
        console.error('❌ 启动系统音频捕获失败:', result.error);
        setAsrServiceError(result.error || '启动系统音频捕获失败');
      }
    } catch (error) {
      console.error('💥 启动系统音频捕获出错:', error);
      setAsrServiceError(error instanceof Error ? error.message : '启动系统音频捕获时出错');
    }
  };
  
  // 停止系统音频捕获
  const stopSystemAudio = async (): Promise<void> => {
    console.log('🛑 开始停止系统音频捕获...');

    if (!electronAPI) {
      console.error('❌ ElectronAPI 不可用');
      return;
    }

    if (isSystemActive) {
      try {
        console.log('📞 调用主进程停止系统音频捕获...');
        const result = await electronAPI.invoke('system-audio:stop-capturing');
        if (result.success) {
          console.log('✅ 系统音频捕获已停止');
          setIsSystemActive(false);
        } else {
          console.error('❌ 停止系统音频捕获失败:', result.error);
          // 即使主进程停止失败，也更新UI状态
          setIsSystemActive(false);
        }
      } catch (error) {
        console.error('❌ 停止系统音频捕获出错:', error);
        // 即使出错，也更新UI状态
        setIsSystemActive(false);
      }
    } else {
      console.log('⚠️ 系统音频未处于活动状态');
    }
  };

  // 监听自动启动麦克风识别的事件
  useEffect(() => {
    console.log('🎤 VoiceRecognitionUI 组件已挂载，按钮应该已经渲染');

    if (!electronAPI) {
      console.warn('ElectronAPI 不可用，无法监听麦克风识别事件');
      return;
    }

    const handleStartMicrophoneRecognition = () => {
      console.log('🎤 收到启动麦克风识别的事件');
      // 延迟启动麦克风识别，确保ASR服务已完全就绪
      setTimeout(() => {
        console.log('🎤 延迟1秒后开始启动麦克风识别...');
        startMicrophoneRecognition();
      }, 1000); // 延迟1秒确保ASR服务完全启动
    };

    console.log('📡 注册 start-microphone-recognition 事件监听器');
    electronAPI.on('start-microphone-recognition', handleStartMicrophoneRecognition);

    return () => {
      console.log('📡 移除 start-microphone-recognition 事件监听器');
      electronAPI.off('start-microphone-recognition', handleStartMicrophoneRecognition);
    };
  }, []);
  
  // 监听麦克风状态变化
  useEffect(() => {
    if (!electronAPI || !electronAPI.onMicrophoneStatus) {
      console.warn('ElectronAPI 或 onMicrophoneStatus 不可用');
      return;
    }

    const unsubscribe = electronAPI.onMicrophoneStatus((_, data) => {
      console.log('麦克风状态变化:', data);
      setIsMicrophoneActive(data.recording);
    });
    
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  // 上报AI回复框位置信息
  useEffect(() => {
    if (!electronAPI) {
      console.warn('ElectronAPI 不可用，无法上报AI回复框位置');
      return;
    }

    const reportAIResponsePositions = () => {
      const fastElement = fastResponseRef.current;
      const accurateElement = accurateResponseRef.current;

      if (fastElement && accurateElement) {
        const fastRect = fastElement.getBoundingClientRect();
        const accurateRect = accurateElement.getBoundingClientRect();

        const positions = {
          fastResponse: {
            x: Math.round(fastRect.left),
            y: Math.round(fastRect.top),
            width: Math.round(fastRect.width),
            height: Math.round(fastRect.height),
            type: 'ai-response-fast'
          },
          accurateResponse: {
            x: Math.round(accurateRect.left),
            y: Math.round(accurateRect.top),
            width: Math.round(accurateRect.width),
            height: Math.round(accurateRect.height),
            type: 'ai-response-accurate'
          }
        };

        console.log('📍 上报AI回复框位置:', positions);

        // 发送位置信息到主进程
        electronAPI.invoke('voice-ai-response-positions', positions).catch(error => {
          console.error('上报AI回复框位置失败:', error);
        });
      }
    };

    // 延迟上报，确保组件完全渲染
    const timer = setTimeout(reportAIResponsePositions, 1000);

    // 监听窗口大小变化，重新上报位置
    const handleResize = () => {
      setTimeout(reportAIResponsePositions, 100);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 监听AI回复框滚动事件
  useEffect(() => {
    if (!electronAPI) {
      console.warn('ElectronAPI 不可用，无法监听AI回复框滚动事件');
      return;
    }

    const handleAIResponseScroll = (_: any, data: { type: 'fast' | 'accurate'; scrollData: { deltaY: number; deltaX: number } }) => {
      console.log('🖱️ 收到AI回复框滚动事件:', data);

      // 根据类型找到对应的AI回复框内容元素并执行滚动
      const targetRef = data.type === 'fast' ? fastResponseRef : accurateResponseRef;
      const targetElement = targetRef.current;

      if (targetElement) {
        // 查找AI回复框内的内容滚动容器
        const scrollContainer = targetElement.querySelector('.ai-response-content') as HTMLElement;
        if (scrollContainer) {
          // 获取当前滚动位置
          const currentScrollTop = scrollContainer.scrollTop;
          const maxScrollTop = scrollContainer.scrollHeight - scrollContainer.clientHeight;

          // 计算新的滚动位置，确保不超出边界
          const newScrollTop = Math.max(0, Math.min(maxScrollTop, currentScrollTop + data.scrollData.deltaY));

          // 执行滚动，使用平滑滚动提升用户体验
          scrollContainer.scrollTo({
            top: newScrollTop,
            left: scrollContainer.scrollLeft + data.scrollData.deltaX,
            behavior: 'smooth'
          });

          console.log(`✅ ${data.type === 'fast' ? '极速' : '精确'}模式AI回复框滚动执行完成, 从 ${currentScrollTop} 滚动到 ${newScrollTop}`);
        } else {
          console.warn(`⚠️ 未找到${data.type === 'fast' ? '极速' : '精确'}模式AI回复框的滚动容器`);
        }
      } else {
        console.warn(`⚠️ 未找到${data.type === 'fast' ? '极速' : '精确'}模式AI回复框元素`);
      }
    };

    console.log('📡 注册AI回复框滚动事件监听器');
    electronAPI.on('ai-response-scroll', handleAIResponseScroll);

    return () => {
      console.log('📡 移除AI回复框滚动事件监听器');
      electronAPI.off('ai-response-scroll', handleAIResponseScroll);
    };
  }, []);

  // 监听语音助手状态变化
  useEffect(() => {
    if (!electronAPI) {
      console.warn('ElectronAPI 不可用，无法监听语音助手状态变化');
      return;
    }

    const handleVoiceAssistantToggle = (_: any, data: any) => {
      console.log('收到语音助手状态变化:', data);
        // 更新ASR服务状态
      if (data.asrConnected !== undefined) {
        setAsrServiceActive(data.asrConnected);
        setAsrServiceInitializing(false); // 不论成功失败，初始化状态都结束
        
        // 如果成功连接，清除之前的错误信息
        if (data.asrConnected) {
          setAsrServiceError(null);
        }
      }
      
      // 更新录音状态
      if (data.recording !== undefined) {
        setIsSystemActive(data.recording);
      }
      
      // 处理错误信息
      if (data.status === 'error' && data.error) {
        setAsrServiceError(data.error);
      } else {
        setAsrServiceError(null);
      }
    };
    
    electronAPI.on('toggle-voice-assistant', handleVoiceAssistantToggle);
    
    return () => {
      electronAPI.off('toggle-voice-assistant', handleVoiceAssistantToggle);
    };
  }, []);
  
  // 监听ASR转写结果
  useEffect(() => {
    if (!electronAPI || !electronAPI.onAsrTranscription) {
      console.warn('ElectronAPI 或 onAsrTranscription 不可用');
      return;
    }

    const unsubscribe = electronAPI.onAsrTranscription((_, data) => {
      console.log('收到ASR转写结果:', data);

      // 根据音频来源处理转写结果
      if (data.source === 'microphone') {
        if (data.isFinal) {
          // 麦克风最终结果
          handleMicrophoneFinalTranscription(data.text, new Date(data.timestamp), data.paragraphId);
        } else {
          // 麦克风临时结果
          handleMicrophoneTranscription(data.text);
        }
      } else if (data.source === 'system') {
        if (data.isFinal) {
          // 系统音频最终结果
          handleSystemFinalTranscription(data.text, new Date(data.timestamp), data.paragraphId);
        } else {
          // 系统音频临时结果
          handleSystemTranscription(data.text);
        }
      } else {
        // 没有指定来源，默认为系统音频
        console.warn('ASR转写结果未指定音频来源:', data);
        if (data.isFinal) {
          handleSystemFinalTranscription(data.text, new Date(data.timestamp), data.paragraphId);
        } else {
          handleSystemTranscription(data.text);
        }
      }
    });
    
    // 监听统一历史记录更新（优先使用）
    let unifiedHistoryUnsubscribe: (() => void) | undefined;
    if (electronAPI.onUnifiedHistoryUpdate) {
      console.log('✅ 统一历史管理器监听器已设置');

      unifiedHistoryUnsubscribe = electronAPI.onUnifiedHistoryUpdate((_, data) => {
        const timestamp = new Date().toISOString();
        console.log(`🔄 [${timestamp}] 收到统一历史记录更新:`, data);

        if (data.history && Array.isArray(data.history) && data.history.length > 0) {
          // 直接使用统一历史管理器提供的已处理历史数据
          const historyItems: TranscriptionItem[] = data.history.map((item: any) => ({
            id: `${item.source}-${item.timestamp}-${Math.random()}`, // 确保唯一ID
            text: item.text,
            timestamp: new Date(item.timestamp),
            source: item.source === 'microphone' ? 'microphone' : 'system',
            isFinal: item.isFinal || true,
            isError: item.isError || false,
            paragraphId: item.paragraphId
          }));

          // 按来源分组统计
          const sourceStats = historyItems.reduce((acc, item) => {
            acc[item.source] = (acc[item.source] || 0) + 1;
            return acc;
          }, {} as Record<string, number>);

          console.log(`🔄 [${timestamp}] 统一历史更新: 设置 ${historyItems.length} 条历史记录`, sourceStats);

          // 直接设置历史记录，不进行复杂的比较
          setTranscriptionHistory(historyItems);
          console.log(`🔄 [${timestamp}] 历史记录已更新到UI`);

          // 添加到window对象用于调试
          (window as any).lastHistoryUpdate = {
            timestamp,
            count: historyItems.length,
            sourceStats,
            items: historyItems
          };

        } else {
          console.log(`🔄 [${timestamp}] 收到空的统一历史更新`);
        }
      });
    } else {
      console.error('❌ onUnifiedHistoryUpdate 不可用，统一历史管理器可能未正确初始化');
      console.error('❌ 可用的electronAPI方法:', Object.keys(electronAPI));
    }

    // 完全禁用传统历史记录更新监听，强制使用统一历史更新
    let historyUnsubscribe: (() => void) | undefined;

    // 临时监听传统事件，但只用于调试日志
    if (electronAPI.onHistoryUpdate) {
      historyUnsubscribe = electronAPI.onHistoryUpdate((_, data) => {
        console.warn('⚠️ 收到传统历史记录更新事件（已忽略）:', data);
        console.warn('⚠️ 请检查是否还有代码在发送 asr:history-update 事件');
        // 不处理传统历史更新，完全依赖统一历史管理器
      });
    }
    
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
      if (unifiedHistoryUnsubscribe) {
        unifiedHistoryUnsubscribe();
      }
      if (historyUnsubscribe) {
        historyUnsubscribe();
      }
    };
  }, []);

  // 优化的滚动到底部功能
  useEffect(() => {
    const container = historyContainerRef.current;
    
    if (container) {
      // 保存当前滚动位置
      const wasAtTop = container.scrollTop === 0;
      
      // 如果之前在顶部(实际是底部，因为我们使用了flex-col-reverse)，则保持在顶部
      // 否则保持当前滚动位置
      if (wasAtTop) {
        // 使用setTimeout确保DOM更新后再滚动
        setTimeout(() => {
          if (container) {
            container.scrollTop = 0;
          }
        }, 0);
      }
    }
  }, [transcriptionHistory, systemTranscript, microphoneTranscript]);
  
  // 通过useImperativeHandle暴露状态检查方法给父组件
  useImperativeHandle(ref, () => ({
    getServiceStates: () => {
      const states = {
        asrServiceActive,
        isSystemActive,
        isMicrophoneActive
      };
      console.log('🔍 通过ref调用状态检查，返回:', states);
      return states;
    }
  }), [asrServiceActive, isSystemActive, isMicrophoneActive]);

  // 清理函数，在组件卸载时停止麦克风音频捕获
  useEffect(() => {
    return () => {
      // 停止麦克风音频捕获
      if (isMicrophoneActive && electronAPI) {
        electronAPI.stopMicrophoneCapturing().catch(error => {
          console.error('停止麦克风音频捕获时出错:', error);
        });
      }

      // 清理音频资源
      const microphoneAudioContext = (window as any).microphoneAudioContext;
      const microphoneProcessor = (window as any).microphoneProcessor;
      const microphoneStream = (window as any).microphoneStream;

      if (microphoneProcessor && microphoneAudioContext) {
        try {
          microphoneProcessor.disconnect();
          microphoneAudioContext.close();
        } catch (error) {
          console.error('清理音频资源时出错:', error);
        }
      }

      if (microphoneStream) {
        try {
          microphoneStream.getTracks().forEach((track: MediaStreamTrack) => track.stop());
        } catch (error) {
          console.error('停止麦克风流时出错:', error);
        }
      }
    };
  }, [isMicrophoneActive]);

  // 格式化时间戳
  const formatTimeWithMs = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // 添加RMS音量计算函数
  const calculateRMS = (audioData: Float32Array | number[]): number => {
    let sumOfSquares = 0;
    for (let i = 0; i < audioData.length; i++) {
      sumOfSquares += audioData[i] * audioData[i];
    }
    const meanSquare = sumOfSquares / audioData.length;
    return Math.sqrt(meanSquare);
  };

  // 启动麦克风音频捕获
  const startMicrophoneRecognition = async () => {
    console.log('🎤 开始启动麦克风音频捕获...');

    if (!electronAPI) {
      console.error('❌ ElectronAPI 不可用');
      setAsrServiceError('ElectronAPI 不可用，请确保在 Electron 环境中运行');
      return;
    }

    try {
      // 首先确保主进程的麦克风管理器已启动
      console.log('📞 确保主进程麦克风管理器已启动...');
      const result = await electronAPI.startMicrophoneCapturing();
      console.log('📞 主进程麦克风管理器启动结果:', result);

      if (result.success) {
        console.log('✅ 主进程麦克风管理器已启动，开始浏览器端音频捕获');
        setIsMicrophoneActive(true);

        // 开始捕获麦克风音频
        console.log('🎵 开始捕获麦克风音频...');
        startMicrophoneAudioCapture();
      } else {
        console.error('❌ 主进程麦克风管理器启动失败:', result.error);
        setAsrServiceError(result.error || '启动主进程麦克风管理器失败');
      }

    } catch (error) {
      console.error('💥 启动麦克风音频捕获时出错:', error);
      setAsrServiceError(error instanceof Error ? error.message : '启动麦克风音频捕获时出错');
    }
  };
  
  // 诊断麦克风状态的详细函数
  const diagnoseMicrophoneStatus = async (): Promise<void> => {
    console.log('🔍 开始诊断麦克风状态...');

    try {
      // 检查权限状态
      if (navigator.permissions) {
        try {
          const micPermission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
          console.log(`🎤 麦克风权限状态: ${micPermission.state}`);
        } catch (e) {
          console.log('ℹ️ 无法查询麦克风权限状态');
        }
      }

      // 检查媒体设备
      if (navigator.mediaDevices) {
        try {
          const devices = await navigator.mediaDevices.enumerateDevices();
          const audioInputs = devices.filter(device => device.kind === 'audioinput');
          console.log(`🎤 可用音频输入设备数量: ${audioInputs.length}`);
          audioInputs.forEach((device, index) => {
            console.log(`  设备 ${index + 1}: ${device.label || '未知设备'} (ID: ${device.deviceId})`);
          });
        } catch (e) {
          console.warn('⚠️ 无法枚举媒体设备:', e);
        }
      }

      // 检查全局变量
      const globalVars = [
        'microphoneStream', 'microphoneAudioContext', 'microphoneProcessor',
        'microphoneWorkletNode', 'microphoneActive'
      ];

      console.log('🔍 检查全局变量状态:');
      globalVars.forEach(varName => {
        const value = (window as any)[varName];
        if (value) {
          console.log(`  ❌ ${varName}: 仍然存在`);
          if (value.getTracks && typeof value.getTracks === 'function') {
            const tracks = value.getTracks();
            console.log(`    包含 ${tracks.length} 个音轨`);
            tracks.forEach((track: MediaStreamTrack, i: number) => {
              console.log(`      音轨 ${i + 1}: ${track.label} (状态: ${track.readyState})`);
            });
          }
          if (value.state !== undefined) {
            console.log(`    状态: ${value.state}`);
          }
        } else {
          console.log(`  ✅ ${varName}: 已清除`);
        }
      });

    } catch (error) {
      console.error('❌ 诊断麦克风状态时出错:', error);
    }
  };

  // 验证麦克风资源是否真正被释放
  const verifyMicrophoneResourcesReleased = async (): Promise<{success: boolean, issues: string[]}> => {
    const issues: string[] = [];

    try {
      // 检查全局引用是否已清除
      if ((window as any).microphoneStream) {
        issues.push('microphoneStream 引用未清除');
      }

      if ((window as any).microphoneAudioContext) {
        const context = (window as any).microphoneAudioContext;
        if (context.state !== 'closed') {
          issues.push(`AudioContext 状态不是 closed: ${context.state}`);
        }
      }

      if ((window as any).microphoneWorkletNode) {
        issues.push('microphoneWorkletNode 引用未清除');
      }

      if ((window as any).microphoneProcessor) {
        issues.push('microphoneProcessor 引用未清除');
      }

      return {
        success: issues.length === 0,
        issues
      };
    } catch (error) {
      console.error('验证麦克风资源时出错:', error);
      return {
        success: false,
        issues: [`验证过程出错: ${error instanceof Error ? error.message : '未知错误'}`]
      };
    }
  };

  // 强制释放麦克风资源函数 - 更彻底的清理
  const forceReleaseMicrophoneResources = async (): Promise<boolean> => {
    console.log('💪 开始强制释放麦克风资源...');

    try {
      // 第一步：立即停止所有可能的MediaStream
      console.log('🔇 第一步：搜索并停止所有MediaStream...');

      // 检查全局变量中的stream
      const globalStreams = [
        (window as any).microphoneStream,
        (window as any).stream,
        (window as any).mediaStream,
        (window as any).audioStream
      ].filter(Boolean);

      console.log(`🔍 发现 ${globalStreams.length} 个全局MediaStream引用`);

      for (const stream of globalStreams) {
        if (stream && typeof stream.getTracks === 'function') {
          const tracks = stream.getTracks();
          console.log(`🔇 处理MediaStream，包含 ${tracks.length} 个音轨`);

          tracks.forEach((track: MediaStreamTrack, index: number) => {
            console.log(`🔇 强制停止音轨 ${index + 1}: ${track.label} (状态: ${track.readyState})`);
            try {
              track.stop();
            } catch (e) {
              console.warn(`⚠️ 停止音轨 ${index + 1} 时出错:`, e);
            }
          });

          // 验证停止状态
          await new Promise(resolve => setTimeout(resolve, 100));
          const stoppedTracks = tracks.filter((track: MediaStreamTrack) => track.readyState === 'ended');
          console.log(`✅ ${stoppedTracks.length}/${tracks.length} 个音轨已停止`);
        }
      }

      // 第二步：尝试创建新的MediaStream来"刷新"权限状态
      try {
        console.log('🔇 第二步：尝试刷新媒体权限状态...');

        // 创建一个临时的MediaStream来触发权限检查
        const tempStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false
          }
        });

        console.log('🔇 临时MediaStream已创建，立即停止...');

        // 立即停止临时流
        tempStream.getTracks().forEach((track: MediaStreamTrack) => {
          console.log(`🔇 停止临时音轨: ${track.label}`);
          track.stop();
        });

        // 等待临时流完全停止
        await new Promise(resolve => setTimeout(resolve, 200));
        console.log('✅ 临时MediaStream已停止');

      } catch (e) {
        console.log('ℹ️ 无法创建临时MediaStream（可能权限已被撤销）:', e);
      }

      // 第三步：强制关闭所有AudioContext
      console.log('🔇 第三步：强制关闭所有AudioContext...');

      const audioContexts = [
        (window as any).microphoneAudioContext,
        (window as any).audioContext,
        (window as any).context
      ].filter(Boolean);

      console.log(`🔍 发现 ${audioContexts.length} 个AudioContext引用`);

      for (const context of audioContexts) {
        if (context && typeof context.close === 'function') {
          try {
            console.log(`🔇 强制关闭AudioContext，当前状态: ${context.state}`);

            if (context.state !== 'closed') {
              if (context.state === 'running') {
                await context.suspend();
                console.log('🔇 AudioContext已暂停');
              }

              await context.close();
              console.log('🔇 AudioContext关闭命令已发送');

              // 等待关闭完成
              let attempts = 0;
              while (context.state !== 'closed' && attempts < 15) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
                console.log(`🔇 等待AudioContext关闭... (${attempts}/15) 状态: ${context.state}`);
              }

              if (context.state === 'closed') {
                console.log('✅ AudioContext已强制关闭');
              } else {
                console.warn('⚠️ AudioContext未能完全关闭');
              }
            }
          } catch (e) {
            console.warn('⚠️ 强制关闭AudioContext时出错:', e);
          }
        }
      }

      // 第四步：清除所有全局引用
      console.log('🔇 第四步：清除所有全局引用...');
      const globalRefs = [
        'microphoneStream', 'stream', 'mediaStream', 'audioStream',
        'microphoneAudioContext', 'audioContext', 'context',
        'microphoneProcessor', 'processor', 'scriptProcessor',
        'microphoneWorkletNode', 'workletNode', 'audioWorklet',
        'microphoneSource', 'audioSource', 'mediaSource',
        'microphoneActive', 'audioActive'
      ];

      globalRefs.forEach(ref => {
        if ((window as any)[ref]) {
          console.log(`🧹 清除全局引用: ${ref}`);
          (window as any)[ref] = null;
        }
      });

      // 第五步：强制垃圾回收
      if ((window as any).gc) {
        try {
          (window as any).gc();
          console.log('🗑️ 强制垃圾回收已执行');
        } catch (e) {
          console.log('🗑️ 垃圾回收不可用');
        }
      }

      // 第六步：等待系统处理
      console.log('⏳ 等待系统处理资源释放...');
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log('💪 强制资源释放完成');
      return true;

    } catch (error) {
      console.error('❌ 强制释放麦克风资源时发生错误:', error);
      return false;
    }
  };

  // 改进的麦克风资源释放函数 - 支持RecordRTC
  const releaseMicrophoneResources = async (): Promise<boolean> => {
    console.log('� 开始释放麦克风资源 (包括RecordRTC)...');

    try {
      const microphoneRecorder = (window as any).microphoneRecorder;
      const microphoneAudioContext = (window as any).microphoneAudioContext;
      const microphoneProcessor = (window as any).microphoneProcessor;
      const microphoneWorkletNode = (window as any).microphoneWorkletNode;
      const microphoneStream = (window as any).microphoneStream;

      // 第零步：停止RecordRTC录制器
      if (microphoneRecorder) {
        console.log('🔇 第零步：停止RecordRTC录制器...');
        try {
          microphoneRecorder.stopRecording();
          console.log('✅ RecordRTC录制器已停止');
        } catch (e) {
          console.error('❌ 停止RecordRTC录制器时出错:', e);
        }
      }

      // 第一步：立即停止MediaStream（最重要！）
      if (microphoneStream) {
        console.log('🔇 第一步：停止MediaStream...');
        try {
          const tracks = microphoneStream.getTracks();
          console.log(`🔇 发现 ${tracks.length} 个音轨`);

          // 立即停止所有音轨
          tracks.forEach((track: MediaStreamTrack) => {
            console.log(`🔇 立即停止音轨: ${track.label} (状态: ${track.readyState})`);
            track.stop();
          });

          // 验证所有音轨是否真正停止
          let allStopped = false;
          let attempts = 0;
          const maxAttempts = 20; // 最多等待1秒

          while (!allStopped && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 50));
            attempts++;

            allStopped = tracks.every((track: MediaStreamTrack) => track.readyState === 'ended');
            console.log(`🔇 验证音轨状态 (尝试 ${attempts}/${maxAttempts}): ${tracks.map((t: MediaStreamTrack) => `${t.label}=${t.readyState}`).join(', ')}`);
          }

          if (allStopped) {
            console.log('✅ 所有MediaStream音轨已确认停止');
          } else {
            console.warn('⚠️ 部分音轨可能未完全停止，但继续释放其他资源');
          }
        } catch (e) {
          console.error('❌ 停止MediaStream时出错:', e);
          return false;
        }
      }

      // 第二步：移除事件监听器
      console.log('🔇 第二步：移除事件监听器...');
      if (microphoneWorkletNode) {
        try {
          microphoneWorkletNode.port.onmessage = null;
          console.log('✅ AudioWorklet 事件监听器已移除');
        } catch (e) {
          console.warn('⚠️ 移除 AudioWorklet 事件监听器时出错:', e);
        }
      }

      if (microphoneProcessor) {
        try {
          microphoneProcessor.onaudioprocess = null;
          console.log('✅ ScriptProcessor 事件监听器已移除');
        } catch (e) {
          console.warn('⚠️ 移除 ScriptProcessor 事件监听器时出错:', e);
        }
      }

      // 第三步：断开音频节点连接
      console.log('🔇 第三步：断开音频节点连接...');
      if (microphoneWorkletNode) {
        try {
          microphoneWorkletNode.disconnect();
          console.log('✅ AudioWorklet 已断开连接');
        } catch (e) {
          console.warn('⚠️ 断开 AudioWorklet 连接时出错:', e);
        }
      }

      if (microphoneProcessor) {
        try {
          microphoneProcessor.disconnect();
          console.log('✅ ScriptProcessor 已断开连接');
        } catch (e) {
          console.warn('⚠️ 断开 ScriptProcessor 连接时出错:', e);
        }
      }

      // 第四步：关闭AudioContext
      if (microphoneAudioContext) {
        console.log('🔇 第四步：关闭AudioContext...');
        try {
          console.log(`🔇 AudioContext当前状态: ${microphoneAudioContext.state}`);

          if (microphoneAudioContext.state !== 'closed') {
            // 先暂停，再关闭
            if (microphoneAudioContext.state === 'running') {
              await microphoneAudioContext.suspend();
              console.log('🔇 AudioContext已暂停');
              await new Promise(resolve => setTimeout(resolve, 100));
            }

            await microphoneAudioContext.close();
            console.log('🔇 AudioContext关闭命令已发送');

            // 等待并验证关闭状态
            let attempts = 0;
            const maxAttempts = 10;
            while (microphoneAudioContext.state !== 'closed' && attempts < maxAttempts) {
              await new Promise(resolve => setTimeout(resolve, 100));
              attempts++;
              console.log(`🔇 等待AudioContext关闭... 尝试 ${attempts}/${maxAttempts}, 当前状态: ${microphoneAudioContext.state}`);
            }

            if (microphoneAudioContext.state === 'closed') {
              console.log('✅ AudioContext已成功关闭');
            } else {
              console.warn('⚠️ AudioContext未能在预期时间内关闭');
            }
          } else {
            console.log('✅ AudioContext已经是关闭状态');
          }
        } catch (e) {
          console.error('❌ 关闭AudioContext时出错:', e);
          return false;
        }
      }

      // 第五步：清除全局引用
      console.log('🔇 第五步：清除全局引用...');
      (window as any).microphoneRecorder = null; // RecordRTC录制器
      (window as any).microphoneAudioContext = null;
      (window as any).microphoneProcessor = null;
      (window as any).microphoneWorkletNode = null;
      (window as any).microphoneStream = null;
      (window as any).microphoneProcessorName = null;

      // 第六步：强制垃圾回收
      if ((window as any).gc) {
        try {
          (window as any).gc();
          console.log('🗑️ 强制垃圾回收已执行');
        } catch (e) {
          console.log('🗑️ 垃圾回收不可用');
        }
      }

      // 第七步：最终验证资源释放状态
      console.log('🔍 第七步：验证资源释放状态...');
      const finalVerification = await verifyMicrophoneResourcesReleased();

      if (finalVerification.success) {
        console.log('✅ 麦克风资源释放完成并验证成功');
        return true;
      } else {
        console.error('❌ 麦克风资源释放验证失败:', finalVerification.issues);
        return false;
      }

    } catch (error) {
      console.error('❌ 释放麦克风资源时发生错误:', error);
      return false;
    }
  };

  // 停止麦克风音频捕获
  const stopMicrophoneRecognition = async () => {
    console.log('🛑 开始停止麦克风音频捕获...');

    // 立即设置标志，阻止新的音频处理
    console.log('🔇 设置停止标志');
    (window as any).microphoneActive = false;
    setIsMicrophoneActive(false);

    // 使用改进的资源释放函数
    const resourcesReleased = await releaseMicrophoneResources();

    if (!resourcesReleased) {
      console.error('❌ 麦克风资源释放失败');
      setAsrServiceError('麦克风资源释放失败，可能仍在占用');
    }

    // 最后通知主进程停止（即使失败也不影响浏览器端的停止）
    if (electronAPI) {
      try {
        console.log('📞 通知主进程停止麦克风捕获...');
        const result = await electronAPI.stopMicrophoneCapturing();
        if (result.success) {
          console.log('✅ 主进程麦克风音频捕获停止成功');
        } else {
          console.error('❌ 主进程停止麦克风音频捕获失败:', result.error);
        }
      } catch (error) {
        console.error('❌ 通知主进程停止麦克风音频捕获时出错:', error);
      }
    } else {
      console.warn('⚠️ ElectronAPI 不可用，无法通知主进程');
    }

    console.log('✅ 麦克风音频捕获停止完成');
  };

  // 开始捕获麦克风音频 - 使用RecordRTC方式
  const startMicrophoneAudioCapture = async () => {
    console.log('🎵 开始捕获麦克风音频 (RecordRTC方式)...');

    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      console.error('❌ 浏览器不支持getUserMedia API');
      setAsrServiceError('浏览器不支持getUserMedia API');
      return;
    }

    // 清理任何残留的资源
    console.log('🧹 清理任何残留的音频资源...');
    await releaseMicrophoneResources();

    console.log('🔐 开始获取麦克风权限和音频流...');

    // 设置全局标志，表示麦克风处于活动状态
    (window as any).microphoneActive = true;

    try {
      // 使用与websocket-record-rtc.js一致的音频约束
      const audioConstraints = {
        audio: true // 简化约束，让RecordRTC处理音频配置
      };
      console.log('🔧 音频约束配置:', audioConstraints);

      // 获取媒体流
      const stream = await navigator.mediaDevices.getUserMedia(audioConstraints);

      console.log('✅ 麦克风权限获取成功，创建RecordRTC录制器...');
      console.log('🎵 音频流信息:', {
        id: stream.id,
        active: stream.active,
        tracks: stream.getTracks().map(track => ({
          kind: track.kind,
          label: track.label,
          enabled: track.enabled,
          readyState: track.readyState
        }))
      });

      // 创建RecordRTC录制器，配置与websocket-record-rtc.js一致
      const recorder = new RecordRTC(stream, {
        // 后端要求单通道，16K采样率，PCM
        type: 'audio',
        recorderType: StereoAudioRecorder,
        mimeType: 'audio/wav',
        numberOfAudioChannels: 1,
        desiredSampRate: 16000,
        disableLogs: true,
        timeSlice: 200,
        bufferSize: 4096,
        ondataavailable: async (recordResult: Blob) => {
          // 检查麦克风是否仍处于活动状态
          if (!(window as any).microphoneActive) {
            console.log('🔇 麦克风已停止，跳过音频数据处理');
            return;
          }

          try {
            console.log('🎵 RecordRTC收到音频数据，大小:', recordResult.size, '字节');

            // 转换Blob为ArrayBuffer
            const arrayBuffer = await recordResult.arrayBuffer();

            // 跳过WAV文件头（44字节），获取PCM数据
            const pcmData = arrayBuffer.slice(44);

            console.log('🎵 PCM数据长度:', pcmData.byteLength, '字节');

            // 使用现有的IPC通信发送到主进程
            if (electronAPI) {
              await electronAPI.invoke('microphone:process-audio', {
                audio_data: pcmData,
                sample_rate: 16000, // RecordRTC已转换为16000Hz
                audio_format: 'pcm'
              });
              console.log('✅ 音频数据发送成功');
            } else {
              console.error('❌ electronAPI 不可用，无法发送音频数据');
            }
          } catch (error) {
            console.error('❌ 处理RecordRTC音频数据时出错:', error);
          }
        }
      });

      // 开始录制
      recorder.startRecording();
      console.log('🎵 RecordRTC录制已开始');

      // 保存引用以便后续清理
      (window as any).microphoneRecorder = recorder;
      (window as any).microphoneStream = stream;

      // 设置麦克风为活动状态
      setIsMicrophoneActive(true);
      console.log('✅ RecordRTC麦克风音频捕获设置完成');

    } catch (error: any) {
      console.error('❌ 获取麦克风权限失败:', error);
      console.error('❌ 错误详情:', {
        name: error.name,
        message: error.message,
        constraint: error.constraint
      });

      // 通知用户麦克风权限问题
      if (error.name === 'NotAllowedError') {
        console.error('🚫 用户拒绝了麦克风权限');
        setAsrServiceError('用户拒绝了麦克风权限');
      } else if (error.name === 'NotFoundError') {
        console.error('🔍 没有找到麦克风设置');
        setAsrServiceError('没有找到麦克风设置');
      } else if (error.name === 'NotReadableError') {
        console.error('🔒 麦克风设备被其他应用占用');
        setAsrServiceError('麦克风设备被其他应用占用');
      } else {
        console.error('❓ 麦克风访问出现未知错误:', error.message);
        setAsrServiceError(`麦克风访问出现未知错误: ${error.message}`);
      }

      // 更新状态，停止录制
      console.log('🔇 更新状态，停止录制');
      setIsMicrophoneActive(false);
      (window as any).microphoneActive = false;
    }
  };

  // 添加流式响应事件监听
  useEffect(() => {
    if (!electronAPI || !electronAPI.onFastVoiceTextChunk || !electronAPI.onAccurateVoiceTextChunk) {
      console.warn('ElectronAPI 或流式响应监听器不可用');
      return;
    }

    // 处理极速模式流式响应
    const unsubscribeFast = electronAPI.onFastVoiceTextChunk((_, data) => {
      // 非常重要: 检查是否正在处理，避免接收到过期的响应
      if (!isProcessingAI.fast) return;
      
      console.log('收到极速模式流式响应块:', data);
      
      if (data.error) {
        // 处理错误情况
        setFastResponse(prev => prev + '\n\n[错误] ' + data.chunk);
        setIsProcessingAI(prev => ({ ...prev, fast: false }));
        return;
      }
      
      if (data.chunk) {
        // 流式更新响应文本
        console.log('极速模式接收到数据块:', data.chunk.substring(0, 100) + (data.chunk.length > 100 ? '...' : ''));
        setFastResponse(prev => prev + data.chunk);
      }
      
      if (data.done) {
        // 完成处理
        setIsProcessingAI(prev => ({ ...prev, fast: false }));
      }
    });
    
    // 处理精确模式流式响应
    const unsubscribeAccurate = electronAPI.onAccurateVoiceTextChunk((_, data) => {
      // 非常重要: 检查是否正在处理，避免接收到过期的响应
      if (!isProcessingAI.accurate) return;
      
      console.log('收到精确模式流式响应块:', data);
      
      if (data.error) {
        // 处理错误情况
        setAccurateResponse(prev => prev + '\n\n[错误] ' + data.chunk);
        setIsProcessingAI(prev => ({ ...prev, accurate: false }));
        return;
      }
      
      if (data.chunk) {
        // 流式更新响应文本
        console.log('精确模式接收到数据块:', data.chunk.substring(0, 100) + (data.chunk.length > 100 ? '...' : ''));
        setAccurateResponse(prev => prev + data.chunk);
      }
      
      if (data.done) {
        // 完成处理
        setIsProcessingAI(prev => ({ ...prev, accurate: false }));
      }
    });
    
    return () => {
      unsubscribeFast();
      unsubscribeAccurate();
    };
  }, [isProcessingAI]);

  /**
   * 收集并格式化所有音频源的文案
   * 按照角色区分：系统音频为提问方，麦克风音频为回复方
   * 格式：提问方：XXXXX；回复方：XXXX；
   * 系统音频最近150个字符，麦克风音频最近50个字符
   */
  const collectAndFormatTranscriptions = (): string => {
    console.log('🔄 开始收集并格式化所有音频源的文案...');

    // 创建一个包含所有文案的数组，每个元素包含文本、时间戳和来源
    const allTranscriptions: Array<{
      text: string;
      timestamp: Date;
      source: string;
      type: 'history' | 'realtime';
    }> = [];

    // 1. 添加历史记录中的文案
    transcriptionHistory.forEach(item => {
      if (item.text.trim()) {
        allTranscriptions.push({
          text: item.text.trim(),
          timestamp: item.timestamp,
          source: item.source,
          type: 'history'
        });
      }
    });

    // 2. 添加当前实时识别的系统音频文案
    if (systemTranscript.trim()) {
      allTranscriptions.push({
        text: systemTranscript.trim(),
        timestamp: new Date(), // 使用当前时间作为实时文案的时间戳
        source: 'system',
        type: 'realtime'
      });
    }

    // 3. 添加当前实时识别的麦克风文案
    if (microphoneTranscript.trim()) {
      allTranscriptions.push({
        text: microphoneTranscript.trim(),
        timestamp: new Date(), // 使用当前时间作为实时文案的时间戳
        source: 'microphone',
        type: 'realtime'
      });
    }

    console.log(`📝 收集到 ${allTranscriptions.length} 条文案:`, allTranscriptions.map(item => ({
      text: item.text.substring(0, 20) + (item.text.length > 20 ? '...' : ''),
      timestamp: item.timestamp.toLocaleTimeString(),
      source: item.source,
      type: item.type
    })));

    // 按时间戳排序（最早的在前面）
    allTranscriptions.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    // 按照角色分类并合并所有文案
    const systemTexts: string[] = []; // 系统音频文案（提问方）
    const microphoneTexts: string[] = []; // 麦克风文案（回复方）

    allTranscriptions.forEach(item => {
      // 确保文本不为空且去除多余空格
      const cleanText = item.text.trim();
      if (!cleanText) return;

      // 根据来源分类文案
      if (item.source === 'system') {
        systemTexts.push(cleanText);
      } else {
        microphoneTexts.push(cleanText);
      }
    });

    console.log(`📊 文案分类统计:`, {
      系统音频文案数: systemTexts.length,
      麦克风文案数: microphoneTexts.length,
      系统音频总字符: systemTexts.join('').length,
      麦克风总字符: microphoneTexts.join('').length
    });

    // 如果没有任何有效的转录内容，返回空字符串
    if (systemTexts.length === 0 && microphoneTexts.length === 0) {
      console.log('⚠️ 没有有效的转录内容');
      return '';
    }

    // 检查合并后的文本长度，避免过短的内容
    const totalLength = systemTexts.join('').length + microphoneTexts.join('').length;
    if (totalLength < 2) {
      console.log('⚠️ 转录内容过短，可能是单字符错误，跳过发送');
      return '';
    }

    // 固定字符分配：系统音频150字符，麦克风50字符
    const SYSTEM_AUDIO_LENGTH = 150; // 系统音频（提问方）字符数
    const MICROPHONE_LENGTH = 50;    // 麦克风（回复方）字符数

    // 合并各自的文案
    const systemFullText = systemTexts.join('');
    const microphoneFullText = microphoneTexts.join('');

    console.log(`📏 原始文案长度:`, {
      系统音频: systemFullText.length,
      麦克风: microphoneFullText.length,
      总计: systemFullText.length + microphoneFullText.length
    });

    // 按照固定长度提取最近的文案
    let finalSystemText = '';
    let finalMicrophoneText = '';

    // 提取系统音频最近150个字符
    if (systemFullText) {
      finalSystemText = systemFullText.length > SYSTEM_AUDIO_LENGTH
        ? systemFullText.slice(-SYSTEM_AUDIO_LENGTH)
        : systemFullText;
      console.log(`📝 系统音频文案: ${systemFullText.length} -> ${finalSystemText.length} 字符`);
    }

    // 提取麦克风最近50个字符
    if (microphoneFullText) {
      finalMicrophoneText = microphoneFullText.length > MICROPHONE_LENGTH
        ? microphoneFullText.slice(-MICROPHONE_LENGTH)
        : microphoneFullText;
      console.log(`📝 麦克风文案: ${microphoneFullText.length} -> ${finalMicrophoneText.length} 字符`);
    }



    // 构建最终的格式化文案
    const formattedParts: string[] = [];

    if (finalSystemText) {
      formattedParts.push(`提问方：${finalSystemText}`);
    }

    if (finalMicrophoneText) {
      formattedParts.push(`回复方：${finalMicrophoneText}`);
    }

    const recentText = formattedParts.join('；');

    console.log(`✅ 最终格式化文案 (${recentText.length} 字符):`, recentText);
    console.log(`📊 固定分配统计:`, {
      历史记录条数: transcriptionHistory.length,
      系统音频实时: systemTranscript ? '有' : '无',
      麦克风实时: microphoneTranscript ? '有' : '无',
      系统音频: `${systemFullText.length} -> ${finalSystemText.length} 字符 (目标150)`,
      麦克风: `${microphoneFullText.length} -> ${finalMicrophoneText.length} 字符 (目标50)`,
      最终总字符数: recentText.length,
      包含角色数: formattedParts.length
    });

    // 验证分配结果
    if (systemFullText && !finalSystemText) {
      console.error('❌ 严重错误：系统音频文案存在但未被包含在最终结果中！');
    } else if (systemFullText && finalSystemText) {
      console.log(`✅ 系统音频文案已成功包含 (${finalSystemText.length}/${SYSTEM_AUDIO_LENGTH} 字符)`);
    }

    if (microphoneFullText && finalMicrophoneText) {
      console.log(`✅ 麦克风文案已成功包含 (${finalMicrophoneText.length}/${MICROPHONE_LENGTH} 字符)`);
    }

    console.log(`📋 格式化示例:`, recentText.substring(0, 100) + (recentText.length > 100 ? '...' : ''));
    console.log(`🔍 角色分类详情:`, {
      系统音频: {
        文案数量: systemTexts.length,
        原始长度: systemFullText.length,
        最终长度: finalSystemText.length,
        内容预览: finalSystemText.substring(0, 50) + (finalSystemText.length > 50 ? '...' : '')
      },
      麦克风: {
        文案数量: microphoneTexts.length,
        原始长度: microphoneFullText.length,
        最终长度: finalMicrophoneText.length,
        内容预览: finalMicrophoneText.substring(0, 50) + (finalMicrophoneText.length > 50 ? '...' : '')
      }
    });

    return recentText;
  };

  // 测试函数：用于验证格式化功能（仅在开发环境中使用）
  const testFormatting = () => {
    console.log('🧪 开始测试角色合并格式化功能...');

    // 模拟测试数据：连续的同角色文案会被合并
    const testHistory = [
      { id: 1, text: "你好", source: 'system' as const, timestamp: new Date(Date.now() - 5000), isFinal: true },
      { id: 2, text: "我想问一个问题", source: 'system' as const, timestamp: new Date(Date.now() - 4000), isFinal: true },
      { id: 3, text: "你好，有什么可以帮助你的吗？", source: 'microphone' as const, timestamp: new Date(Date.now() - 3000), isFinal: true },
      { id: 4, text: "请详细说明你的问题", source: 'microphone' as const, timestamp: new Date(Date.now() - 2000), isFinal: true },
      { id: 5, text: "请帮我写一个计算两个数之和的函数", source: 'system' as const, timestamp: new Date(Date.now() - 1000), isFinal: true },
      { id: 6, text: "好的，我来帮你写", source: 'microphone' as const, timestamp: new Date(), isFinal: true }
    ];

    // 设置测试数据到全局，方便调试
    (window as any).testTranscriptionHistory = testHistory;
    (window as any).testSystemTranscript = "这是系统音频实时转录";
    (window as any).testMicrophoneTranscript = "这是麦克风音频实时转录";

    console.log('🧪 测试数据已设置');
    console.log('📋 预期结果格式（所有提问方合并，所有回复方合并）:');
    console.log('   提问方：你好我想问一个问题请帮我写一个计算两个数之和的函数这是系统音频实时转录；回复方：你好，有什么可以帮助你的吗？请详细说明你的问题好的，我来帮你写这是麦克风音频实时转录');
    console.log('🔧 可以在控制台中调用 window.collectAndFormatTranscriptions() 进行测试');
  };

  // 在开发环境中暴露测试函数到全局
  if (process.env.NODE_ENV === 'development') {
    (window as any).testVoiceFormatting = testFormatting;
    (window as any).collectAndFormatTranscriptions = collectAndFormatTranscriptions;
  }

  // 更新handleSendToAI函数
  const handleSendToAI = async (textToSend: string, mode: 'fast' | 'accurate') => {
    if (!electronAPI) {
      console.error('ElectronAPI 不可用，无法发送到AI');
      return;
    }

    // 验证输入文本
    const cleanTextToSend = textToSend.trim();
    if (!cleanTextToSend) {
      console.warn('⚠️ 没有可发送的文案内容');
      return;
    }

    // 检查文本长度，避免发送过短的内容
    if (cleanTextToSend.length < 2) {
      console.warn('⚠️ 文案内容过短，可能是单字符错误:', cleanTextToSend);
      return;
    }

    console.log(`🚀 准备发送到AI (${mode}模式):`, cleanTextToSend);
    console.log(`📊 发送文案统计: 字符数=${cleanTextToSend.length}`);

    setIsProcessingAI(prev => ({...prev, [mode]: true}));

    try {
      // 在开始处理前先清空对应的响应，以便显示流式响应
      if (mode === 'fast') {
        setFastResponse('');
      } else {
        setAccurateResponse('');
      }

      // 使用流式API
      const channel = mode === 'fast' ? 'process-fast-voice-text-stream' : 'process-accurate-voice-text-stream';

      // 开始流式请求 - 发送拼接后的文案
      const result = await electronAPI.invoke(channel, cleanTextToSend);

      if (!result.success) {
        console.error(`流式${mode === 'fast' ? '极速' : '精确'}模式请求失败:`, result.error);

        // 在UI中显示错误
        if (mode === 'fast') {
          setFastResponse('请求失败: ' + (result.error || '未知错误'));
        } else {
          setAccurateResponse('请求失败: ' + (result.error || '未知错误'));
        }

        // 更新处理状态
        setIsProcessingAI(prev => ({...prev, [mode]: false}));
      }
    } catch (error) {
      console.error(`发送到AI (${mode}模式) 失败:`, error);

      // 在UI中显示错误
      const errorMessage = error instanceof Error ? error.message : '未知错误';

      if (mode === 'fast') {
        setFastResponse('请求出错: ' + errorMessage);
      } else {
        setAccurateResponse('请求出错: ' + errorMessage);
      }

      // 更新处理状态
      setIsProcessingAI(prev => ({...prev, [mode]: false}));
    }
  };

  return (
    <div className="flex h-full overflow-hidden">
      {/* 左侧：语音识别面板 */}
      <div className="w-1/4 h-full flex flex-col border-r border-blue-500/20 overflow-hidden">
        {/* 状态栏 */}
        <div className="flex items-center justify-between bg-blue-900/10 px-3 py-1.5 border-b border-blue-500/10">
          {/* 左侧状态指示 */}
          <div className="flex items-center gap-1">
            <div className={`w-2 h-2 rounded-full ${isSystemActive || isMicrophoneActive ? 'bg-red-500 animate-pulse' : 'bg-gray-500'}`}></div>
            <span className="text-xs text-white/80 font-medium">
              {isSystemActive && '系统声音'}
              {isMicrophoneActive && isSystemActive && ' | '}
              {isMicrophoneActive && '麦克风'}
              {!isSystemActive && !isMicrophoneActive && '未录制'}
            </span>
          </div>

          {/* 右侧按钮组 - 重新排列：系统音频、麦克风、一键启动 */}
          <div className="flex items-center gap-2">
            {/* 系统音频按钮 */}
            <button
              onClick={() => {
                if (isSystemActive) {
                  console.log('🔇 用户点击停止系统音频');
                  stopSystemAudio();
                } else {
                  console.log('🔊 用户点击启动系统音频');
                  startSystemAudio();
                }
              }}
              className={`voice-system-audio-button p-1.5 rounded-full flex items-center justify-center ${
                isSystemActive
                  ? 'bg-red-600 text-white ring-1 ring-red-300 ring-opacity-50'
                  : 'bg-blue-700 text-gray-300 hover:bg-blue-600'
              } ${!asrServiceActive ? 'opacity-50 cursor-not-allowed' : ''}`}
              title={isSystemActive ? "停止系统声音录制" : "开始系统声音录制"}
              disabled={!asrServiceActive}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM14.657 2.929a1 1 0 011.414 0A9.972 9.972 0 0119 10a9.972 9.972 0 01-2.929 7.071a1 1 0 01-1.414-1.414A7.971 7.971 0 0017 10c0-2.21-.894-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 10a5.984 5.984 0 01-1.757 4.243a1 1 0 01-1.415-1.415A3.984 3.984 0 0013 10a3.983 3.983 0 00-1.172-2.828a1 1 0 010-1.415z" clipRule="evenodd" />
              </svg>
            </button>

            {/* 麦克风按钮 */}
            <button
              onClick={() => isMicrophoneActive ? stopMicrophoneRecognition() : startMicrophoneRecognition()}
              className={`voice-microphone-button p-1.5 rounded-full flex items-center justify-center ${
                isMicrophoneActive
                  ? 'bg-purple-600 text-white ring-1 ring-purple-300 ring-opacity-50'
                  : 'bg-blue-700 text-gray-300 hover:bg-blue-600'
              } ${!asrServiceActive ? 'opacity-50 cursor-not-allowed' : ''}`}
              title={isMicrophoneActive ? "停止麦克风录制" : "开始麦克风录制"}
              disabled={!asrServiceActive}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
              </svg>
            </button>

            {/* 一键启动/关闭按钮 - 放在最右边，稍大一些 */}
            <button
              onClick={toggleAllServices}
              disabled={asrServiceInitializing}
              className={`voice-one-click-start-button p-2 rounded-full flex items-center justify-center ${
                asrServiceActive
                  ? 'bg-red-600 text-white ring-1 ring-red-300 ring-opacity-50'
                  : asrServiceInitializing
                    ? 'bg-yellow-600 text-white animate-pulse'
                    : 'bg-green-600 text-white'
              }`}
              title={asrServiceActive
                ? "停止所有服务"
                : asrServiceInitializing
                  ? "正在初始化服务..."
                  : "一键启动所有服务"}
            >
              {asrServiceInitializing ? (
                <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : asrServiceActive ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                </svg>
              )}
            </button>
          </div>
        </div>

        {/* 聊天区域 - 完全重建为固定的三部分结构，增加透明度 */}
        <div className="flex-1 bg-gray-900/20 backdrop-blur-sm flex flex-col overflow-hidden">

          {/* 错误提示 */}
          {asrServiceError && (
            <div className="bg-red-900/20 border border-red-500/20 m-2 p-2 rounded-md">
              <p className="text-red-400 text-xs">错误: {asrServiceError}</p>
            </div>
          )}
          
          {/* 聊天历史记录区域 - 固定结构确保向上扩展 */}
          <div className="flex-1 relative">
            {/* 空状态 - 绝对定位在中心 */}
            {!systemTranscript && !microphoneTranscript && transcriptionHistory.length === 0 && (
              <div className="absolute inset-0 flex items-center justify-center text-white/50 text-center">
                <div className="px-2">
                  {!asrServiceActive ? (
                    <div className="flex flex-col items-center gap-4">
                      <p className="text-xs">点击右上角播放按钮启动语音识别服务</p>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                      </svg>
                    </div>
                  ) : (
                    <div>
                      <p className="mb-3 text-xs">点击按钮开始识别</p>
                      <div className="flex flex-col gap-2 text-xs">
                        <div className="flex items-center gap-2 p-2 rounded-lg bg-blue-900/10">
                          <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                          <span>系统声音</span>
                        </div>
                        <div className="flex items-center gap-2 p-2 rounded-lg bg-blue-900/10">
                          <div className="w-3 h-3 rounded-full bg-purple-500"></div>
                          <span>麦克风</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
            
            {/* 滚动容器 - 固定在底部，向上扩展 */}
            <div 
              ref={historyContainerRef}
              className="absolute inset-0 overflow-y-auto voice-chat-scrollbar flex flex-col-reverse"
              style={{
                scrollbarWidth: 'thin',
                scrollbarColor: 'rgba(59, 130, 246, 0.5) transparent'
              }}
            >
              {/* 消息容器 - 从底部开始，向上扩展 */}
              <div className="p-3 min-h-full flex flex-col justify-end">
                {/* 消息列表 - 正序显示，但容器反向，所以最新的在底部 */}
                <div className="space-y-2">
                  {/* 历史记录 - 正序显示，但因为父容器是flex-col-reverse，所以最新的在底部 */}
                  {transcriptionHistory.map(item => (
                    <div 
                      key={item.id} 
                      className={`flex ${item.source === 'system' ? 'justify-start' : 'justify-end'} mb-1`}
                    >
                      {/* 系统音频显示在左侧，带有头像 */}
                      {item.source === 'system' && (
                        <div className="flex items-start">
                          <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white mr-1 flex-shrink-0">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <div className="flex flex-col max-w-[80%]">
                            <div 
                              className={`rounded-lg p-2 text-xs ${
                                item.isError
                                  ? 'bg-red-500/5 border border-red-500/10 text-red-400'
                                  : 'bg-blue-900/10 border border-blue-500/10 text-white/90'
                              }`}
                            >
                              <p className="break-words">{item.text}</p>
                            </div>
                            <span className="text-[9px] text-white/40 mt-0.5 ml-1">
                              {formatTimeWithMs(item.timestamp)}
                            </span>
                          </div>
                        </div>
                      )}
                      
                      {/* 麦克风音频显示在右侧，带有头像 */}
                      {item.source === 'microphone' && (
                        <div className="flex items-start">
                          <div className="flex flex-col max-w-[80%] items-end">
                            <div 
                              className={`rounded-lg p-2 text-xs ${
                                item.isError
                                  ? 'bg-red-500/5 border border-red-500/10 text-red-400'
                                  : 'bg-purple-900/10 border border-purple-500/10 text-white/90'
                              }`}
                            >
                              <p className="break-words">{item.text}</p>
                            </div>
                            <span className="text-[9px] text-white/40 mt-0.5 mr-1">
                              {formatTimeWithMs(item.timestamp)}
                            </span>
                          </div>
                          <div className="w-6 h-6 rounded-full bg-purple-500 flex items-center justify-center text-white ml-1 flex-shrink-0">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4z" clipRule="evenodd" />
                              <path d="M11 14.93V17h2a1 1 0 110 2H7a1 1 0 110-2h2v-2.07A7 7 0 015 8a1 1 0 112 0 5 5 0 0010 0 1 1 0 112 0 7 7 0 01-6 6.93z" />
                            </svg>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                  
                  {/* 实时识别结果 - 系统音频 */}
                  {systemTranscript && (
                    <div className="flex justify-start mb-1">
                      <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white mr-1 flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="flex flex-col max-w-[80%]">
                        <div className="rounded-lg p-2 text-xs bg-blue-900/10 border border-blue-500/10 text-white/90 animate-pulse">
                          <p className="break-words">{systemTranscript}</p>
                        </div>
                        <span className="text-[9px] text-white/40 mt-0.5 ml-1">
                          识别中...
                        </span>
                      </div>
                    </div>
                  )}
                  
                  {/* 实时识别结果 - 麦克风 */}
                  {microphoneTranscript && (
                    <div className="flex justify-end mb-1">
                      <div className="flex flex-col max-w-[80%] items-end">
                        <div className="rounded-lg p-2 text-xs bg-purple-900/10 border border-purple-500/10 text-white/90 animate-pulse">
                          <p className="break-words">{microphoneTranscript}</p>
                        </div>
                        <span className="text-[9px] text-white/40 mt-0.5 mr-1">
                          识别中...
                        </span>
                      </div>
                      <div className="w-6 h-6 rounded-full bg-purple-500 flex items-center justify-center text-white ml-1 flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4z" clipRule="evenodd" />
                          <path d="M11 14.93V17h2a1 1 0 110 2H7a1 1 0 110-2h2v-2.07A7 7 7 0 015 8a1 1 0 112 0 5 5 0 0010 0 1 1 0 112 0 7 7 0 01-6 6.93z" />
                        </svg>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          
          {/* 底部操作区域 - 固定在底部 */}
          <div className="p-2 border-t border-blue-500/10 bg-blue-900/10">
            {/* 单一发送按钮，始终显示 */}
            <button
              onClick={() => {
                // 直接使用格式化后的完整文案，而不是只取最新的一条
                console.log('🚀 点击发送到AI按钮，开始收集完整文案...');
                const formattedText = collectAndFormatTranscriptions();

                if (formattedText.trim()) {
                  console.log('📤 发送完整格式化文案到AI:', formattedText);
                  handleSendToAI(formattedText.trim(), 'fast');
                  handleSendToAI(formattedText.trim(), 'accurate');
                } else {
                  console.warn('⚠️ 没有可发送的内容');
                }
              }}
              disabled={isProcessingAI.fast || isProcessingAI.accurate || 
                (!transcriptionHistory.length && !systemTranscript && !microphoneTranscript)}
              className={`voice-send-to-ai-button w-full px-3 py-2 rounded-md text-sm font-medium flex items-center justify-center gap-2 ${
                isProcessingAI.fast || isProcessingAI.accurate ||
                (!transcriptionHistory.length && !systemTranscript && !microphoneTranscript)
                  ? 'bg-gray-700/60 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-900/50 hover:bg-blue-700/80 active:bg-blue-800/90 text-white'
              }`}
            >
              {(isProcessingAI.fast || isProcessingAI.accurate) ? (
                <>
                  <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>处理中...</span>
                </>
              ) : (
                <>
                  <span>🚀🚀🚀🚀🚀🚀</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
      
      {/* 中间：极速模式AI响应 */}
      <div className="flex-1 h-full px-2 overflow-hidden" ref={fastResponseRef}>
        <div className="h-full">
          <AIResponse
            title="极速模式"
            response={fastResponse}
            isLoading={isProcessingAI.fast}
            type="fast"
          />
        </div>
      </div>

      {/* 右侧：精确模式AI响应 */}
      <div className="flex-1 h-full px-2 pr-1 overflow-hidden" ref={accurateResponseRef}>
        <div className="h-full">
          <AIResponse
            title="精确模式"
            response={accurateResponse}
            isLoading={isProcessingAI.accurate}
            type="accurate"
          />
        </div>
      </div>
    </div>
  );
});

VoiceRecognitionUI.displayName = 'VoiceRecognitionUI';

export default VoiceRecognitionUI;