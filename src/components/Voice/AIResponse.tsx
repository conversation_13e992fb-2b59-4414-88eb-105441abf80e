import React, { useEffect, useRef, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';

interface AIResponseProps {
  title?: string;
  response: string;
  isLoading: boolean;
  type?: 'fast' | 'accurate'; // 添加类型属性用于区分极速和精确模式
}

const AIResponse: React.FC<AIResponseProps> = ({ title = "AI 回复", response, isLoading, type }) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const [showUpArrow, setShowUpArrow] = useState<boolean>(false);
  const [showDownArrow, setShowDownArrow] = useState<boolean>(false);
  const upArrowRef = useRef<HTMLButtonElement>(null);
  const downArrowRef = useRef<HTMLButtonElement>(null);

  // 滚动到底部
  const scrollToBottom = () => {
    if (contentRef.current) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight;
    }
  };

  // 滚动到顶部
  const scrollToTop = () => {
    if (contentRef.current) {
      contentRef.current.scrollTop = 0;
    }
  };

  // 向上滚动
  const scrollUp = () => {
    if (contentRef.current) {
      const currentScrollTop = contentRef.current.scrollTop;
      const newScrollTop = Math.max(0, currentScrollTop - 350);

      contentRef.current.scrollTo({
        top: newScrollTop,
        behavior: 'smooth'
      });

      // 滚动后重新检查状态
      setTimeout(checkScrollStatus, 300);
    }
  };

  // 向下滚动
  const scrollDown = () => {
    if (contentRef.current) {
      const currentScrollTop = contentRef.current.scrollTop;
      const maxScrollTop = contentRef.current.scrollHeight - contentRef.current.clientHeight;
      const newScrollTop = Math.min(maxScrollTop, currentScrollTop + 350);

      contentRef.current.scrollTo({
        top: newScrollTop,
        behavior: 'smooth'
      });

      // 滚动后重新检查状态
      setTimeout(checkScrollStatus, 300);
    }
  };

  // 检查滚动状态
  const checkScrollStatus = () => {
    if (contentRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = contentRef.current;
      const isScrollable = scrollHeight > clientHeight;
      const isAtBottom = scrollHeight - scrollTop - clientHeight < 30;
      const isAtTop = scrollTop < 30;

      // 只显示需要的箭头按钮
      setShowUpArrow(isScrollable && !isAtTop);
      setShowDownArrow(isScrollable && !isAtBottom);
    }
  };

  // 监听滚动事件
  useEffect(() => {
    const element = contentRef.current;
    if (element) {
      element.addEventListener('scroll', checkScrollStatus);
      return () => element.removeEventListener('scroll', checkScrollStatus);
    }
  }, []);

  // 当内容变化时检查滚动状态
  useEffect(() => {
    setTimeout(checkScrollStatus, 100);
  }, [response]);

  // 当内容变化时自动滚动到底部（如果之前在底部）
  useEffect(() => {
    if (response && contentRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = contentRef.current;
      const wasAtBottom = scrollHeight - scrollTop - clientHeight < 30;
      if (wasAtBottom || scrollTop === 0) {
        setTimeout(scrollToBottom, 50);
      }

      // 内容变化后重新检查滚动状态和上报位置
      setTimeout(() => {
        checkScrollStatus();

        // 如果有类型，重新上报按钮位置
        if (type) {
          setTimeout(() => {
            const upElement = upArrowRef.current;
            const downElement = downArrowRef.current;

            if ((showUpArrow && upElement) || (showDownArrow && downElement)) {
              const positions: { [key: string]: { x: number; y: number; width: number; height: number; type: string } } = {};

              if (showUpArrow && upElement) {
                const upRect = upElement.getBoundingClientRect();
                positions[`${type}UpArrow`] = {
                  x: Math.round(upRect.left + window.screenX),
                  y: Math.round(upRect.top + window.screenY),
                  width: Math.round(upRect.width),
                  height: Math.round(upRect.height),
                  type: `ai-response-${type}-up-arrow`
                };
              }

              if (showDownArrow && downElement) {
                const downRect = downElement.getBoundingClientRect();
                positions[`${type}DownArrow`] = {
                  x: Math.round(downRect.left + window.screenX),
                  y: Math.round(downRect.top + window.screenY),
                  width: Math.round(downRect.width),
                  height: Math.round(downRect.height),
                  type: `ai-response-${type}-down-arrow`
                };
              }

              console.log(`📍 内容变化后上报${type}模式箭头按钮位置:`, positions);

              if ((window as any).electronAPI && Object.keys(positions).length > 0) {
                (window as any).electronAPI.invoke('voice-ai-arrow-positions', positions).catch((error: any) => {
                  console.error(`内容变化后上报${type}模式箭头按钮位置失败:`, error);
                });
              }
            }
          }, 100);
        }
      }, 200);
    }
  }, [response, type, showUpArrow, showDownArrow]);

  // 上报箭头按钮位置信息
  useEffect(() => {
    if (!type) return; // 只有指定了类型才上报位置

    const reportArrowPositions = () => {
      const upElement = upArrowRef.current;
      const downElement = downArrowRef.current;

      // 只有当箭头按钮存在且可见时才上报位置
      if ((showUpArrow && upElement) || (showDownArrow && downElement)) {
        const positions: { [key: string]: { x: number; y: number; width: number; height: number; type: string } } = {};

        // 只上报可见的按钮位置
        if (showUpArrow && upElement) {
          const upRect = upElement.getBoundingClientRect();
          positions[`${type}UpArrow`] = {
            x: Math.round(upRect.left + window.screenX),
            y: Math.round(upRect.top + window.screenY),
            width: Math.round(upRect.width),
            height: Math.round(upRect.height),
            type: `ai-response-${type}-up-arrow`
          };
        }

        if (showDownArrow && downElement) {
          const downRect = downElement.getBoundingClientRect();
          positions[`${type}DownArrow`] = {
            x: Math.round(downRect.left + window.screenX),
            y: Math.round(downRect.top + window.screenY),
            width: Math.round(downRect.width),
            height: Math.round(downRect.height),
            type: `ai-response-${type}-down-arrow`
          };
        }

        console.log(`📍 上报${type}模式箭头按钮位置:`, positions);

        // 发送位置信息到主进程
        if ((window as any).electronAPI && Object.keys(positions).length > 0) {
          (window as any).electronAPI.invoke('voice-ai-arrow-positions', positions).catch((error: any) => {
            console.error(`上报${type}模式箭头按钮位置失败:`, error);
          });
        }
      }
    };

    // 当箭头按钮显示状态改变时上报位置
    if (showUpArrow || showDownArrow) {
      const timer = setTimeout(reportArrowPositions, 150); // 增加延迟确保DOM完全更新
      return () => clearTimeout(timer);
    }
  }, [type, showUpArrow, showDownArrow]);

  // 监听窗口大小变化，重新上报位置
  useEffect(() => {
    if (!type) return;

    const handleResize = () => {
      // 延迟执行，确保DOM更新完成
      setTimeout(() => {
        const upElement = upArrowRef.current;
        const downElement = downArrowRef.current;

        // 只要有一个按钮存在且可见就上报位置
        if ((showUpArrow && upElement) || (showDownArrow && downElement)) {
          const positions: { [key: string]: { x: number; y: number; width: number; height: number; type: string } } = {};

          // 只上报可见的按钮位置
          if (showUpArrow && upElement) {
            const upRect = upElement.getBoundingClientRect();
            positions[`${type}UpArrow`] = {
              x: Math.round(upRect.left + window.screenX),
              y: Math.round(upRect.top + window.screenY),
              width: Math.round(upRect.width),
              height: Math.round(upRect.height),
              type: `ai-response-${type}-up-arrow`
            };
          }

          if (showDownArrow && downElement) {
            const downRect = downElement.getBoundingClientRect();
            positions[`${type}DownArrow`] = {
              x: Math.round(downRect.left + window.screenX),
              y: Math.round(downRect.top + window.screenY),
              width: Math.round(downRect.width),
              height: Math.round(downRect.height),
              type: `ai-response-${type}-down-arrow`
            };
          }

          console.log(`📍 窗口大小变化时上报${type}模式箭头按钮位置:`, positions);

          if ((window as any).electronAPI && Object.keys(positions).length > 0) {
            (window as any).electronAPI.invoke('voice-ai-arrow-positions', positions).catch((error: any) => {
              console.error(`窗口大小变化时上报${type}模式箭头按钮位置失败:`, error);
            });
          }
        }
      }, 200); // 增加延迟时间
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [type, showUpArrow, showDownArrow]); // 添加按钮状态依赖

  // 组件挂载后立即检查并上报位置
  useEffect(() => {
    if (!type) return;

    // 延迟执行，确保组件完全渲染
    const timer = setTimeout(() => {
      checkScrollStatus(); // 先检查滚动状态，确定按钮是否应该显示

      // 再延迟一点上报位置，确保按钮状态已更新
      setTimeout(() => {
        const upElement = upArrowRef.current;
        const downElement = downArrowRef.current;

        if ((showUpArrow && upElement) || (showDownArrow && downElement)) {
          const positions: { [key: string]: { x: number; y: number; width: number; height: number; type: string } } = {};

          if (showUpArrow && upElement) {
            const upRect = upElement.getBoundingClientRect();
            positions[`${type}UpArrow`] = {
              x: Math.round(upRect.left + window.screenX),
              y: Math.round(upRect.top + window.screenY),
              width: Math.round(upRect.width),
              height: Math.round(upRect.height),
              type: `ai-response-${type}-up-arrow`
            };
          }

          if (showDownArrow && downElement) {
            const downRect = downElement.getBoundingClientRect();
            positions[`${type}DownArrow`] = {
              x: Math.round(downRect.left + window.screenX),
              y: Math.round(downRect.top + window.screenY),
              width: Math.round(downRect.width),
              height: Math.round(downRect.height),
              type: `ai-response-${type}-down-arrow`
            };
          }

          console.log(`📍 组件挂载后上报${type}模式箭头按钮位置:`, positions);

          if ((window as any).electronAPI && Object.keys(positions).length > 0) {
            (window as any).electronAPI.invoke('voice-ai-arrow-positions', positions).catch((error: any) => {
              console.error(`组件挂载后上报${type}模式箭头按钮位置失败:`, error);
            });
          }
        }
      }, 100);
    }, 300);

    return () => clearTimeout(timer);
  }, []); // 只在组件挂载时执行一次



  return (
    <div
      className="ai-response-container"
      style={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: 'rgba(30, 58, 138, 0.02)',
        backdropFilter: 'blur(12px)',
        borderRadius: '8px',
        border: '1px solid rgba(59, 130, 246, 0.08)',
        color: 'white',
        fontSize: '14px',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* Header */}
      <div 
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '12px 16px',
          borderBottom: '1px solid rgba(59, 130, 246, 0.08)',
          backgroundColor: 'rgba(0, 0, 0, 0.05)',
          flexShrink: 0
        }}
      >
        <span style={{ fontSize: '12px', fontWeight: 500, color: 'rgba(147, 197, 253, 0.9)' }}>
          {title}
        </span>
        {isLoading && (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <div 
              style={{
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                backgroundColor: '#3b82f6',
                animation: 'pulse 2s infinite'
              }}
            />
            <span style={{ fontSize: '10px', color: 'rgba(255, 255, 255, 0.4)' }}>
              处理中...
            </span>
          </div>
        )}
      </div>

      {/* Content */}
      <div 
        ref={contentRef}
        className="ai-response-content"
        style={{
          flex: '1 1 0%',
          padding: '16px',
          overflowY: 'scroll',
          scrollBehavior: 'smooth',
          // 强制显示滚动条的样式
          scrollbarWidth: 'thin',
          scrollbarColor: 'rgba(59, 130, 246, 0.6) rgba(30, 41, 59, 0.4)',
          msOverflowStyle: 'auto'
        }}
      >
        {isLoading && !response ? (
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center', 
            height: '100%' 
          }}>
            <div style={{ textAlign: 'center' }}>
              <div 
                style={{
                  width: '32px',
                  height: '32px',
                  border: '2px solid #3b82f6',
                  borderTop: '2px solid transparent',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite',
                  margin: '0 auto 8px'
                }}
              />
              <p style={{ color: 'rgba(255, 255, 255, 0.5)' }}>正在生成回复...</p>
            </div>
          </div>
        ) : (
          <div className="markdown-content">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw]}
              components={{
                code: ({ className, children, ...props }: any) => {
                  const isInline = !className?.includes('language-');
                  return isInline ? (
                    <code 
                      style={{
                        backgroundColor: 'rgba(30, 41, 59, 0.3)',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        fontSize: '13px',
                        fontFamily: 'monospace'
                      }}
                      {...props}
                    >
                      {children}
                    </code>
                  ) : (
                    <pre 
                      style={{
                        backgroundColor: 'rgba(30, 41, 59, 0.4)',
                        padding: '12px',
                        borderRadius: '6px',
                        overflow: 'auto',
                        margin: '12px 0',
                        border: '1px solid rgba(59, 130, 246, 0.15)'
                      }}
                    >
                      <code 
                        style={{
                          fontFamily: 'monospace',
                          fontSize: '13px'
                        }}
                        {...props}
                      >
                        {children}
                      </code>
                    </pre>
                  );
                },
                a: ({ children, href, ...props }) => (
                  <a
                    href={href}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{
                      color: 'rgba(96, 165, 250, 0.9)',
                      textDecoration: 'none'
                    }}
                    {...props}
                  >
                    {children}
                  </a>
                ),
                blockquote: ({ children, ...props }) => (
                  <blockquote
                    style={{
                      borderLeft: '4px solid rgba(59, 130, 246, 0.3)',
                      paddingLeft: '16px',
                      margin: '16px 0',
                      color: 'rgba(255, 255, 255, 0.7)',
                      fontStyle: 'italic'
                    }}
                    {...props}
                  >
                    {children}
                  </blockquote>
                ),
                table: ({ children, ...props }) => (
                  <div style={{ overflowX: 'auto', margin: '16px 0' }}>
                    <table 
                      style={{
                        width: '100%',
                        borderCollapse: 'collapse',
                        border: '1px solid rgba(59, 130, 246, 0.15)'
                      }}
                      {...props}
                    >
                      {children}
                    </table>
                  </div>
                ),
                th: ({ children, ...props }) => (
                  <th
                    style={{
                      border: '1px solid rgba(59, 130, 246, 0.15)',
                      backgroundColor: 'rgba(30, 58, 138, 0.15)',
                      padding: '8px 12px',
                      textAlign: 'left',
                      fontWeight: 600
                    }}
                    {...props}
                  >
                    {children}
                  </th>
                ),
                td: ({ children, ...props }) => (
                  <td
                    style={{
                      border: '1px solid rgba(59, 130, 246, 0.15)',
                      padding: '8px 12px'
                    }}
                    {...props}
                  >
                    {children}
                  </td>
                )
              }}
            >
              {response}
            </ReactMarkdown>
            {isLoading && (
              <div 
                style={{
                  marginTop: '4px',
                  marginLeft: '4px',
                  display: 'inline-block',
                  width: '8px',
                  height: '16px',
                  backgroundColor: '#60a5fa',
                  opacity: 0.75,
                  animation: 'pulse 2s infinite'
                }}
              />
            )}
          </div>
        )}
      </div>

      {/* 右下角滚动按钮组 */}
      {(showUpArrow || showDownArrow) && (
        <div
          style={{
            position: 'absolute',
            bottom: '16px',
            right: '16px',
            display: 'flex',
            flexDirection: 'column',
            gap: '8px',
            zIndex: 10
          }}
        >
          {/* 上箭头按钮 */}
          {showUpArrow && (
            <button
              ref={upArrowRef}
              onClick={scrollUp}
              style={{
                backgroundColor: 'rgba(59, 130, 246, 0.8)',
                backdropFilter: 'blur(8px)',
                color: 'white',
                padding: '10px',
                borderRadius: '50%',
                border: 'none',
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.4)',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '40px',
                height: '40px'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(37, 99, 235, 0.9)';
                e.currentTarget.style.transform = 'scale(1.1)';
                e.currentTarget.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.5)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(59, 130, 246, 0.8)';
                e.currentTarget.style.transform = 'scale(1)';
                e.currentTarget.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.4)';
              }}
              title="向上滚动"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5">
                <path d="M18 15l-6-6-6 6" />
              </svg>
            </button>
          )}

          {/* 下箭头按钮 */}
          {showDownArrow && (
            <button
              ref={downArrowRef}
              onClick={scrollDown}
              style={{
                backgroundColor: 'rgba(59, 130, 246, 0.8)',
                backdropFilter: 'blur(8px)',
                color: 'white',
                padding: '10px',
                borderRadius: '50%',
                border: 'none',
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.4)',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '40px',
                height: '40px'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(37, 99, 235, 0.9)';
                e.currentTarget.style.transform = 'scale(1.1)';
                e.currentTarget.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.5)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(59, 130, 246, 0.8)';
                e.currentTarget.style.transform = 'scale(1)';
                e.currentTarget.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.4)';
              }}
              title="向下滚动"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5">
                <path d="M6 9l6 6 6-6" />
              </svg>
            </button>
          )}
        </div>
      )}



      {/* CSS Animations and Scrollbar Styles */}
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
          
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
          }
          
          /* 强制显示滚动条 - 覆盖全局隐藏样式 */
          .ai-response-content {
            -ms-overflow-style: auto !important;
            scrollbar-width: thin !important;
            scrollbar-color: rgba(59, 130, 246, 0.6) rgba(30, 41, 59, 0.4) !important;
          }
          
          .ai-response-content::-webkit-scrollbar {
            width: 12px !important;
            display: block !important;
          }
          
          .ai-response-content::-webkit-scrollbar-track {
            background: rgba(30, 41, 59, 0.4) !important;
            border-radius: 6px !important;
          }
          
          .ai-response-content::-webkit-scrollbar-thumb {
            background: rgba(59, 130, 246, 0.6) !important;
            border-radius: 6px !important;
            border: 2px solid rgba(30, 41, 59, 0.6) !important;
          }
          
          .ai-response-content::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.8) !important;
          }
          
          /* Markdown 内容样式 */
          .markdown-content {
            line-height: 1.6;
            word-wrap: break-word;
          }
          
          .markdown-content h1, .markdown-content h2, .markdown-content h3,
          .markdown-content h4, .markdown-content h5, .markdown-content h6 {
            margin-top: 1.5em;
            margin-bottom: 0.75em;
            color: rgba(255, 255, 255, 0.95);
            font-weight: 600;
          }
          
          .markdown-content p {
            margin-bottom: 1em;
            color: rgba(255, 255, 255, 0.9);
          }
          
          .markdown-content ul, .markdown-content ol {
            padding-left: 1.5em;
            margin-bottom: 1em;
          }
          
          .markdown-content li {
            margin-bottom: 0.5em;
          }
          
          .markdown-content strong {
            color: rgba(255, 255, 255, 0.95);
            font-weight: 600;
          }
          
          .markdown-content em {
            color: rgba(255, 255, 255, 0.85);
            font-style: italic;
          }
          
          .markdown-content hr {
            border: none;
            border-top: 1px solid rgba(59, 130, 246, 0.3);
            margin: 2em 0;
          }
        `}
      </style>
    </div>
  );
};

export default AIResponse;
