# ASR 优化测试文档

## 优化内容

根据用户需求，已对麦克风音频和系统音频的历史文案和正在转录的文案进行优化：

### 主要变更

1. **SystemASRManager.ts**
   - 移除了 `utterances` 相关的处理逻辑
   - 只使用 `result.text` 处理转录结果
   - 简化了代码结构，去除了复杂的历史转录和当前转录区分逻辑

2. **MicrophoneASRManager.ts**
   - 移除了 `utterances` 相关的处理逻辑
   - 只使用 `result.text` 处理转录结果
   - 简化了代码结构

3. **VoiceRecognitionUI.tsx**
   - 移除了 `utterance` 历史数据的特殊处理逻辑
   - 简化了 ASR 转写结果的处理流程
   - 统一了麦克风和系统音频的处理方式

### 新的处理逻辑

**不再依赖 `isFinal` 变量**，而是通过比较文案前20个字符来判断：

根据用户提供的示例数据：

```json
{
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: '你好',
}

{
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: '你好，我叫tom',
}

{
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: '你好，我叫tom，请问你是？',
}

{
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: '能听见吗？',
}

{
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: '能听见吗？给个回复',
}
```

**处理逻辑**：
1. **前三条数据**：前20个字符都是"你好"，判断为同一段正在转录的内容
   - 实时显示："你好" → "你好，我叫tom" → "你好，我叫tom，请问你是？"

2. **第四条数据**：前20个字符变为"能听见吗？"，与之前不同
   - 将"你好，我叫tom，请问你是？"加入历史记录
   - 开始新的转录："能听见吗？"

3. **第五条数据**：前20个字符仍是"能听见吗？"，继续当前转录
   - 实时显示："能听见吗？给个回复"

**最终结果**：
- **历史文案**："你好，我叫tom，请问你是？"
- **当前转录文案**："能听见吗？给个回复"

### 测试要点

1. **数据流简化**：确认只处理 `result.text`，不再处理 `utterances`
2. **前20字符比较逻辑**：验证通过比较前20个字符正确判断是否为连续转录
3. **历史文案正确性**：验证当前20个字符发生变化时，之前的内容正确加入历史记录
4. **实时转录**：验证前20个字符相同时，内容实时更新显示
5. **代码简洁性**：确认移除了依赖 `isFinal` 的复杂逻辑

## 配置更新

- `show_utterances: false` - 明确标注不需要 utterances 数据
- 保持现有的其他配置不变

## 兼容性

- 保留了对列表格式 result 的兼容性处理
- 保持了现有的错误处理机制
