# ASR 优化测试文档

## 优化内容

根据用户需求，已对麦克风音频和系统音频的历史文案和正在转录的文案进行优化：

### 主要变更

1. **SystemASRManager.ts**
   - 移除了 `utterances` 相关的处理逻辑
   - 只使用 `result.text` 处理转录结果
   - 简化了代码结构，去除了复杂的历史转录和当前转录区分逻辑

2. **MicrophoneASRManager.ts**
   - 移除了 `utterances` 相关的处理逻辑
   - 只使用 `result.text` 处理转录结果
   - 简化了代码结构

3. **VoiceRecognitionUI.tsx**
   - 移除了 `utterance` 历史数据的特殊处理逻辑
   - 简化了 ASR 转写结果的处理流程
   - 统一了麦克风和系统音频的处理方式

### 预期行为

根据用户提供的示例数据：

```json
{
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: '你好',
}

{
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: '你好，我叫tom',
}

{
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: '你好，我叫tom，请问你是？',
}

{
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: '能听见吗？',
}

{
    additions: { log_id: '202507301108479A1993F0EA20ABA39668' },
    text: '能听见吗？给个回复',
}
```

- **历史文案**：当 `isFinal: true` 时，"你好，我叫tom，请问你是？" 会被添加到历史记录
- **当前转录文案**：当 `isFinal: false` 时，"能听见吗？给个回复" 会显示为正在转录的文案

### 测试要点

1. **数据流简化**：确认只处理 `result.text`，不再处理 `utterances`
2. **历史文案正确性**：验证最终确定的文案能正确添加到历史记录
3. **实时转录**：验证正在转录的文案能实时更新显示
4. **代码简洁性**：确认移除了无用的复杂逻辑

## 配置更新

- `show_utterances: false` - 明确标注不需要 utterances 数据
- 保持现有的其他配置不变

## 兼容性

- 保留了对列表格式 result 的兼容性处理
- 保持了现有的错误处理机制
