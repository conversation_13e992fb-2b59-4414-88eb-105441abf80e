{"$schema": "https://www.raycast.com/schemas/extension.json", "name": "speech-to-text", "title": "Speech to Text", "description": "A simple extension that allows Speech to Text using Doubao (豆包) API.", "icon": "extension-icon.png", "author": "facundo_prieto", "categories": ["Productivity", "Media", "Developer Tools", "Communication"], "license": "MIT", "commands": [{"name": "record-transcription", "title": "Record Transcription", "subtitle": "Speech to Text", "description": "Record an audio and transcribe it to text", "mode": "view"}, {"name": "transcription-history", "title": "Transcription History", "subtitle": "Speech to Text", "description": "View and manage your transcription history", "mode": "view"}, {"name": "view-logs", "title": "View Plugin Logs", "subtitle": "Speech to Text", "description": "View debug logs for troubleshooting", "mode": "view", "keywords": ["logs", "debug", "troubleshoot"]}], "preferences": [{"name": "doubaoAppKey", "title": "Doubao App Key", "description": "Your Doubao App Key (required for Doubao)", "type": "textfield", "required": false, "placeholder": "Enter your Doubao App Key"}, {"name": "doubaoAccessToken", "title": "Doubao Access Token", "description": "Your Doubao Access Token (required for Doubao)", "type": "password", "required": false, "placeholder": "Enter your Doubao Access Token"}, {"name": "doubaoSecretKey", "title": "Doubao Secret Key", "description": "Your Doubao Secret Key (required for <PERSON><PERSON><PERSON>)", "type": "password", "required": false, "placeholder": "Enter your Doubao Secret Key"}, {"name": "language", "title": "Default Language", "description": "Selecting a specific language can improve transcription accuracy", "type": "dropdown", "required": true, "default": "auto", "data": [{"title": "Auto-detect (default)", "value": "auto"}, {"title": "English", "value": "en"}, {"title": "Spanish", "value": "es"}, {"title": "French", "value": "fr"}, {"title": "German", "value": "de"}, {"title": "Italian", "value": "it"}, {"title": "Portuguese", "value": "pt"}, {"title": "Chinese", "value": "zh"}, {"title": "Japanese", "value": "ja"}, {"title": "Korean", "value": "ko"}, {"title": "Russian", "value": "ru"}]}, {"name": "promptText", "title": "De<PERSON>ult Prompt", "description": "Default prompt text to guide the AI transcription", "type": "textfield", "required": false, "placeholder": "Enter instructions for the AI transcription", "default": "Maintain proper sentence structure, punctuation, and paragraphs. Format numbers, currency, and units appropriately. Preserve speaker transitions if multiple speakers are detected."}, {"name": "userTerms", "title": "Custom Terms", "description": "Comma-separated list of specialized terms, names, or jargon to help with transcription accuracy", "type": "textfield", "required": false, "placeholder": "e.g., React.js, TypeScript, GraphQL, <names>, etc."}, {"name": "enableContext", "title": "Use Highlighted Text", "label": "Use highlighted text as context", "description": "Automatically use any text you have highlighted in other apps as context for transcription", "type": "checkbox", "default": true, "required": false}, {"name": "enableLogging", "title": "Enable Debug Logging", "label": "Enable debug logging for troubleshooting", "description": "Enable detailed logging for development and debugging. Recommended for developers, disable for end users.", "type": "checkbox", "default": false, "required": false}, {"name": "logLevel", "title": "Log Level", "description": "Set the minimum log level to capture. Lower levels include all higher levels.", "type": "dropdown", "required": false, "default": "INFO", "data": [{"title": "TRACE - Most detailed (development only)", "value": "TRACE"}, {"title": "DEBUG - Debug information", "value": "DEBUG"}, {"title": "INFO - General information (recommended)", "value": "INFO"}, {"title": "WARN - Warnings only", "value": "WARN"}, {"title": "ERROR - Errors only", "value": "ERROR"}]}, {"name": "logToFile", "title": "Log to File", "label": "Save logs to debug file", "description": "Save debug logs to speech-to-text-debug.log file for troubleshooting", "type": "checkbox", "default": true, "required": false}, {"name": "logToConsole", "title": "Log to Console", "label": "Display logs in console", "description": "Display debug logs in console (for development use)", "type": "checkbox", "default": false, "required": false}, {"name": "deepseekApiKey", "title": "DeepSeek API Key", "description": "Your DeepSeek API key for text polishing features", "type": "password", "required": false, "placeholder": "Enter your DeepSeek API key"}, {"name": "deepseekModel", "title": "DeepSeek Model", "description": "DeepSeek model to use for text processing", "type": "dropdown", "required": false, "default": "deepseek-chat", "data": [{"title": "DeepSeek Chat (Recommended)", "value": "deepseek-chat"}, {"title": "DeepSeek Coder", "value": "deepseek-coder"}]}, {"name": "deepseekBaseUrl", "title": "DeepSeek Base URL", "description": "DeepSeek API base URL (use default unless you have a custom endpoint)", "type": "textfield", "required": false, "default": "https://api.deepseek.com/v1", "placeholder": "https://api.deepseek.com/v1"}, {"name": "polishPrompt", "title": "Default Polish Prompt", "description": "Custom prompt template for text polishing", "type": "textfield", "required": false, "default": "请润色以下文本，使其更加通顺自然，保持原意不变", "placeholder": "Enter custom instructions for text polishing"}, {"name": "enablePolishing", "title": "Enable Text Polishing", "label": "Enable DeepSeek text polishing feature", "description": "Enable the DeepSeek-powered text polishing functionality", "type": "checkbox", "default": true, "required": false}, {"name": "polishingTask", "title": "Default Polishing Task", "description": "Default task type for text polishing", "type": "dropdown", "required": false, "default": "润色", "data": [{"title": "润色 - Polish text for better readability", "value": "润色"}, {"title": "改写 - Rewrite with different expressions", "value": "改写"}, {"title": "纠错 - Fix grammar and spelling errors", "value": "纠错"}, {"title": "翻译 - Translate to another language", "value": "翻译"}, {"title": "扩写 - Expand with more details", "value": "扩写"}, {"title": "缩写 - Condense to key points", "value": "缩写"}, {"title": "学术润色 - Academic style polishing", "value": "学术润色"}]}, {"name": "saveAudioFiles", "title": "保存音频文件", "label": "录音完成后保留音频文件", "description": "是否在转录完成后保留原始音频文件。关闭此选项可节省存储空间", "type": "checkbox", "default": false, "required": false}, {"name": "audioSaveLocation", "title": "音频保存位置", "description": "自定义音频文件保存位置。留空则使用默认位置", "type": "textfield", "required": false, "placeholder": "输入自定义路径或留空使用默认位置"}], "dependencies": {"@raycast/api": "^1.93.0", "@raycast/utils": "^1.19.0", "@types/fs-extra": "^11.0.4", "fs-extra": "^11.3.0", "ws": "^8.16.0"}, "devDependencies": {"@raycast/eslint-config": "^1.0.11", "@types/node": "20.8.10", "@types/react": "18.3.3", "@types/ws": "^8.5.10", "eslint": "^8.57.1", "prettier": "^3.5.3", "typescript": "^5.4.5"}, "scripts": {"build": "ray build", "dev": "rm -f /tmp/speech-to-text-debug.flag && ray develop", "dev:debug": "echo 'debug' > /tmp/speech-to-text-debug.flag && ray develop", "fix-lint": "ray lint --fix", "lint": "ray lint", "prepublishOnly": "echo \"\\n\\nIt seems like you are trying to publish the Raycast extension to npm.\\n\\nIf you did intend to publish it to npm, remove the \\`prepublishOnly\\` script and rerun \\`npm publish\\` again.\\nIf you wanted to publish it to the Raycast Store instead, use \\`npm run publish\\` instead.\\n\\n\" && exit 1", "publish": "npx @raycast/api@latest publish"}}