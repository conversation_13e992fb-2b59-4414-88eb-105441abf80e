
> secure-kernel@1.1.0 dev
> cross-env NODE_ENV=development vite


  VITE v5.4.19  ready in 3604 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
  ➜  press h + enter to show help
vite v5.4.19 building for development...

watching for file changes...
vite v5.4.19 building for development...

watching for file changes...

build started...

build started...
transforming...
✓ 1 modules transformed.
transforming...
rendering chunks...
computing gzip size...
dist-electron/preload.js  14.11 kB │ gzip: 2.34 kB
built in 96ms.
✓ 774 modules transformed.
rendering chunks...
computing gzip size...
dist-electron/main.js  1,226.87 kB │ gzip: 253.53 kB
built in 1718ms.
从配置中读取模型列表: [
  'doubao-pro',
  'doubao-thinking',
  'deepseek-v3',
  'deepseek-r1',
  'gemini-2.5',
  'claude',
  'grok-4'
]
UnifiedHistoryManager: 实例已创建
WebSocket module loaded successfully for MicrophoneASR
MicrophoneASRManager instance created
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
MicrophoneASRManager: 语音配置加载成功 {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
MicrophoneManager: 初始化麦克风管理器
WebSocket module loaded successfully for SystemASR
SystemASRManager instance created
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
SystemASRManager: 语音配置加载成功 {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
Setting up app.whenReady() handler...
Main.ts loaded, app state: { isReady: false, version: '29.4.6' }
App 'ready' event fired
Electron app is ready, starting initialization...
initializeApp: Starting app initialization...
initializeApp: Creating app state...
所有快捷键已注销
快捷键 Alt+C 注册成功，已阻止事件传播
快捷键 Alt+V 注册成功，已阻止事件传播
快捷键 Alt+A 注册成功，已阻止事件传播
快捷键 CommandOrControl+Left 注册成功，已阻止事件传播
快捷键 CommandOrControl+Right 注册成功，已阻止事件传播
快捷键 CommandOrControl+Down 注册成功，已阻止事件传播
快捷键 CommandOrControl+Up 注册成功，已阻止事件传播
快捷键 CommandOrControl+B 注册成功，已阻止事件传播
快捷键 Alt+Z 注册成功，已阻止事件传播
快捷键 Alt+2 注册成功，已阻止事件传播
快捷键 Alt+3 注册成功，已阻止事件传播
快捷键 Alt+1 注册成功，已阻止事件传播
快捷键 CommandOrControl+Q 注册成功，已阻止事件传播
所有快捷键注册成功
键盘钩子已激活
设置 macOS 键盘钩子
注册系统级键盘监听器
添加键盘钩子: Alt+C
添加键盘钩子: Alt+Z
添加键盘钩子: Alt+1
添加键盘钩子: Alt+2
添加键盘钩子: Alt+3
添加键盘钩子: Alt+V
添加键盘钩子: Alt+A
添加键盘钩子: CommandOrControl+R
添加键盘钩子: CommandOrControl+Left
添加键盘钩子: CommandOrControl+Right
添加键盘钩子: CommandOrControl+Down
添加键盘钩子: CommandOrControl+Up
添加键盘钩子: CommandOrControl+B
添加键盘钩子: CommandOrControl+Q
系统级键盘拦截已设置
ShortcutsHelper initialized
鼠标点击事件监听设置完成
ioHook started
initializeApp: Creating main window...
初始化SystemAudioManager...
初始化MicrophoneManager...
初始化UnifiedHistoryManager...
注册语音处理IPC处理器...
注册语音助手 IPC 处理程序...
initializeApp: Initializing IPC handlers...
initializeApp: 注册全局快捷键...
重新注册全局快捷键
快捷键 Alt+C 已注销
快捷键 Alt+V 已注销
快捷键 Alt+A 已注销
快捷键 CommandOrControl+Left 已注销
快捷键 CommandOrControl+Right 已注销
快捷键 CommandOrControl+Down 已注销
快捷键 CommandOrControl+Up 已注销
快捷键 CommandOrControl+B 已注销
快捷键 Alt+Z 已注销
快捷键 Alt+2 已注销
快捷键 Alt+3 已注销
快捷键 Alt+1 已注销
快捷键 CommandOrControl+Q 已注销
所有快捷键已注销
快捷键 Alt+C 注册成功，已阻止事件传播
快捷键 Alt+V 注册成功，已阻止事件传播
快捷键 Alt+A 注册成功，已阻止事件传播
快捷键 CommandOrControl+Left 注册成功，已阻止事件传播
快捷键 CommandOrControl+Right 注册成功，已阻止事件传播
快捷键 CommandOrControl+Down 注册成功，已阻止事件传播
快捷键 CommandOrControl+Up 注册成功，已阻止事件传播
快捷键 CommandOrControl+B 注册成功，已阻止事件传播
快捷键 Alt+Z 注册成功，已阻止事件传播
快捷键 Alt+2 注册成功，已阻止事件传播
快捷键 Alt+3 注册成功，已阻止事件传播
快捷键 Alt+1 注册成功，已阻止事件传播
快捷键 CommandOrControl+Q 注册成功，已阻止事件传播
所有快捷键注册成功
initializeApp: 等待窗口加载完成后检查配置...
initializeApp: 窗口正在加载，等待加载完成...
UnifiedHistoryManager: 🧹 设置新主窗口，清空历史记录
UnifiedHistoryManager: 🚀 发送统一历史更新，共 0 条记录 {}
UnifiedHistoryManager: 历史记录已清空
SystemAudioManager: 主窗口已设置
注册系统音频IPC处理器...
UnifiedHistoryManager: 🧹 设置新主窗口，清空历史记录
UnifiedHistoryManager: 🚀 发送统一历史更新，共 0 条记录 {}
UnifiedHistoryManager: 历史记录已清空
SystemAudioManager: 主窗口已设置
移除已存在的system-audio:start-capturing处理器
移除已存在的system-audio:stop-capturing处理器
移除已存在的system-audio:get-status处理器
系统音频相关的IPC处理器已注册
UnifiedHistoryManager: 🧹 设置新主窗口，清空历史记录
UnifiedHistoryManager: 🚀 发送统一历史更新，共 0 条记录 {}
UnifiedHistoryManager: 历史记录已清空
MicrophoneManager: 主窗口已设置
注册麦克风IPC处理器...
UnifiedHistoryManager: 🧹 设置新主窗口，清空历史记录
UnifiedHistoryManager: 🚀 发送统一历史更新，共 0 条记录 {}
UnifiedHistoryManager: 历史记录已清空
MicrophoneManager: 主窗口已设置
移除已存在的microphone:start-capturing处理器
移除已存在的microphone:stop-capturing处理器
移除已存在的microphone:get-status处理器
移除已存在的microphone:process-audio处理器
麦克风相关的IPC处理器已注册
UnifiedHistoryManager: 🧹 设置新主窗口，清空历史记录
UnifiedHistoryManager: 🚀 发送统一历史更新，共 0 条记录 {}
UnifiedHistoryManager: 历史记录已清空
UnifiedHistoryManager 已初始化并设置主窗口
initializeApp: 窗口加载完成，开始配置检查
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3',
      'deepseek-r1',
      'gemini-2.5',
      'claude',
      'grok-4'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3',
      'deepseek-r1',
      'gemini-2.5',
      'claude',
      'grok-4'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
工具条位置已更新: { x: 16, y: 12, width: 454, height: 49 }
📍 工具条区域已更新: {
  regionsCount: 4,
  regions: [
    { type: 'model', x: 261, y: 24, width: 80, height: 10 },
    { type: 'reset', x: 120, y: 19, width: 83, height: 19 },
    { type: 'capture', x: 21, y: 21, width: 66, height: 16 },
    { type: 'solution', x: 365, y: 23, width: 58, height: 11 }
  ]
}
工具条位置更新被节流
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'model', x: 261, y: 24, width: 57, height: 10 },
    { type: 'reset', x: 120, y: 19, width: 83, height: 19 },
    { type: 'capture', x: 21, y: 21, width: 66, height: 16 },
    { type: 'solution', x: 343, y: 23, width: 58, height: 11 },
    { type: 'voice', x: 427, y: 21, width: 46, height: 16 }
  ]
}
工具条位置已更新: { x: 16, y: 12, width: 483, height: 49 }
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'model', x: 261, y: 24, width: 57, height: 10 },
    { type: 'reset', x: 120, y: 19, width: 83, height: 19 },
    { type: 'capture', x: 21, y: 21, width: 66, height: 16 },
    { type: 'solution', x: 343, y: 23, width: 58, height: 11 },
    { type: 'voice', x: 427, y: 21, width: 46, height: 16 }
  ]
}
initializeApp: 开始检查并初始化配置...
checkAndInitializeConfig called
检测到已存储的 API Key，开始初始化配置...
开始初始化配置...
configResult: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3',
      'deepseek-r1',
      'gemini-2.5',
      'claude',
      'grok-4'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
配置初始化成功
通知渲染进程更新麦克风按钮状态...
✅ 配置更新事件已发送到渲染进程
initializeApp: 开始请求系统权限...
initializeApp: 权限请求完成
initializeApp: 应用初始化完成
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3',
      'deepseek-r1',
      'gemini-2.5',
      'claude',
      'grok-4'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
🖱️ 检测到鼠标点击事件: { x: 461, y: 41, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 461, y: 41 },
  windowBounds: { x: 0, y: 0, width: 531, height: 73 },
  toolbarBounds: { x: 16, y: 12, width: 483, height: 49 },
  regionsCount: 5
}
使用渲染进程报告的工具条位置: { x: 16, y: 12, width: 483, height: 49 }
鼠标位置 (461, 41) 在工具条范围内
点击在 voice 区域内
Voice区域被点击，显示语音识别界面
🖱️ 鼠标滚轮监听已启动
工具条位置已更新: { x: 5, y: 1, width: 649, height: 749 }
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 589, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 77, y: 65, width: 28, height: 28 },
    { type: 'voice-system-audio', x: 41, y: 65, width: 28, height: 28 },
    {
      type: 'voice-one-click-start',
      x: 113,
      y: 61,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 145, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 589, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 77, y: 65, width: 28, height: 28, type: 'voice-microphone' },
  { x: 41, y: 65, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 113,
    y: 61,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 145, height: 36, type: 'voice-send-to-ai' }
]
📍 更新AI回复框位置信息: {
  fastResponse: { x: 167, y: 50, width: 245, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 413,
    y: 50,
    width: 241,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 167, y: 50, width: 245, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 413,
    y: 50,
    width: 241,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 175, y: 50, width: 257, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 433,
    y: 50,
    width: 253,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 199, y: 50, width: 293, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 493,
    y: 50,
    width: 289,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 199, y: 50, width: 293, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 493,
    y: 50,
    width: 289,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 207, y: 50, width: 305, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 513,
    y: 50,
    width: 301,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 215, y: 50, width: 317, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 533,
    y: 50,
    width: 313,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 223, y: 50, width: 329, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 553,
    y: 50,
    width: 325,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 247, y: 50, width: 365, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 613,
    y: 50,
    width: 361,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 255, y: 50, width: 377, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 633,
    y: 50,
    width: 373,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 255, y: 50, width: 377, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 633,
    y: 50,
    width: 373,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 263, y: 50, width: 389, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 653,
    y: 50,
    width: 385,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 271, y: 50, width: 401, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 673,
    y: 50,
    width: 397,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 279, y: 50, width: 413, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 693,
    y: 50,
    width: 409,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 279, y: 50, width: 413, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 693,
    y: 50,
    width: 409,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 295, y: 50, width: 437, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 733,
    y: 50,
    width: 433,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 303, y: 50, width: 449, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 753,
    y: 50,
    width: 445,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 311, y: 50, width: 461, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 773,
    y: 50,
    width: 457,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 319, y: 50, width: 473, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 793,
    y: 50,
    width: 469,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 319, y: 50, width: 473, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 793,
    y: 50,
    width: 469,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 335, y: 50, width: 497, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 833,
    y: 50,
    width: 493,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 343, y: 50, width: 509, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 853,
    y: 50,
    width: 505,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 351, y: 50, width: 521, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 873,
    y: 50,
    width: 517,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 367, y: 50, width: 545, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 913,
    y: 50,
    width: 541,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 367, y: 50, width: 545, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 913,
    y: 50,
    width: 541,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 367, y: 50, width: 545, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 913,
    y: 50,
    width: 541,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 375, y: 50, width: 557, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 933,
    y: 50,
    width: 553,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 383, y: 50, width: 569, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 953,
    y: 50,
    width: 565,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 383, y: 50, width: 569, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 953,
    y: 50,
    width: 565,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 412, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1010,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 428, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1026,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 419, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1017,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 444, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1042,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 435, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1033,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 435, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1033,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
工具条位置已更新: { x: 37, y: 1, width: 1590, height: 749 }
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
📍 更新AI回复框位置信息: {
  fastResponse: { x: 435, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1033,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 435, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1033,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 435, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1033,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 435, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1033,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 499, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1097,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 499, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1097,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 499, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1097,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 499, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1097,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
📍 更新AI回复框位置信息: {
  fastResponse: { x: 499, y: 50, width: 598, height: 700, type: 'ai-response-fast' },
  accurateResponse: {
    x: 1097,
    y: 50,
    width: 594,
    height: 700,
    type: 'ai-response-accurate'
  }
}
工具条位置已更新: { x: 101, y: 1, width: 1590, height: 749 }
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🖱️ 检测到鼠标点击事件: { x: 472, y: 74, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 472, y: 74 },
  windowBounds: { x: 0, y: 0, width: 1792, height: 751 },
  toolbarBounds: { x: 101, y: 1, width: 1590, height: 749 },
  regionsCount: 5
}
使用渲染进程报告的工具条位置: { x: 101, y: 1, width: 1590, height: 749 }
鼠标位置 (472, 74) 在工具条范围内
点击在 voice-one-click-start 区域内
语音面板一键启动按钮被点击
🚀 执行语音识别流程启动: 系统音频=true, 麦克风=true
📡 步骤1: 启动系统音频ASR连接...
Starting SystemASR session for system audio
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
SystemASRManager: 系统音频识别启用状态: true
SystemASR: Starting connection attempt (ID: c56a6f92-db01-41a1-b4d2-aded51bb7bba)
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
SystemASR: WebSocket connection established
SystemASR: 发送初始化消息
连接状态更新: systemASR = true {
  systemASR: true,
  microphoneASR: false,
  systemAudio: false,
  microphone: false
}
✅ 系统音频ASR连接成功
SystemASR: Received FULL_SERVER_RESPONSE
SystemASR: Processing transcript response: {"audio_info":{"duration":0},"result":{"additions":{"log_id":"20250714202802F63B58FBEC47BADDB0B8"},"text":""}}
SystemASR: New session started with ID: 20250714202802F63B58FBEC47BADDB0B8
SystemASR: 距离上次转写超过30秒，清空临时记录
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
📡 启动系统音频捕获...
SystemAudioManager: 开始启动系统音频捕获
SystemAudioManager: 第一步 - 启动SystemASR服务
Starting SystemASR session for system audio
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
SystemASRManager: 系统音频识别启用状态: true
SystemASR: Active WebSocket connection already exists
SystemAudioManager: SystemASR服务启动成功
SystemAudioManager: 第二步 - 启动系统音频捕获
SystemAudioManager: macOS音频捕获命令: /Users/<USER>/Desktop/coder-master/bin/macos/system-audio-capture --output /Users/<USER>/Library/Application Support/secure-kernel/temp/system-audio.pcm
SystemAudioManager: 启动音频捕获进程... (尝试 1/4)
SystemAudioManager: 音频捕获进程已启动
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
SystemAudioManager: stdout: {"code":"RECORDING_STARTED","sampleRate":48000,"timestamp":"2025-07-14T12:28:04Z","channels":2,"format":"PCM 16-bit","path":"\/Users\/<USER>\/Library\/Application Support\/secure-kernel\/temp\/system-audio.pcm"}

SystemAudioManager: 录音已开始，文件保存在: /Users/<USER>/Library/Application Support/secure-kernel/temp/system-audio.pcm
macOS音频参数: 采样率=48000, 通道数=2, 位数=16
SystemAudioManager: 输出文件大小: 4096字节
开始监控PCM文件 (安全版本): /Users/<USER>/Library/Application Support/secure-kernel/temp/system-audio.pcm
采用安全读取机制，避免读写竞争
位置指针管理器已重置
启动音频文件监控，首次读取将在200ms后开始
连接状态更新: systemAudio = true {
  systemASR: true,
  microphoneASR: false,
  systemAudio: true,
  microphone: false
}
✅ 系统音频捕获启动成功
🎤 步骤2: 启动麦克风ASR连接...
🎤 Starting MicrophoneASR session for microphone audio
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
MicrophoneASRManager: 麦克风音频识别启用状态: true
MicrophoneASR: Starting connection attempt (ID: f25d742c-6e9d-4e90-a54c-b095035979ec)
MicrophoneASR: WebSocket connection established
MicrophoneASR: 发送初始化消息
连接状态更新: microphoneASR = true {
  systemASR: true,
  microphoneASR: true,
  systemAudio: true,
  microphone: false
}
✅ 麦克风ASR连接成功
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":0},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: New session started with ID: 20250714202805ABAFDF4BE475D8DC508D
MicrophoneASR: 距离上次转写超过15秒，清空临时记录
安全读取: 位置=0, 文件大小=57856, 待读取=57856字节
帧处理: 总数据=57856字节, 完整帧=57856字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57856字节, 缓存完整帧=0字节, 有效帧=57856字节, 新不完整帧=0字节
当前文件位置: 57856字节, 总读取: 0字节
发送音频数据: 9642字节, 4821样本, 能量=509.23, 静音=false
SystemASR: 处理音频数据，长度=9642字节, 采样率=16000Hz
安全读取并处理 57856 字节音频数据，新位置: 57856
SystemASR: 发送音频数据，序列号=2, 总大小=113字节
SystemASR: Received FULL_SERVER_RESPONSE
SystemASR: Processing transcript response: {"audio_info":{"duration":301},"result":{"additions":{"log_id":"20250714202802F63B58FBEC47BADDB0B8"},"text":""}}
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
安全读取: 位置=57856, 文件大小=123136, 待读取=65280字节
帧处理: 总数据=65280字节, 完整帧=65280字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=65280字节, 缓存完整帧=0字节, 有效帧=65280字节, 新不完整帧=0字节
当前文件位置: 123136字节, 总读取: 57856字节
发送音频数据: 10880字节, 5440样本, 能量=0.00, 静音=true
SystemASR: 检测到几乎完全静音数据，跳过发送
安全读取并处理 65280 字节音频数据，新位置: 123136
安全读取: 位置=123136, 文件大小=184576, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 184576
🎤 启动麦克风捕获...
🎤 MicrophoneManager: 开始启动麦克风音频捕获
🎤 MicrophoneManager: 第一步 - 启动MicrophoneASRManager服务
🎤 Starting MicrophoneASR session for microphone audio
从配置中读取语音配置: {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
MicrophoneASRManager: 麦克风音频识别启用状态: true
✅ MicrophoneASR: Active WebSocket connection already exists
🎤 MicrophoneManager: MicrophoneASRManager启动结果: { success: true }
✅ MicrophoneManager: MicrophoneASRManager服务启动成功
🎤 MicrophoneManager: 第二步 - 启动麦克风音频捕获
📡 MicrophoneManager: 发送麦克风状态到渲染进程
✅ MicrophoneManager: 已通知渲染进程录制开始
✅ MicrophoneManager: 麦克风音频捕获启动成功
连接状态更新: microphone = true {
  systemASR: true,
  microphoneASR: true,
  systemAudio: true,
  microphone: true
}
✅ 麦克风捕获启动成功
📡 发送 start-microphone-recognition 事件到渲染进程
🎯 语音识别流程启动结果: 整体=true, 系统音频=true, 麦克风=true
📊 连接状态: {
  systemASR: true,
  microphoneASR: true,
  systemAudio: true,
  microphone: true
}
安全读取: 位置=184576, 文件大小=246016, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 246016
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
安全读取: 位置=246016, 文件大小=303616, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
检测到静音开始
当前文件位置: 303616字节, 总读取: 246016字节
发送音频数据: 9600字节, 4800样本, 能量=0.00, 静音=true
SystemASR: 检测到几乎完全静音数据，跳过发送
安全读取并处理 57600 字节音频数据，新位置: 303616
安全读取: 位置=303616, 文件大小=365056, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 365056
IPC: 收到启动麦克风音频捕获请求
🎤 MicrophoneManager: 开始启动麦克风音频捕获
🎤 MicrophoneManager: 麦克风已在录制中
IPC: 收到启动麦克风音频捕获请求
🎤 MicrophoneManager: 开始启动麦克风音频捕获
🎤 MicrophoneManager: 麦克风已在录制中
安全读取: 位置=365056, 文件大小=430336, 待读取=65280字节
帧处理: 总数据=65280字节, 完整帧=65280字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=65280字节, 缓存完整帧=0字节, 有效帧=65280字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 65280 字节音频数据，新位置: 430336
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
安全读取: 位置=430336, 文件大小=491776, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 491776
安全读取: 位置=491776, 文件大小=549376, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 57600 字节音频数据，新位置: 549376
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3584
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496088041 处理麦克风音频数据，长度=3584字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0141, 动态范围: 64.23dB, 信噪比估计: 64.23dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=2, 总大小=1641字节, 原始长度=3584字节, 压缩率=45.45%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":112},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496088141 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0266, 动态范围: 66.66dB, 信噪比估计: 66.66dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=3, 总大小=2897字节, 原始长度=3328字节, 压缩率=86.69%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":216},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
安全读取: 位置=549376, 文件大小=610816, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 610816
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496088251 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0211, 动态范围: 64.09dB, 信噪比估计: 64.09dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=4, 总大小=2819字节, 原始长度=3328字节, 压缩率=84.34%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":320},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496088351 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0104, 动态范围: 61.16dB, 信噪比估计: 61.16dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=5, 总大小=2653字节, 原始长度=3328字节, 压缩率=79.36%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":424},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496088452 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0135, 动态范围: 60.68dB, 信噪比估计: 60.68dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=6, 总大小=2720字节, 原始长度=3328字节, 压缩率=81.37%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":528},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
安全读取: 位置=610816, 文件大小=676096, 待读取=65280字节
帧处理: 总数据=65280字节, 完整帧=65280字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=65280字节, 缓存完整帧=0字节, 有效帧=65280字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 65280 字节音频数据，新位置: 676096
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496088771 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0021, 动态范围: 46.65dB, 信噪比估计: 46.65dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=7, 总大小=2336字节, 原始长度=3328字节, 压缩率=69.83%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=676096, 文件大小=737536, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 737536
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":632},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496089081 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0023, 动态范围: 47.78dB, 信噪比估计: 47.78dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=8, 总大小=2338字节, 原始长度=3328字节, 压缩率=69.89%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":736},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
安全读取: 位置=737536, 文件大小=795136, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 57600 字节音频数据，新位置: 795136
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496089182 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0020, 动态范围: 48.37dB, 信噪比估计: 48.37dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=9, 总大小=2322字节, 原始长度=3328字节, 压缩率=69.41%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":840},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
安全读取: 位置=795136, 文件大小=856576, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 856576
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496089592 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0025, 动态范围: 47.85dB, 信噪比估计: 47.85dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=10, 总大小=2335字节, 原始长度=3328字节, 压缩率=69.80%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=856576, 文件大小=918016, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 918016
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":944},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
安全读取: 位置=918016, 文件大小=975616, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 57600 字节音频数据，新位置: 975616
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496090121 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0105, 动态范围: 61.98dB, 信噪比估计: 61.98dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=11, 总大小=2643字节, 原始长度=3328字节, 压缩率=79.06%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":1048},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496090221 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0041, 动态范围: 54.82dB, 信噪比估计: 54.82dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=12, 总大小=2428字节, 原始长度=3328字节, 压缩率=72.60%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":1152},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
安全读取: 位置=975616, 文件大小=1037056, 待读取=61440字节
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496090432 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0176, 动态范围: 67.88dB, 信噪比估计: 67.88dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=13, 总大小=2701字节, 原始长度=3328字节, 压缩率=80.80%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 1037056
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496090531 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=3328字节
MicrophoneASR: 准备发送音频数据 - 长度: 3328字节, 采样率: 16000Hz, 格式: PCM16
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: 发送音频数据，序列号=14, 总大小=2884字节, 原始长度=3328字节, 压缩率=86.30%
MicrophoneASR: 消息头详情 - MessageType: AUDIO_ONLY_REQUEST(0x02), SerializationMethod: JSON_FORMAT(0x01), CompressionType: GZIP(0x01)
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496090631 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0215, 动态范围: 65.30dB, 信噪比估计: 65.30dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=15, 总大小=2814字节, 原始长度=3328字节, 压缩率=84.19%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":1256},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":1360},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":1464},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
安全读取: 位置=1037056, 文件大小=1098496, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 1098496
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496090751 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0070, 动态范围: 60.32dB, 信噪比估计: 60.32dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=16, 总大小=2563字节, 原始长度=3328字节, 压缩率=76.65%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496090851 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0301, 动态范围: 68.69dB, 信噪比估计: 68.69dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=17, 总大小=2881字节, 原始长度=3328字节, 压缩率=86.21%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496090951 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0260, 动态范围: 68.05dB, 信噪比估计: 68.05dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=18, 总大小=2876字节, 原始长度=3328字节, 压缩率=86.06%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":1568},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":1672},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":1776},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496091051 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0084, 动态范围: 59.74dB, 信噪比估计: 59.74dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=19, 总大小=2602字节, 原始长度=3328字节, 压缩率=77.82%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1098496, 文件大小=1159936, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 1159936
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496091161 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0157, 动态范围: 63.90dB, 信噪比估计: 63.90dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=20, 总大小=2773字节, 原始长度=3328字节, 压缩率=82.96%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496091262 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0056, 动态范围: 56.65dB, 信噪比估计: 56.65dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=21, 总大小=2503字节, 原始长度=3328字节, 压缩率=74.85%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":1880},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":1984},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":2088},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496091371 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0120, 动态范围: 59.42dB, 信噪比估计: 59.42dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=22, 总大小=2691字节, 原始长度=3328字节, 压缩率=80.50%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1159936, 文件大小=1221376, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 1221376
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496091471 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0202, 动态范围: 65.14dB, 信噪比估计: 65.14dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=23, 总大小=2820字节, 原始长度=3328字节, 压缩率=84.38%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496091571 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0208, 动态范围: 64.36dB, 信噪比估计: 64.36dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=24, 总大小=2824字节, 原始长度=3328字节, 压缩率=84.50%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":2192},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":2296},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":2400},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496091671 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0401, 动态范围: 73.66dB, 信噪比估计: 73.66dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=25, 总大小=2873字节, 原始长度=3328字节, 压缩率=85.97%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1221376, 文件大小=1275136, 待读取=53760字节
帧处理: 总数据=53760字节, 完整帧=53760字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=53760字节, 缓存完整帧=0字节, 有效帧=53760字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 53760 字节音频数据，新位置: 1275136
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496091791 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0291, 动态范围: 71.86dB, 信噪比估计: 71.86dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=26, 总大小=2898字节, 原始长度=3328字节, 压缩率=86.72%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496091891 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0298, 动态范围: 71.74dB, 信噪比估计: 71.74dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=27, 总大小=2924字节, 原始长度=3328字节, 压缩率=87.50%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":2504},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":2608},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":2712},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496091991 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0438, 动态范围: 74.62dB, 信噪比估计: 74.62dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=28, 总大小=2989字节, 原始长度=3328字节, 压缩率=89.45%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1275136, 文件大小=1336576, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 1336576
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496092091 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0992, 动态范围: 78.62dB, 信噪比估计: 78.62dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=29, 总大小=3189字节, 原始长度=3328字节, 压缩率=95.46%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496092201 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0714, 动态范围: 76.99dB, 信噪比估计: 76.99dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=30, 总大小=3106字节, 原始长度=3328字节, 压缩率=92.97%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":2816},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":2920},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":3024},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496092302 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0431, 动态范围: 72.94dB, 信噪比估计: 72.94dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=31, 总大小=3008字节, 原始长度=3328字节, 压缩率=90.02%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1336576, 文件大小=1398016, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 1398016
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496092412 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0776, 动态范围: 62.43dB, 信噪比估计: 62.43dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=32, 总大小=3125字节, 原始长度=3328字节, 压缩率=93.54%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496092512 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0407, 动态范围: 72.66dB, 信噪比估计: 72.66dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=33, 总大小=2998字节, 原始长度=3328字节, 压缩率=89.72%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":3128},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":3232},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":3336},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496092611 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=3328字节
MicrophoneASR: 准备发送音频数据 - 长度: 3328字节, 采样率: 16000Hz, 格式: PCM16
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: 发送音频数据，序列号=34, 总大小=2833字节, 原始长度=3328字节, 压缩率=84.77%
MicrophoneASR: 消息头详情 - MessageType: AUDIO_ONLY_REQUEST(0x02), SerializationMethod: JSON_FORMAT(0x01), CompressionType: GZIP(0x01)
安全读取: 位置=1398016, 文件大小=1459456, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 1459456
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496092712 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0213, 动态范围: 66.02dB, 信噪比估计: 66.02dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=35, 总大小=2849字节, 原始长度=3328字节, 压缩率=85.25%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496092832 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0207, 动态范围: 66.88dB, 信噪比估计: 66.88dB, 有效信号: true
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":3440},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: 发送音频数据，序列号=36, 总大小=2837字节, 原始长度=3328字节, 压缩率=84.89%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":3544},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":3648},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496092931 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=3328字节
MicrophoneASR: 准备发送音频数据 - 长度: 3328字节, 采样率: 16000Hz, 格式: PCM16
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: 发送音频数据，序列号=37, 总大小=2535字节, 原始长度=3328字节, 压缩率=75.81%
MicrophoneASR: 消息头详情 - MessageType: AUDIO_ONLY_REQUEST(0x02), SerializationMethod: JSON_FORMAT(0x01), CompressionType: GZIP(0x01)
安全读取: 位置=1459456, 文件大小=1520896, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 1520896
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496093031 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=3328字节
MicrophoneASR: 准备发送音频数据 - 长度: 3328字节, 采样率: 16000Hz, 格式: PCM16
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: 发送音频数据，序列号=38, 总大小=2430字节, 原始长度=3328字节, 压缩率=72.66%
MicrophoneASR: 消息头详情 - MessageType: AUDIO_ONLY_REQUEST(0x02), SerializationMethod: JSON_FORMAT(0x01), CompressionType: GZIP(0x01)
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496093131 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=3328字节
MicrophoneASR: 准备发送音频数据 - 长度: 3328字节, 采样率: 16000Hz, 格式: PCM16
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: 发送音频数据，序列号=39, 总大小=2446字节, 原始长度=3328字节, 压缩率=73.14%
MicrophoneASR: 消息头详情 - MessageType: AUDIO_ONLY_REQUEST(0x02), SerializationMethod: JSON_FORMAT(0x01), CompressionType: GZIP(0x01)
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":3752},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":3856},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":3960},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496093241 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0086, 动态范围: 63.35dB, 信噪比估计: 63.35dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=40, 总大小=2591字节, 原始长度=3328字节, 压缩率=77.49%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1520896, 文件大小=1582336, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 1582336
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496093341 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0089, 动态范围: 65.58dB, 信噪比估计: 65.58dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=41, 总大小=2568字节, 原始长度=3328字节, 压缩率=76.80%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":4064},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496093441 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0742, 动态范围: 85.68dB, 信噪比估计: 85.68dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=42, 总大小=2944字节, 原始长度=3328字节, 压缩率=88.10%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":4168},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":4272},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496093551 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.1770, 动态范围: 89.06dB, 信噪比估计: 89.06dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=43, 总大小=3236字节, 原始长度=3328字节, 压缩率=96.88%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1582336, 文件大小=1639936, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 57600 字节音频数据，新位置: 1639936
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":4376},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496093651 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0909, 动态范围: 83.21dB, 信噪比估计: 83.21dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=44, 总大小=3083字节, 原始长度=3328字节, 压缩率=92.28%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496093751 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0733, 动态范围: 82.99dB, 信噪比估计: 82.99dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=45, 总大小=2964字节, 原始长度=3328字节, 压缩率=88.70%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":4480},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496093871 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0072, 动态范围: 70.87dB, 信噪比估计: 70.87dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=46, 总大小=2456字节, 原始长度=3328字节, 压缩率=73.44%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":4584},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
安全读取: 位置=1639936, 文件大小=1701376, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 1701376
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":4688},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496093971 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0224, 动态范围: 79.92dB, 信噪比估计: 79.92dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=47, 总大小=2691字节, 原始长度=3328字节, 压缩率=80.50%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496094071 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0136, 动态范围: 67.66dB, 信噪比估计: 67.66dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=48, 总大小=2682字节, 原始长度=3328字节, 压缩率=80.23%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":4792},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":4896},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
安全读取: 位置=1701376, 文件大小=1762816, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 1762816
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496094281 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0135, 动态范围: 77.43dB, 信噪比估计: 77.43dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=49, 总大小=2350字节, 原始长度=3328字节, 压缩率=70.25%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":5000},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496094491 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0026, 动态范围: 52.81dB, 信噪比估计: 52.81dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=50, 总大小=2322字节, 原始长度=3328字节, 压缩率=69.41%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1762816, 文件大小=1824256, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 1824256
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":5104},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496094591 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0094, 动态范围: 60.37dB, 信噪比估计: 60.37dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=51, 总大小=2613字节, 原始长度=3328字节, 压缩率=78.16%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":5208},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496094691 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0239, 动态范围: 67.52dB, 信噪比估计: 67.52dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=52, 总大小=2868字节, 原始长度=3328字节, 压缩率=85.82%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":5312},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496094791 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0365, 动态范围: 72.78dB, 信噪比估计: 72.78dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=53, 总大小=2953字节, 原始长度=3328字节, 压缩率=88.37%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1824256, 文件大小=1885696, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 1885696
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":5416},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496094911 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0205, 动态范围: 67.68dB, 信噪比估计: 67.68dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=54, 总大小=2806字节, 原始长度=3328字节, 压缩率=83.95%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496095011 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0289, 动态范围: 71.86dB, 信噪比估计: 71.86dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=55, 总大小=2707字节, 原始长度=3328字节, 压缩率=80.98%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496095111 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0353, 动态范围: 71.05dB, 信噪比估计: 71.05dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=56, 总大小=2965字节, 原始长度=3328字节, 压缩率=88.73%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=1885696, 文件大小=1947136, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 1947136
音频监控统计 - 运行时间: 10.1s, 总读取: 32, 成功: 32, 空读: 0, 错误: 0
数据统计 - 总字节: 1947136, 平均速率: 192139 bytes/s, 不完整帧缓存: 0字节
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":5520},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":5624},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496095211 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0443, 动态范围: 73.39dB, 信噪比估计: 73.39dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=57, 总大小=3010字节, 原始长度=3328字节, 压缩率=90.08%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":5728},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496095321 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0131, 动态范围: 65.33dB, 信噪比估计: 65.33dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=58, 总大小=2697字节, 原始长度=3328字节, 压缩率=80.68%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496095421 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0282, 动态范围: 71.26dB, 信噪比估计: 71.26dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=59, 总大小=2866字节, 原始长度=3328字节, 压缩率=85.76%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":5832},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":5936},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
安全读取: 位置=1947136, 文件大小=2008576, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 2008576
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":6040},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496095521 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0140, 动态范围: 63.30dB, 信噪比估计: 63.30dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=60, 总大小=2735字节, 原始长度=3328字节, 压缩率=81.82%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496095631 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0100, 动态范围: 63.36dB, 信噪比估计: 63.36dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=61, 总大小=2634字节, 原始长度=3328字节, 压缩率=78.79%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496095731 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0080, 动态范围: 57.86dB, 信噪比估计: 57.86dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=62, 总大小=2595字节, 原始长度=3328字节, 压缩率=77.61%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":6144},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":6248},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
安全读取: 位置=2008576, 文件大小=2070016, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 2070016
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":6352},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496095831 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0316, 动态范围: 72.00dB, 信噪比估计: 72.00dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=63, 总大小=2921字节, 原始长度=3328字节, 压缩率=87.41%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496095951 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0135, 动态范围: 66.22dB, 信噪比估计: 66.22dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=64, 总大小=2680字节, 原始长度=3328字节, 压缩率=80.17%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496096051 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0420, 动态范围: 73.74dB, 信噪比估计: 73.74dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=65, 总大小=2996字节, 原始长度=3328字节, 压缩率=89.66%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":6456},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":6560},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
安全读取: 位置=2070016, 文件大小=2131456, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 2131456
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":6664},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496096151 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0366, 动态范围: 71.55dB, 信噪比估计: 71.55dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=66, 总大小=2976字节, 原始长度=3328字节, 压缩率=89.06%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":6768},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496096251 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0173, 动态范围: 66.27dB, 信噪比估计: 66.27dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=67, 总大小=2787字节, 原始长度=3328字节, 压缩率=83.38%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
SystemASR: 超过8秒未接收到音频数据，准备关闭服务
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496096362 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0135, 动态范围: 62.83dB, 信噪比估计: 62.83dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=68, 总大小=2718字节, 原始长度=3328字节, 压缩率=81.31%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
安全读取: 位置=2131456, 文件大小=2192896, 待读取=61440字节
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 2192896
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496096461 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=3328字节
MicrophoneASR: 准备发送音频数据 - 长度: 3328字节, 采样率: 16000Hz, 格式: PCM16
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: 发送音频数据，序列号=69, 总大小=2429字节, 原始长度=3328字节, 压缩率=72.63%
MicrophoneASR: 消息头详情 - MessageType: AUDIO_ONLY_REQUEST(0x02), SerializationMethod: JSON_FORMAT(0x01), CompressionType: GZIP(0x01)
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":6872},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":6976},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":7080},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":""}}
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496096571 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0433, 动态范围: 79.10dB, 信噪比估计: 79.10dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=70, 总大小=2652字节, 原始长度=3328字节, 压缩率=79.33%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496096671 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.1641, 动态范围: 77.33dB, 信噪比估计: 77.33dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=71, 总大小=3270字节, 原始长度=3328字节, 压缩率=97.90%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2192896, 文件大小=2254336, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 2254336
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496096771 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.1781, 动态范围: 65.51dB, 信噪比估计: 65.51dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=72, 总大小=3265字节, 原始长度=3328字节, 压缩率=97.75%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":7184},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完","utterances":[{"definite":false,"end_time":6892,"start_time":6512,"text":"你做完","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6892,"start_time":6812,"text":"做完"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":7288},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完","utterances":[{"definite":false,"end_time":6892,"start_time":6512,"text":"你做完","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6892,"start_time":6812,"text":"做完"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496096871 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.1909, 动态范围: 60.63dB, 信噪比估计: 60.63dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=73, 总大小=3301字节, 原始长度=3328字节, 压缩率=98.83%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":7392},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完","utterances":[{"definite":false,"end_time":6892,"start_time":6512,"text":"你做完","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6892,"start_time":6812,"text":"做完"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496096991 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.1874, 动态范围: 60.78dB, 信噪比估计: 60.78dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=74, 总大小=3293字节, 原始长度=3328字节, 压缩率=98.59%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2254336, 文件大小=2315776, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 2315776
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496097081 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=3328字节
MicrophoneASR: 准备发送音频数据 - 长度: 3328字节, 采样率: 16000Hz, 格式: PCM16
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: 发送音频数据，序列号=75, 总大小=3302字节, 原始长度=3328字节, 压缩率=98.86%
MicrophoneASR: 消息头详情 - MessageType: AUDIO_ONLY_REQUEST(0x02), SerializationMethod: JSON_FORMAT(0x01), CompressionType: GZIP(0x01)
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":7496},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完","utterances":[{"definite":false,"end_time":6892,"start_time":6512,"text":"你做完","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6892,"start_time":6812,"text":"做完"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完"
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":7600},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完","utterances":[{"definite":false,"end_time":6892,"start_time":6512,"text":"你做完","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6892,"start_time":6812,"text":"做完"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完"
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":7704},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完","utterances":[{"definite":false,"end_time":6892,"start_time":6512,"text":"你做完","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6892,"start_time":6812,"text":"做完"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496097192 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.1174, 动态范围: 70.01dB, 信噪比估计: 70.01dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=76, 总大小=3202字节, 原始长度=3328字节, 压缩率=95.85%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496097291 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=3328字节
MicrophoneASR: 准备发送音频数据 - 长度: 3328字节, 采样率: 16000Hz, 格式: PCM16
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: 发送音频数据，序列号=77, 总大小=3184字节, 原始长度=3328字节, 压缩率=95.31%
MicrophoneASR: 消息头详情 - MessageType: AUDIO_ONLY_REQUEST(0x02), SerializationMethod: JSON_FORMAT(0x01), CompressionType: GZIP(0x01)
安全读取: 位置=2315776, 文件大小=2377216, 待读取=61440字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496097401 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0767, 动态范围: 76.22dB, 信噪比估计: 76.22dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=78, 总大小=3129字节, 原始长度=3328字节, 压缩率=93.66%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 2377216
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":7808},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完","utterances":[{"definite":false,"end_time":6892,"start_time":6512,"text":"你做完","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6892,"start_time":6812,"text":"做完"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完"
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":7912},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完","utterances":[{"definite":false,"end_time":6892,"start_time":6512,"text":"你做完","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6892,"start_time":6812,"text":"做完"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完"
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":8016},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完","utterances":[{"definite":false,"end_time":6892,"start_time":6512,"text":"你做完","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6892,"start_time":6812,"text":"做完"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496097502 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0686, 动态范围: 71.36dB, 信噪比估计: 71.36dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=79, 总大小=3109字节, 原始长度=3328字节, 压缩率=93.06%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496097612 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0137, 动态范围: 65.29dB, 信噪比估计: 65.29dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=80, 总大小=2660字节, 原始长度=3328字节, 压缩率=79.57%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2377216, 文件大小=2438656, 待读取=61440字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496097712 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0037, 动态范围: 54.05dB, 信噪比估计: 54.05dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=81, 总大小=2396字节, 原始长度=3328字节, 压缩率=71.63%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 2438656
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":8120},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂","utterances":[{"definite":false,"end_time":7772,"start_time":6512,"text":"你做完，喂喂","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7772,"start_time":7692,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":8224},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂","utterances":[{"definite":false,"end_time":7772,"start_time":6512,"text":"你做完，喂喂","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7772,"start_time":7692,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂"
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":8328},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂","utterances":[{"definite":false,"end_time":7772,"start_time":6512,"text":"你做完，喂喂","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7772,"start_time":7692,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496097811 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=3328字节
MicrophoneASR: 准备发送音频数据 - 长度: 3328字节, 采样率: 16000Hz, 格式: PCM16
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: 发送音频数据，序列号=82, 总大小=2369字节, 原始长度=3328字节, 压缩率=70.82%
MicrophoneASR: 消息头详情 - MessageType: AUDIO_ONLY_REQUEST(0x02), SerializationMethod: JSON_FORMAT(0x01), CompressionType: GZIP(0x01)
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496097921 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0071, 动态范围: 64.13dB, 信噪比估计: 64.13dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=83, 总大小=2441字节, 原始长度=3328字节, 压缩率=72.99%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2438656, 文件大小=2500096, 待读取=61440字节
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496098032 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0373, 动态范围: 70.60dB, 信噪比估计: 70.60dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=84, 总大小=2972字节, 原始长度=3328字节, 压缩率=88.94%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 2500096
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":8432},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":8536},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂"
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":8640},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496098131 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=3328字节
MicrophoneASR: 准备发送音频数据 - 长度: 3328字节, 采样率: 16000Hz, 格式: PCM16
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: 发送音频数据，序列号=85, 总大小=2871字节, 原始长度=3328字节, 压缩率=85.91%
MicrophoneASR: 消息头详情 - MessageType: AUDIO_ONLY_REQUEST(0x02), SerializationMethod: JSON_FORMAT(0x01), CompressionType: GZIP(0x01)
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496098231 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=3328字节
MicrophoneASR: 准备发送音频数据 - 长度: 3328字节, 采样率: 16000Hz, 格式: PCM16
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: 发送音频数据，序列号=86, 总大小=2943字节, 原始长度=3328字节, 压缩率=88.07%
MicrophoneASR: 消息头详情 - MessageType: AUDIO_ONLY_REQUEST(0x02), SerializationMethod: JSON_FORMAT(0x01), CompressionType: GZIP(0x01)
SystemASR: 由于音频数据超时，自动关闭会话
SystemASR: Stopping ASR session
SystemASR: Stopping session
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496098331 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=3328字节
MicrophoneASR: 准备发送音频数据 - 长度: 3328字节, 采样率: 16000Hz, 格式: PCM16
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: 发送音频数据，序列号=87, 总大小=2901字节, 原始长度=3328字节, 压缩率=86.81%
MicrophoneASR: 消息头详情 - MessageType: AUDIO_ONLY_REQUEST(0x02), SerializationMethod: JSON_FORMAT(0x01), CompressionType: GZIP(0x01)
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":8744},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂"
安全读取: 位置=2500096, 文件大小=2561536, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 2561536
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":8848},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂"
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":8952},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂"
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496098441 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0335, 动态范围: 66.13dB, 信噪比估计: 66.13dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=88, 总大小=2904字节, 原始长度=3328字节, 压缩率=86.90%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496098541 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0201, 动态范围: 65.85dB, 信噪比估计: 65.85dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=89, 总大小=2672字节, 原始长度=3328字节, 压缩率=79.93%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496098641 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0346, 动态范围: 73.32dB, 信噪比估计: 73.32dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=90, 总大小=2929字节, 原始长度=3328字节, 压缩率=87.65%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":9056},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂"
安全读取: 位置=2561536, 文件大小=2615296, 待读取=53760字节
帧处理: 总数据=53760字节, 完整帧=53760字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=53760字节, 缓存完整帧=0字节, 有效帧=53760字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 53760 字节音频数据，新位置: 2615296
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":9160},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂"
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":9264},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496098752 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0267, 动态范围: 70.06dB, 信噪比估计: 70.06dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=91, 总大小=2892字节, 原始长度=3328字节, 压缩率=86.54%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496098851 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=3328字节
MicrophoneASR: 准备发送音频数据 - 长度: 3328字节, 采样率: 16000Hz, 格式: PCM16
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: 发送音频数据，序列号=92, 总大小=2731字节, 原始长度=3328字节, 压缩率=81.70%
MicrophoneASR: 消息头详情 - MessageType: AUDIO_ONLY_REQUEST(0x02), SerializationMethod: JSON_FORMAT(0x01), CompressionType: GZIP(0x01)
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":9368},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496098962 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0168, 动态范围: 63.29dB, 信噪比估计: 63.29dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=93, 总大小=2775字节, 原始长度=3328字节, 压缩率=83.02%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2615296, 文件大小=2676736, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 2676736
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":9472},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂"
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":9576},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496099071 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0188, 动态范围: 69.35dB, 信噪比估计: 69.35dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=94, 总大小=2766字节, 原始长度=3328字节, 压缩率=82.75%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496099171 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0202, 动态范围: 68.78dB, 信噪比估计: 68.78dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=95, 总大小=2812字节, 原始长度=3328字节, 压缩率=84.13%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496099271 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0245, 动态范围: 69.75dB, 信噪比估计: 69.75dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=96, 总大小=2873字节, 原始长度=3328字节, 压缩率=85.97%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2676736, 文件大小=2734336, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 57600 字节音频数据，新位置: 2734336
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":9680},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":9784},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂。"
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":9888},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂。"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496099371 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0218, 动态范围: 70.33dB, 信噪比估计: 70.33dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=97, 总大小=2832字节, 原始长度=3328字节, 压缩率=84.74%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496099481 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0185, 动态范围: 68.04dB, 信噪比估计: 68.04dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=98, 总大小=2773字节, 原始长度=3328字节, 压缩率=82.96%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496099581 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0191, 动态范围: 61.37dB, 信噪比估计: 61.37dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=99, 总大小=2824字节, 原始长度=3328字节, 压缩率=84.50%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2734336, 文件大小=2795776, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 2795776
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":9992},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂。"
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":10096},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂。"
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":10200},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂。"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496099681 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0065, 动态范围: 58.28dB, 信噪比估计: 58.28dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=100, 总大小=2541字节, 原始长度=3328字节, 压缩率=75.99%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496099891 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0029, 动态范围: 48.94dB, 信噪比估计: 48.94dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=101, 总大小=2355字节, 原始长度=3328字节, 压缩率=70.40%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2795776, 文件大小=2857216, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 2857216
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":10304},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂。"
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":10408},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂。"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496100001 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0031, 动态范围: 49.51dB, 信噪比估计: 49.51dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=102, 总大小=2362字节, 原始长度=3328字节, 压缩率=70.61%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":10512},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂。"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496100111 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0022, 动态范围: 47.16dB, 信噪比估计: 47.16dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=103, 总大小=2337字节, 原始长度=3328字节, 压缩率=69.86%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2857216, 文件大小=2918656, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 2918656
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496100311 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0047, 动态范围: 58.98dB, 信噪比估计: 58.98dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=104, 总大小=2437字节, 原始长度=3328字节, 压缩率=72.87%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":10616},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂。"
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":10720},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂。"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496100411 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0287, 动态范围: 69.93dB, 信噪比估计: 69.93dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=105, 总大小=2904字节, 原始长度=3328字节, 压缩率=86.90%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":10824},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂。"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496100521 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0307, 动态范围: 71.11dB, 信噪比估计: 71.11dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=106, 总大小=2931字节, 原始长度=3328字节, 压缩率=87.71%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=2918656, 文件大小=2980096, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 2980096
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":10928},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂。"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496100621 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0543, 动态范围: 72.58dB, 信噪比估计: 72.58dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=107, 总大小=3054字节, 原始长度=3328字节, 压缩率=91.41%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496100721 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0132, 动态范围: 64.06dB, 信噪比估计: 64.06dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=108, 总大小=2704字节, 原始长度=3328字节, 压缩率=80.89%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":11032},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":false,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]}]}}
MicrophoneASR: Processing 1 utterances
MicrophoneASR: 跳过重复的临时结果: "你做完，喂喂喂喂。"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496100831 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0879, 动态范围: 78.55dB, 信噪比估计: 78.55dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=109, 总大小=3157字节, 原始长度=3328字节, 压缩率=94.50%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":11136},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
UnifiedHistoryManager: 🔍 尝试添加历史项 [microphone]: "你做完，喂喂喂喂。"
UnifiedHistoryManager: 🔍 当前历史记录数量: 0
UnifiedHistoryManager: 🔍 去重缓存大小: 0
UnifiedHistoryManager: 🔍 检查重复 [microphone]: "你做完，喂喂喂喂。"
UnifiedHistoryManager: 🔍 未发现重复，允许添加
UnifiedHistoryManager: 🚀 发送统一历史更新，共 1 条记录 { microphone: 1 }
UnifiedHistoryManager 📊 统计: 总更新=6, 节流更新=0, 过滤重复=0
UnifiedHistoryManager: ✅ 添加历史项 [microphone]: "你做完，喂喂喂喂。"
UnifiedHistoryManager: 📊 当前历史记录总数: 1
安全读取: 位置=2980096, 文件大小=3041536, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 3041536
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":11240},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
MicrophoneASR: 跳过完全相同的文本: "你做完，喂喂喂喂。"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496100941 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0347, 动态范围: 72.68dB, 信噪比估计: 72.68dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=110, 总大小=2950字节, 原始长度=3328字节, 压缩率=88.28%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496101031 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=3328字节
MicrophoneASR: 准备发送音频数据 - 长度: 3328字节, 采样率: 16000Hz, 格式: PCM16
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: 发送音频数据，序列号=111, 总大小=2912字节, 原始长度=3328字节, 压缩率=87.14%
MicrophoneASR: 消息头详情 - MessageType: AUDIO_ONLY_REQUEST(0x02), SerializationMethod: JSON_FORMAT(0x01), CompressionType: GZIP(0x01)
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":11344},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
MicrophoneASR: 跳过完全相同的文本: "你做完，喂喂喂喂。"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496101151 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0349, 动态范围: 70.44dB, 信噪比估计: 70.44dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=112, 总大小=2964字节, 原始长度=3328字节, 压缩率=88.70%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":11448},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
MicrophoneASR: 跳过完全相同的文本: "你做完，喂喂喂喂。"
安全读取: 位置=3041536, 文件大小=3102976, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 3102976
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":11552},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
MicrophoneASR: 跳过完全相同的文本: "你做完，喂喂喂喂。"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496101251 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0351, 动态范围: 50.58dB, 信噪比估计: 50.58dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=113, 总大小=2921字节, 原始长度=3328字节, 压缩率=87.41%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496101351 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0299, 动态范围: 61.42dB, 信噪比估计: 61.42dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=114, 总大小=2896字节, 原始长度=3328字节, 压缩率=86.66%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496101451 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0285, 动态范围: 68.33dB, 信噪比估计: 68.33dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=115, 总大小=2912字节, 原始长度=3328字节, 压缩率=87.14%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":11656},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
MicrophoneASR: 跳过完全相同的文本: "你做完，喂喂喂喂。"
安全读取: 位置=3102976, 文件大小=3164416, 待读取=61440字节
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":11760},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
MicrophoneASR: 跳过完全相同的文本: "你做完，喂喂喂喂。"
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 3164416
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":11864},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
MicrophoneASR: 跳过完全相同的文本: "你做完，喂喂喂喂。"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496101561 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0206, 动态范围: 65.38dB, 信噪比估计: 65.38dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=116, 总大小=2819字节, 原始长度=3328字节, 压缩率=84.34%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496101663 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0095, 动态范围: 60.06dB, 信噪比估计: 60.06dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=117, 总大小=2637字节, 原始长度=3328字节, 压缩率=78.88%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496101771 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0338, 动态范围: 68.91dB, 信噪比估计: 68.91dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=118, 总大小=2954字节, 原始长度=3328字节, 压缩率=88.40%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":11968},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
MicrophoneASR: 跳过完全相同的文本: "你做完，喂喂喂喂。"
安全读取: 位置=3164416, 文件大小=3222016, 待读取=57600字节
帧处理: 总数据=57600字节, 完整帧=57600字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=57600字节, 缓存完整帧=0字节, 有效帧=57600字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 57600 字节音频数据，新位置: 3222016
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":12072},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
MicrophoneASR: 跳过完全相同的文本: "你做完，喂喂喂喂。"
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":12176},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
MicrophoneASR: 跳过完全相同的文本: "你做完，喂喂喂喂。"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496101871 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0213, 动态范围: 68.92dB, 信噪比估计: 68.92dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=119, 总大小=2821字节, 原始长度=3328字节, 压缩率=84.41%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496101971 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0039, 动态范围: 54.99dB, 信噪比估计: 54.99dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=120, 总大小=2420字节, 原始长度=3328字节, 压缩率=72.36%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":12280},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
MicrophoneASR: 跳过完全相同的文本: "你做完，喂喂喂喂。"
安全读取: 位置=3222016, 文件大小=3283456, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 3283456
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":12384},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
MicrophoneASR: 跳过完全相同的文本: "你做完，喂喂喂喂。"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496102191 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0029, 动态范围: 49.71dB, 信噪比估计: 49.71dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=121, 总大小=2356字节, 原始长度=3328字节, 压缩率=70.43%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":12488},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
MicrophoneASR: 跳过完全相同的文本: "你做完，喂喂喂喂。"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496102291 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0075, 动态范围: 57.03dB, 信噪比估计: 57.03dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=122, 总大小=2568字节, 原始长度=3328字节, 压缩率=76.80%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=3283456, 文件大小=3321856, 待读取=38400字节
帧处理: 总数据=38400字节, 完整帧=38400字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=38400字节, 缓存完整帧=0字节, 有效帧=38400字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 38400 字节音频数据，新位置: 3321856
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496102391 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0037, 动态范围: 53.42dB, 信噪比估计: 53.42dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=123, 总大小=2408字节, 原始长度=3328字节, 压缩率=72.00%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496102491 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0046, 动态范围: 53.91dB, 信噪比估计: 53.91dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=124, 总大小=2451字节, 原始长度=3328字节, 压缩率=73.29%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":12592},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
MicrophoneASR: 跳过完全相同的文本: "你做完，喂喂喂喂。"
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":12696},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
MicrophoneASR: 跳过完全相同的文本: "你做完，喂喂喂喂。"
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":12800},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
MicrophoneASR: 跳过完全相同的文本: "你做完，喂喂喂喂。"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496102612 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0061, 动态范围: 57.62dB, 信噪比估计: 57.62dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=125, 总大小=2507字节, 原始长度=3328字节, 压缩率=74.97%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=3321856, 文件大小=3383296, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 3383296
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496102701 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=3328字节
MicrophoneASR: 准备发送音频数据 - 长度: 3328字节, 采样率: 16000Hz, 格式: PCM16
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: 发送音频数据，序列号=126, 总大小=2492字节, 原始长度=3328字节, 压缩率=74.52%
MicrophoneASR: 消息头详情 - MessageType: AUDIO_ONLY_REQUEST(0x02), SerializationMethod: JSON_FORMAT(0x01), CompressionType: GZIP(0x01)
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496102801 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 发送缓冲的音频数据，大小=3328字节
MicrophoneASR: 准备发送音频数据 - 长度: 3328字节, 采样率: 16000Hz, 格式: PCM16
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
MicrophoneASR: 发送音频数据，序列号=127, 总大小=2408字节, 原始长度=3328字节, 压缩率=72.00%
MicrophoneASR: 消息头详情 - MessageType: AUDIO_ONLY_REQUEST(0x02), SerializationMethod: JSON_FORMAT(0x01), CompressionType: GZIP(0x01)
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":12904},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
MicrophoneASR: 跳过完全相同的文本: "你做完，喂喂喂喂。"
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":13008},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
MicrophoneASR: 跳过完全相同的文本: "你做完，喂喂喂喂。"
MicrophoneASR: Received FULL_SERVER_RESPONSE
MicrophoneASR: Processing transcript response: {"audio_info":{"duration":13112},"result":{"additions":{"log_id":"20250714202805ABAFDF4BE475D8DC508D"},"text":"你做完，喂喂喂喂。","utterances":[{"definite":true,"end_time":8092,"start_time":6512,"text":"你做完，喂喂喂喂。","words":[{"end_time":6592,"start_time":6512,"text":"你"},{"end_time":6752,"start_time":6672,"text":"做完"},{"end_time":7472,"start_time":7392,"text":"喂"},{"end_time":7632,"start_time":7552,"text":"喂"},{"end_time":7792,"start_time":7712,"text":"喂"},{"end_time":8092,"start_time":8012,"text":"喂"}]},{"definite":false,"end_time":-1,"start_time":-1,"text":""}]}}
MicrophoneASR: Processing 2 utterances
MicrophoneASR: 跳过完全相同的文本: "你做完，喂喂喂喂。"
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496102911 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0077, 动态范围: 59.20dB, 信噪比估计: 59.20dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=128, 总大小=2580字节, 原始长度=3328字节, 压缩率=77.16%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
安全读取: 位置=3383296, 文件大小=3444736, 待读取=61440字节
帧处理: 总数据=61440字节, 完整帧=61440字节, 不完整帧=0字节
完整性检查: 通过 - 数据完整性检查通过
处理音频帧: 输入=61440字节, 缓存完整帧=0字节, 有效帧=61440字节, 新不完整帧=0字节
检测到重复音频块，跳过处理
安全读取并处理 61440 字节音频数据，新位置: 3444736
🎵 MicrophoneManager: 处理麦克风音频数据，长度: 3328
📤 MicrophoneManager: 发送音频数据到MicrophoneASRManager
🎵 MicrophoneASR: 1752496103011 处理麦克风音频数据，长度=3328字节, 采样率=16000Hz
🔗 MicrophoneASR: WebSocket状态: {
  readyState: 1,
  isConnected: true,
  sessionStarted: true,
  readyToSendAudio: true
}
MicrophoneASR: 音频质量分析 - RMS: 0.0041, 动态范围: 54.87dB, 信噪比估计: 54.87dB, 有效信号: true
MicrophoneASR: 发送音频数据，序列号=129, 总大小=2418字节, 原始长度=3328字节, 压缩率=72.30%
✅ MicrophoneManager: 音频数据已发送到MicrophoneASRManager
🖱️ 检测到鼠标点击事件: { x: 468, y: 78, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 468, y: 78 },
  windowBounds: { x: 0, y: 0, width: 1792, height: 751 },
  toolbarBounds: { x: 101, y: 1, width: 1590, height: 749 },
  regionsCount: 5
}
使用渲染进程报告的工具条位置: { x: 101, y: 1, width: 1590, height: 749 }
鼠标位置 (468, 78) 在工具条范围内
点击在 voice-one-click-start 区域内
语音面板一键启动按钮被点击
🛑 收到停止语音识别流程请求: 系统音频=true, 麦克风=true
🔇 停止系统音频服务...
停止系统音频捕获
SystemASR: Stopping ASR session
开始终止系统音频捕获进程...
正在终止音频捕获进程 PID: 84474
✅ [2025-07-14T12:28:23.093Z] 进程操作: terminate_start (PID: 84474) - Starting termination process
第一步：尝试优雅终止进程...
✅ [2025-07-14T12:28:23.093Z] 进程操作: graceful_terminate (PID: 84474) - Sending SIGTERM
IPC: 收到停止麦克风音频捕获请求
🛑 MicrophoneManager: 停止麦克风音频捕获
🔇 MicrophoneManager: 通知MicrophoneASRManager停止处理音频
MicrophoneASR: Stopping ASR session
MicrophoneASR: Stopping session
📡 MicrophoneManager: 发送停止录制状态到渲染进程
✅ MicrophoneManager: 已通知渲染进程录制停止
SystemAudioManager: 音频捕获进程退出，代码: null, 信号: SIGTERM
✅ 进程已优雅退出
✅ [2025-07-14T12:28:23.099Z] 进程操作: graceful_exit (PID: 84474) - Process exited gracefully
✅ [2025-07-14T12:28:23.099Z] 进程操作: cleanup_complete (PID: 84474) - Process reference cleared
已删除系统音频输出文件
连接状态更新: systemAudio = false {
  systemASR: true,
  microphoneASR: true,
  systemAudio: false,
  microphone: true
}
✅ 系统音频捕获已停止
SystemASR: Stopping ASR session
连接状态更新: systemASR = false {
  systemASR: false,
  microphoneASR: true,
  systemAudio: false,
  microphone: true
}
✅ 系统音频ASR连接已关闭
🎤 停止麦克风服务...
🛑 MicrophoneManager: 停止麦克风音频捕获
⚠️ MicrophoneManager: 麦克风未在录制中
连接状态更新: microphone = false {
  systemASR: false,
  microphoneASR: true,
  systemAudio: false,
  microphone: false
}
✅ 麦克风捕获已停止
MicrophoneASR: Stopping ASR session
MicrophoneASR: Stopping session
连接状态更新: microphoneASR = false {
  systemASR: false,
  microphoneASR: false,
  systemAudio: false,
  microphone: false
}
✅ 麦克风ASR连接已关闭
📊 停止后连接状态: {
  systemASR: false,
  microphoneASR: false,
  systemAudio: false,
  microphone: false
}
音频文件监控已停止，缓存已清理
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
工具条位置无实质性变化，忽略更新
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'voice-back', x: 1530, y: 12, width: 48, height: 24 },
    { type: 'voice-microphone', x: 313, y: 59, width: 28, height: 28 },
    {
      type: 'voice-system-audio',
      x: 277,
      y: 59,
      width: 28,
      height: 28
    },
    {
      type: 'voice-one-click-start',
      x: 349,
      y: 55,
      width: 36,
      height: 36
    },
    { type: 'voice-send-to-ai', x: 8, y: 705, width: 381, height: 36 }
  ]
}
🎤 语音面板按钮区域: [
  { x: 1530, y: 12, width: 48, height: 24, type: 'voice-back' },
  { x: 313, y: 59, width: 28, height: 28, type: 'voice-microphone' },
  { x: 277, y: 59, width: 28, height: 28, type: 'voice-system-audio' },
  {
    x: 349,
    y: 55,
    width: 36,
    height: 36,
    type: 'voice-one-click-start'
  },
  { x: 8, y: 705, width: 381, height: 36, type: 'voice-send-to-ai' }
]
🖱️ 检测到鼠标点击事件: { x: 1651, y: 26, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 1651, y: 26 },
  windowBounds: { x: 0, y: 0, width: 1792, height: 751 },
  toolbarBounds: { x: 101, y: 1, width: 1590, height: 749 },
  regionsCount: 5
}
使用渲染进程报告的工具条位置: { x: 101, y: 1, width: 1590, height: 749 }
鼠标位置 (1651, 26) 在工具条范围内
点击在 voice-back 区域内
语音面板返回按钮被点击
🖱️ 鼠标滚轮监听已停止
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3',
      'deepseek-r1',
      'gemini-2.5',
      'claude',
      'grok-4'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
shouldShowMicrophoneButton: {
  success: true,
  message: 'init success',
  data: {
    models: [
      'doubao-pro',
      'doubao-thinking',
      'deepseek-v3',
      'deepseek-r1',
      'gemini-2.5',
      'claude',
      'grok-4'
    ],
    voiceConfig: {
      voiceEnabled: true,
      voiceAppId: '1957035768',
      voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
    }
  }
}
config status: true {
  voiceEnabled: true,
  voiceAppId: '1957035768',
  voiceAccessKeyId: 'gwz3RV-EsVU6gaTqM7rS4JVdpoqcIrte'
}
工具条位置已更新: { x: 16, y: 12, width: 454, height: 49 }
📍 工具条区域已更新: {
  regionsCount: 4,
  regions: [
    { type: 'model', x: 261, y: 24, width: 80, height: 10 },
    { type: 'reset', x: 120, y: 19, width: 83, height: 19 },
    { type: 'capture', x: 21, y: 21, width: 66, height: 16 },
    { type: 'solution', x: 365, y: 23, width: 58, height: 11 }
  ]
}
工具条位置更新被节流
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'model', x: 261, y: 24, width: 57, height: 10 },
    { type: 'reset', x: 120, y: 19, width: 83, height: 19 },
    { type: 'capture', x: 21, y: 21, width: 66, height: 16 },
    { type: 'solution', x: 343, y: 23, width: 58, height: 11 },
    { type: 'voice', x: 427, y: 21, width: 46, height: 16 }
  ]
}
工具条位置已更新: { x: 16, y: 12, width: 483, height: 49 }
📍 工具条区域已更新: {
  regionsCount: 5,
  regions: [
    { type: 'model', x: 261, y: 24, width: 57, height: 10 },
    { type: 'reset', x: 120, y: 19, width: 83, height: 19 },
    { type: 'capture', x: 21, y: 21, width: 66, height: 16 },
    { type: 'solution', x: 343, y: 23, width: 58, height: 11 },
    { type: 'voice', x: 427, y: 21, width: 46, height: 16 }
  ]
}
🖱️ 检测到鼠标点击事件: { x: 1206, y: 1113, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 1206, y: 1113 },
  windowBounds: { x: 0, y: 0, width: 531, height: 73 },
  toolbarBounds: { x: 16, y: 12, width: 483, height: 49 },
  regionsCount: 5
}
使用渲染进程报告的工具条位置: { x: 16, y: 12, width: 483, height: 49 }
鼠标位置 (1206, 1113) 不在工具条范围内
🖱️ 检测到鼠标点击事件: { x: 1196, y: 1147, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 1196, y: 1147 },
  windowBounds: { x: 0, y: 0, width: 531, height: 73 },
  toolbarBounds: { x: 16, y: 12, width: 483, height: 49 },
  regionsCount: 5
}
使用渲染进程报告的工具条位置: { x: 16, y: 12, width: 483, height: 49 }
鼠标位置 (1196, 1147) 不在工具条范围内
🖱️ 检测到鼠标点击事件: { x: 1189, y: 1146, button: 1, clicks: 1 }
🖱️ 鼠标点击检测: {
  mousePosition: { x: 1189, y: 1146 },
  windowBounds: { x: 0, y: 0, width: 531, height: 73 },
  toolbarBounds: { x: 16, y: 12, width: 483, height: 49 },
  regionsCount: 5
}
使用渲染进程报告的工具条位置: { x: 16, y: 12, width: 483, height: 49 }
鼠标位置 (1189, 1146) 不在工具条范围内
