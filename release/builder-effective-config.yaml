directories:
  output: release
  buildResources: assets
appId: interview.coder.id
productName: SecureKernel
files:
  - filter:
      - dist/**/*
      - dist-electron/**/*
      - package.json
      - '!dist/**/*.map'
      - '!dist-electron/**/*.map'
asar: true
compression: maximum
removePackageScripts: true
electronCompile: false
electronLanguages:
  - en
  - zh-CN
asarUnpack:
  - node_modules/@mhgbrown/iohook/**/*
afterPack: ./scripts/afterPack.js
extraResources:
  - from: assets/icons
    to: icons
    filter:
      - '**/*.png'
      - '**/*.icns'
      - '**/*.ico'
  - from: bin
    to: bin
    filter:
      - '**/*'
mac:
  category: public.app-category.developer-tools
  target:
    - target: dmg
      arch:
        - arm64
        - x64
  icon: assets/icons/mac/icon.icns
  hardenedRuntime: true
  gatekeeperAssess: false
  identity: null
  entitlements: build/entitlements.mac.plist
  entitlementsInherit: build/entitlements.mac.plist
  artifactName: ${productName}-${version}-${arch}.${ext}
  darkModeSupport: true
  minimumSystemVersion: 10.15.0
dmg:
  sign: false
  contents:
    - x: 130
      'y': 220
    - x: 410
      'y': 220
      type: link
      path: /Applications
  window:
    width: 540
    height: 380
  internetEnabled: false
win:
  target:
    - target: nsis
      arch:
        - x64
  icon: assets/icons/win/icon.png
linux:
  target:
    - AppImage
  icon: assets/icons/png/icon_256x256.png
publish:
  provider: github
  owner: zhaoq
  repo: secure-kernel
npmRebuild: false
fileAssociations: []
protocols: []
extraMetadata:
  main: ./dist-electron/main.js
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
electronVersion: 29.4.6
