# ASR 转录逻辑示例

## 新的处理逻辑说明

### 核心算法
```typescript
// 比较前20个字符判断是否为连续转录
const currentPrefix = this.currentTranscription.substring(0, 20);
const newPrefix = trimmedText.substring(0, 20);
const isContinuousTranscription = currentPrefix === newPrefix && currentPrefix.length > 0;
```

### 处理流程示例

#### 输入数据序列：
1. `text: "你好"`
2. `text: "你好，我叫tom"`  
3. `text: "你好，我叫tom，请问你是？"`
4. `text: "能听见吗？"`
5. `text: "能听见吗？给个回复"`

#### 处理过程：

**第1条数据：** `"你好"`
- currentPrefix: `""` (空)
- newPrefix: `"你好"`
- isContinuousTranscription: `false` (currentPrefix为空)
- 动作：开始新转录，发送 `{text: "你好", isFinal: false}`

**第2条数据：** `"你好，我叫tom"`
- currentPrefix: `"你好"`
- newPrefix: `"你好，我叫tom"`（前20字符：`"你好，我叫tom"`）
- isContinuousTranscription: `true` (前20字符匹配)
- 动作：继续转录，发送 `{text: "你好，我叫tom", isFinal: false}`

**第3条数据：** `"你好，我叫tom，请问你是？"`
- currentPrefix: `"你好，我叫tom"`
- newPrefix: `"你好，我叫tom，请问你是？"`（前20字符：`"你好，我叫tom，请问你是？"`）
- isContinuousTranscription: `true` (前20字符匹配)
- 动作：继续转录，发送 `{text: "你好，我叫tom，请问你是？", isFinal: false}`

**第4条数据：** `"能听见吗？"`
- currentPrefix: `"你好，我叫tom，请问你是？"`（前20字符：`"你好，我叫tom，请问你是？"`）
- newPrefix: `"能听见吗？"`
- isContinuousTranscription: `false` (前20字符不匹配)
- 动作：
  1. 将之前内容加入历史：发送 `{text: "你好，我叫tom，请问你是？", isFinal: true}`
  2. 开始新转录：发送 `{text: "能听见吗？", isFinal: false}`

**第5条数据：** `"能听见吗？给个回复"`
- currentPrefix: `"能听见吗？"`
- newPrefix: `"能听见吗？给个回复"`（前20字符：`"能听见吗？给个回复"`）
- isContinuousTranscription: `true` (前20字符匹配)
- 动作：继续转录，发送 `{text: "能听见吗？给个回复", isFinal: false}`

### 最终结果

**历史记录：**
- "你好，我叫tom，请问你是？"

**当前转录：**
- "能听见吗？给个回复"

### 优势

1. **不依赖服务端标志**：不需要依赖 `isFinal` 等服务端提供的标志
2. **智能判断**：通过内容相似性自动判断是否为连续转录
3. **实时响应**：连续转录时实时更新，新内容时及时归档历史
4. **简单可靠**：逻辑简单，容易理解和维护
